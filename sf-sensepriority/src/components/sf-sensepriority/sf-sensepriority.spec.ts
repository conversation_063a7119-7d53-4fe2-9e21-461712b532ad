import { newSpecPage } from '@stencil/core/testing';
import { SfSensepriority } from './sf-sensepriority';

describe('sf-sensepriority', () => {
  it('renders with error when no survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensepriority],
      html: '<sf-sensepriority></sf-sensepriority>',
    });
    expect(root).toEqualHtml(`
      <sf-sensepriority>
        <mock:shadow-root>
          <p part="message error-message">Please provide a valid survey key</p>
        </mock:shadow-root>
      </sf-sensepriority>
    `);
  });

  it('renders loading state when survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensepriority],
      html: '<sf-sensepriority survey-key="test-key"></sf-sensepriority>',
    });
    expect(root).toEqualHtml(`
      <sf-sensepriority survey-key="test-key">
        <mock:shadow-root>
          <p part="message loading-message">Loading survey...</p>
        </mock:shadow-root>
      </sf-sensepriority>
    `);
  });
});
