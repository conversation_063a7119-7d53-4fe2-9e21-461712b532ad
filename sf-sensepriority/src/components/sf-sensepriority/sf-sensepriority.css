:host {
  display: block;
}

.dragging {
  opacity: 0.5;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for accessibility */
[role='listitem']:focus {
  outline: 2px solid #005fcc;
  outline-offset: 2px;
}

.focused {
  background-color: rgba(0, 95, 204, 0.1);
}

.drag-mode {
  background-color: rgba(0, 95, 204, 0.2);
  border: 2px dashed #005fcc;
}

/* Button focus indicators */
button:focus {
  outline: 2px solid #005fcc;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [role='listitem']:focus,
  button:focus {
    outline: 3px solid;
  }

  .focused {
    background-color: highlight;
    color: highlighttext;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
