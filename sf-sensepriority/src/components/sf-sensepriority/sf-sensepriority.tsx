import { Component, Host, h, Prop, State, Element, Listen } from '@stencil/core';
import { isV<PERSON>d<PERSON><PERSON>, shuffleArray, moveArrayItem, getFieldConfig, addToCommaSeparatedList, removeFromCommaSeparatedList } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/sensePriority';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/sensePriority';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  PRIORITY_RANKING = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface PriorityItem {
  title: string;
  description?: string;
  value: string;
}

interface RespondentDetail {
  label: string;
  value: string;
  inputType: string; // 'text', 'email', 'dropdown', 'radio', 'checkbox', 'number'
  required?: boolean;
  placeholder?: string;
  options?: Array<{
    value: string;
    label: string;
  }>;
  defaultValue?: any;
}

interface SurveyConfig {
  question: string;
  items: PriorityItem[];
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

interface RankedItem extends PriorityItem {
  rank: number;
}

@Component({
  tag: 'sf-sensepriority',
  styleUrl: 'sf-sensepriority.css',
  shadow: true,
})
export class SfSensepriority {
  @Element() el: HTMLElement;

  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.PRIORITY_RANKING;

  @State() rankedItems: RankedItem[] = [];

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  @State() draggedIndex: number | null = null;

  @State() focusedItemIndex: number = -1;

  @State() announceMessage: string = '';

  @State() isDragMode: boolean = false;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);
      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      this.config = data.payload.config;
      this.respondentDetails = data.payload.respondentDetails || [];

      // Initialize ranked items with shuffled order
      if (this.config?.items) {
        const shuffledItems = shuffleArray(this.config.items);
        this.rankedItems = shuffledItems.map((item, index) => ({
          ...item,
          rank: index + 1,
        }));
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || this.rankedItems.length === 0) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          rankings: this.rankedItems.map(item => ({
            value: item.value,
            title: item.title,
            rank: item.rank,
          })),
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'sensePriority',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    // Use modern navigator.userAgentData when available, fallback to userAgent parsing
    const getPlatform = () => {
      if ('userAgentData' in navigator && (navigator as any).userAgentData?.platform) {
        return (navigator as any).userAgentData.platform;
      }
      // Fallback: extract platform info from userAgent
      const ua = navigator.userAgent;
      if (ua.includes('Win')) return 'Windows';
      if (ua.includes('Mac')) return 'macOS';
      if (ua.includes('Linux')) return 'Linux';
      if (ua.includes('Android')) return 'Android';
      if (ua.includes('iOS')) return 'iOS';
      return 'Unknown';
    };

    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: getPlatform(),
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.PRIORITY_RANKING) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.PRIORITY_RANKING;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  @Listen('keydown')
  handleKeyDown(event: KeyboardEvent) {
    if (this.currentStep === SurveyStep.PRIORITY_RANKING) {
      this.handleRankingKeydown(event);
    }
  }

  private handleRankingKeydown(event: KeyboardEvent) {
    if (this.rankedItems.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.focusedItemIndex = Math.min(this.focusedItemIndex + 1, this.rankedItems.length - 1);
        this.focusCurrentItem();
        break;

      case 'ArrowUp':
        event.preventDefault();
        this.focusedItemIndex = Math.max(this.focusedItemIndex - 1, 0);
        this.focusCurrentItem();
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        this.isDragMode = !this.isDragMode;
        if (this.isDragMode) {
          this.announceToScreenReader(`Grabbed ${this.rankedItems[this.focusedItemIndex]?.title}. Use arrow keys to move, press Enter or Space to drop.`);
        } else {
          this.announceToScreenReader(`Dropped ${this.rankedItems[this.focusedItemIndex]?.title} at position ${this.focusedItemIndex + 1}.`);
        }
        break;

      case 'ArrowLeft':
      case 'ArrowRight':
        if (this.isDragMode) {
          event.preventDefault();
          const direction = event.key === 'ArrowLeft' ? -1 : 1;
          this.moveItemKeyboard(direction);
        }
        break;

      case 'Escape':
        if (this.isDragMode) {
          event.preventDefault();
          this.isDragMode = false;
          this.announceToScreenReader('Move cancelled. Item returned to original position.');
        }
        break;
    }
  }

  private moveItemKeyboard(direction: number) {
    if (this.focusedItemIndex < 0 || this.focusedItemIndex >= this.rankedItems.length) return;

    const newIndex = this.focusedItemIndex + direction;
    if (newIndex >= 0 && newIndex < this.rankedItems.length) {
      this.rankedItems = moveArrayItem(this.rankedItems, this.focusedItemIndex, newIndex);
      this.focusedItemIndex = newIndex;
      this.updateRanks();
      this.announceToScreenReader(`Moved to position ${newIndex + 1}`);
    }
  }

  private updateRanks() {
    this.rankedItems = this.rankedItems.map((item, index) => ({
      ...item,
      rank: index + 1,
    }));
  }

  private focusCurrentItem() {
    const itemElements = this.el.shadowRoot?.querySelectorAll('[data-item-index]');
    if (itemElements && this.focusedItemIndex >= 0 && this.focusedItemIndex < itemElements.length) {
      (itemElements[this.focusedItemIndex] as HTMLElement).focus();
    }
  }

  private announceToScreenReader(message: string) {
    this.announceMessage = message;
    setTimeout(() => {
      this.announceMessage = '';
    }, 1000);
  }

  private isValidEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  private validateRespondentField(detail: RespondentDetail, value: string): string | null {
    if (detail.required !== false && (!value || value.trim().length === 0)) {
      return 'This field is required';
    }

    if (value && value.trim().length > 0) {
      const config = getFieldConfig(detail, value);
      if (config.inputType === 'email') {
        if (!this.isValidEmail(value)) {
          return 'Please enter a valid email address';
        }
      }
    }

    return null;
  }

  private handleRespondentDetailChange(key: string, event: Event) {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    let value: string;

    if (target.type === 'checkbox') {
      // Handle checkbox inputs using browser-compatible helpers
      const currentValue = this.userRespondentDetails[key] || '';
      if (target.checked) {
        value = addToCommaSeparatedList(currentValue, target.value);
      } else {
        value = removeFromCommaSeparatedList(currentValue, target.value);
      }
    } else {
      // Handle all other input types (text, email, number, radio, select)
      value = target.value;
    }

    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private createInputHandler(fieldValue: string) {
    const self = this;
    return function (e: Event) {
      self.handleRespondentDetailChange(fieldValue, e);
    };
  }

  private isValueInArray(array: string[], value: string): boolean {
    for (let i = 0; i < array.length; i++) {
      if (array[i] === value) {
        return true;
      }
    }
    return false;
  }

  private renderField(detail: RespondentDetail) {
    const config = getFieldConfig(detail, this.userRespondentDetails[detail.value] || '');
    const inputHandler = this.createInputHandler(detail.value);

    switch (config.inputType) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <input part="input form-input" type={config.inputType} value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />
        );

      case 'dropdown':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <select part="select form-select" onChange={inputHandler} required={config.required}>
            {!config.defaultValue && (
              <option value="" disabled>
                {config.placeholder}
              </option>
            )}
            {config.options.map(function (option) {
              return (
                <option key={option.value} value={option.value} selected={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}>
                  {option.label}
                </option>
              );
            })}
          </select>
        );

      case 'radio':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <div part="radio-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="radio-option">
                  <input
                    part="radio-input"
                    type="radio"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}
                    onChange={inputHandler}
                    required={config.required}
                  />
                  <label part="radio-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      case 'checkbox':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        const self = this;
        return (
          <div part="checkbox-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="checkbox-option">
                  <input
                    part="checkbox-input"
                    type="checkbox"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={self.isValueInArray(config.selectedValues, option.value)}
                    onChange={inputHandler}
                  />
                  <label part="checkbox-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      default:
        return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
    }
  }

  private isRespondentDetailsValid(): boolean {
    return this.respondentDetails.every(detail => {
      const value = this.userRespondentDetails[detail.value] || '';
      const error = this.validateRespondentField(detail, value);
      return error === null;
    });
  }

  private moveItem(fromIndex: number, toIndex: number) {
    if (fromIndex === toIndex) return;

    const newRankedItems = moveArrayItem(this.rankedItems, fromIndex, toIndex);

    // Update ranks based on new positions
    this.rankedItems = newRankedItems.map((item, index) => ({
      ...item,
      rank: index + 1,
    }));
  }

  private moveItemUp(index: number) {
    if (index > 0) {
      const item = this.rankedItems[index];
      this.moveItem(index, index - 1);
      this.announceToScreenReader(`Moved ${item.title} up to rank ${index}`);
    }
  }

  private moveItemDown(index: number) {
    if (index < this.rankedItems.length - 1) {
      const item = this.rankedItems[index];
      this.moveItem(index, index + 1);
      this.announceToScreenReader(`Moved ${item.title} down to rank ${index + 2}`);
    }
  }

  private handleDragStart(event: DragEvent, index: number) {
    this.draggedIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', '');
    }
  }

  private handleDragOver(event: DragEvent) {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  private handleDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    if (this.draggedIndex !== null && this.draggedIndex !== dropIndex) {
      this.moveItem(this.draggedIndex, dropIndex);
    }
    this.draggedIndex = null;
  }

  private handleDragEnd() {
    this.draggedIndex = null;
  }

  private renderPriorityRankingStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step priority-ranking-step">
        <h2 part="heading priority-heading">{this.config?.question || 'Rank these items by priority'}</h2>
        <p part="instructions" id="ranking-instructions">
          Drag and drop items to reorder them, or use the arrow buttons. Rank 1 is highest priority. Keyboard users: Use arrow keys to navigate, Enter or Space to grab/drop items.
        </p>

        <div part="ranking-container" role="list" aria-labelledby="ranking-instructions">
          {this.rankedItems.map((item, index) => (
            <div
              part="ranking-item"
              class={{
                'dragging': this.draggedIndex === index,
                'focused': this.focusedItemIndex === index,
                'drag-mode': this.isDragMode && this.focusedItemIndex === index,
              }}
              role="listitem"
              aria-label={`${item.title}, rank ${item.rank} of ${this.rankedItems.length}`}
              aria-describedby={item.description ? `item-desc-${index}` : undefined}
              aria-grabbed={this.isDragMode && this.focusedItemIndex === index}
              tabindex={index === this.focusedItemIndex ? 0 : -1}
              data-item-index={index}
              draggable={true}
              onDragStart={e => this.handleDragStart(e, index)}
              onDragOver={e => this.handleDragOver(e)}
              onDrop={e => this.handleDrop(e, index)}
              onDragEnd={() => this.handleDragEnd()}
              onFocus={() => (this.focusedItemIndex = index)}
            >
              <div part="rank-number" aria-label={`Rank ${item.rank}`}>
                {item.rank}
              </div>
              <div part="item-content">
                <div part="item-title" id={`item-title-${index}`}>
                  {item.title}
                </div>
                {item.description && (
                  <div part="item-description" id={`item-desc-${index}`}>
                    {item.description}
                  </div>
                )}
              </div>
              <div part="item-controls" role="group" aria-label="Reorder controls">
                <button part="button control-button up-button" onClick={() => this.moveItemUp(index)} disabled={index === 0} aria-label={`Move ${item.title} up`} title="Move up">
                  ↑
                </button>
                <button
                  part="button control-button down-button"
                  onClick={() => this.moveItemDown(index)}
                  disabled={index === this.rankedItems.length - 1}
                  aria-label={`Move ${item.title} down`}
                  title="Move down"
                >
                  ↓
                </button>
              </div>
            </div>
          ))}
        </div>

        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              {this.renderField(detail)}
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        <div part="ranking-summary">
          <h3 part="summary-heading">Your Priority Ranking:</h3>
          <div part="summary-list">
            {this.rankedItems.map(item => (
              <div part="summary-item">
                <span part="summary-rank">#{item.rank}</span>
                <span part="summary-title">{item.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.PRIORITY_RANKING]: this.renderPriorityRankingStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.PRIORITY_RANKING];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container" role="main" aria-label="Priority Ranking Survey">
          {this.renderCurrentStep()}
          {/* ARIA live region for screen reader announcements */}
          <div aria-live="polite" aria-atomic="true" class="sr-only" part="announcements">
            {this.announceMessage}
          </div>
        </div>
      </Host>
    );
  }
}
