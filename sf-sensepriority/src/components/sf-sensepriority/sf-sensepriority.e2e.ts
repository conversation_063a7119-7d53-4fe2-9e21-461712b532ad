import { newE2EPage } from '@stencil/core/testing';

describe('sf-sensepriority', () => {
  it('renders', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepriority></sf-sensepriority>');
    const element = await page.find('sf-sensepriority');
    expect(element).toHaveClass('hydrated');
  });

  it('shows error message when no survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepriority></sf-sensepriority>');
    const element = await page.find('sf-sensepriority >>> p[part="message error-message"]');
    expect(element.textContent).toEqual('Please provide a valid survey key');
  });

  it('shows loading message when survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepriority survey-key="test-key"></sf-sensepriority>');
    const element = await page.find('sf-sensepriority >>> p[part="message loading-message"]');
    expect(element.textContent).toEqual('Loading survey...');
  });
});
