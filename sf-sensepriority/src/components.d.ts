/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface SfSensepriority {
        "surveyKey": string;
    }
}
declare global {
    interface HTMLSfSensepriorityElement extends Components.SfSensepriority, HTMLStencilElement {
    }
    var HTMLSfSensepriorityElement: {
        prototype: HTMLSfSensepriorityElement;
        new (): HTMLSfSensepriorityElement;
    };
    interface HTMLElementTagNameMap {
        "sf-sensepriority": HTMLSfSensepriorityElement;
    }
}
declare namespace LocalJSX {
    interface SfSensepriority {
        "surveyKey"?: string;
    }
    interface IntrinsicElements {
        "sf-sensepriority": SfSensepriority;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "sf-sensepriority": LocalJSX.SfSensepriority & JSXBase.HTMLAttributes<HTMLSfSensepriorityElement>;
        }
    }
}
