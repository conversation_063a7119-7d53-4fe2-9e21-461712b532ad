import { isValid<PERSON>ey, formatErrorMessage, shuffleArray, moveArrayItem } from './utils';

describe('isValidKey', () => {
  it('returns false for undefined key', () => {
    expect(isValidKey(undefined)).toEqual(false);
  });

  it('returns false for null key', () => {
    expect(isValid<PERSON>ey(null)).toEqual(false);
  });

  it('returns false for empty string', () => {
    expect(isValidKey('')).toEqual(false);
  });

  it('returns false for whitespace string', () => {
    expect(isValidKey('   ')).toEqual(false);
  });

  it('returns true for valid key', () => {
    expect(isValidKey('valid-key')).toEqual(true);
  });
});

describe('formatErrorMessage', () => {
  it('returns string as-is', () => {
    expect(formatErrorMessage('Test error')).toEqual('Test error');
  });

  it('returns error message from Error object', () => {
    const error = new Error('Test error message');
    expect(formatErrorMessage(error)).toEqual('Test error message');
  });

  it('returns default message for unknown error types', () => {
    expect(formatErrorMessage(123)).toEqual('An unknown error occurred');
  });
});

describe('shuffleArray', () => {
  it('returns array with same length', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffleArray(original);
    expect(shuffled.length).toEqual(original.length);
  });

  it('returns array with same elements', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffleArray(original);
    expect(shuffled.sort()).toEqual(original.sort());
  });

  it('does not modify original array', () => {
    const original = [1, 2, 3, 4, 5];
    const originalCopy = [...original];
    shuffleArray(original);
    expect(original).toEqual(originalCopy);
  });
});

describe('moveArrayItem', () => {
  it('moves item from one position to another', () => {
    const original = ['a', 'b', 'c', 'd'];
    const result = moveArrayItem(original, 0, 2);
    expect(result).toEqual(['b', 'c', 'a', 'd']);
  });

  it('moves item to end', () => {
    const original = ['a', 'b', 'c', 'd'];
    const result = moveArrayItem(original, 1, 3);
    expect(result).toEqual(['a', 'c', 'd', 'b']);
  });

  it('does not modify original array', () => {
    const original = ['a', 'b', 'c', 'd'];
    const originalCopy = [...original];
    moveArrayItem(original, 0, 2);
    expect(original).toEqual(originalCopy);
  });
});
