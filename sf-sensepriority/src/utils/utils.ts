/**
 * Utility functions for sf-sensepriority
 */

/**
 * Interface for respondent detail options
 */
interface RespondentDetailOption {
  value: string;
  label: string;
}

/**
 * Interface for respondent detail configuration
 */
interface RespondentDetail {
  label: string;
  value: string;
  inputType: string;
  required?: boolean;
  placeholder?: string;
  options?: RespondentDetailOption[];
  defaultValue?: any;
}

/**
 * Check if a survey key is valid UUID
 * @param key The survey key to validate
 * @returns True if the key is a valid UUID, false otherwise
 */
export function isValidKey(key: string | undefined | null): boolean {
  if (typeof key !== 'string' || key.trim().length === 0) {
    return false;
  }

  // UUID v4 regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(key.trim());
}

/**
 * Format error messages for display
 * @param error The error object or message
 * @returns A formatted error message string
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
}

/**
 * Shuffle an array using Fisher-Yates algorithm
 * @param array The array to shuffle
 * @returns A new shuffled array
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Move an item in an array from one position to another
 * @param array The array to modify
 * @param fromIndex The source index
 * @param toIndex The destination index
 * @returns A new array with the item moved
 */
export function moveArrayItem<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const result = [...array];
  const [removed] = result.splice(fromIndex, 1);
  result.splice(toIndex, 0, removed);
  return result;
}

/**
 * Browser-compatible helper functions
 */
function arrayContains(array: string[], value: string): boolean {
  for (let i = 0; i < array.length; i++) {
    if (array[i] === value) {
      return true;
    }
  }
  return false;
}

function parseCommaSeparatedValues(value: string): string[] {
  if (!value) {
    return [];
  }
  const parts = value.split(',');
  const result = [];
  for (let i = 0; i < parts.length; i++) {
    const trimmed = parts[i].replace(/^\s+|\s+$/g, '');
    if (trimmed) {
      result.push(trimmed);
    }
  }
  return result;
}

/**
 * Get field configuration for rendering
 * @param detail The respondent detail configuration
 * @param value The current value of the field
 * @returns Field configuration object
 */
export function getFieldConfig(detail: RespondentDetail, value: string) {
  const inputType = detail.inputType || 'text';
  const placeholder = detail.placeholder || 'Enter your ' + detail.label.toLowerCase();
  const required = detail.required !== false;

  return {
    inputType,
    placeholder,
    required,
    options: detail.options || [],
    defaultValue: detail.defaultValue,
    hasOptions: detail.options && detail.options.length > 0,
    selectedValues: value ? value.split(',').map(v => v.trim()) : [],
    fieldValue: detail.value,
    currentValue: value || '',
  };
}

export function addToCommaSeparatedList(currentValue: string, valueToAdd: string): string {
  const values = parseCommaSeparatedValues(currentValue);
  if (!arrayContains(values, valueToAdd)) {
    values.push(valueToAdd);
  }
  return values.join(', ');
}

export function removeFromCommaSeparatedList(currentValue: string, valueToRemove: string): string {
  const values = parseCommaSeparatedValues(currentValue);
  const result = [];
  for (let i = 0; i < values.length; i++) {
    if (values[i] !== valueToRemove) {
      result.push(values[i]);
    }
  }
  return result.join(', ');
}
