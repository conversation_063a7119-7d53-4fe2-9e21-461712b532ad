{"name": "sf-sensepriority", "version": "0.0.1", "description": "Stencil Component Starter", "main": "dist/index.cjs.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "unpkg": "dist/sf-sensepriority/sf-sensepriority.esm.js", "exports": {".": {"import": "./dist/sf-sensepriority/sf-sensepriority.esm.js", "require": "./dist/sf-sensepriority/sf-sensepriority.cjs.js"}, "./my-component": {"import": "./dist/components/my-component.js", "types": "./dist/components/my-component.d.ts"}, "./loader": {"import": "./loader/index.js", "require": "./loader/index.cjs", "types": "./loader/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/stenciljs/component-starter.git"}, "files": ["dist/", "loader/"], "scripts": {"build": "stencil build", "start": "stencil build --dev --watch --serve", "test": "stencil test --spec --e2e", "test.watch": "stencil test --spec --e2e --watchAll", "generate": "stencil generate"}, "devDependencies": {"@stencil/core": "^4.27.1", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "jest": "^29.7.0", "jest-cli": "^29.7.0", "puppeteer": "^24.3.0"}, "license": "MIT"}