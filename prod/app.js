"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_session_1 = __importDefault(require("express-session"));
const connect_redis_1 = __importDefault(require("connect-redis"));
const ioredis_1 = __importDefault(require("ioredis"));
const cors_1 = __importDefault(require("cors"));
const helpers_1 = require("./global/helpers");
const var_1 = require("./global/var");
const middlewares_1 = require("./global/middlewares");
const app = (0, express_1.default)();
const redisStore = (0, connect_redis_1.default)(express_session_1.default);
let corsOrigin = var_1.Var.node.env === "dev" ? var_1.Var.app.url.dev : var_1.Var.app.url.prod;
app.use((0, cors_1.default)({
    origin: [corsOrigin],
    credentials: true,
}));
app.use(express_1.default.json());
if (var_1.Var.node.env === "prod") {
    app.set("trust proxy", 1);
}
const client = new ioredis_1.default({
    host: var_1.Var.redis.host,
    port: +var_1.Var.redis.port,
});
app.use((0, express_session_1.default)({
    secret: var_1.Var.node.express.session.secret,
    name: var_1.Var.node.express.session.name,
    cookie: {
        maxAge: +var_1.Var.node.express.session.maxAge,
        secure: var_1.Var.node.env === "prod" ? true : false,
        sameSite: var_1.Var.node.env === "prod" ? "none" : "lax",
        httpOnly: true,
    },
    resave: false,
    saveUninitialized: false,
    store: new redisStore({ client }),
}));
(0, helpers_1.IncludeRoutes)().then((routeIndexPaths) => {
    routeIndexPaths.forEach((routeIndexPath) => {
        var _a;
        (_a = "./" + routeIndexPath, Promise.resolve().then(() => __importStar(require(_a)))).then((route) => {
            for (const property in route) {
                app.use(route[property]);
            }
        });
    });
});
app.use(middlewares_1.HandleErrors);
exports.default = app;
