{"version": 3, "names": ["NAMESPACE", "BUILD", "allRenderFn", "appendChildSlotFix", "asyncLoading", "asyncQueue", "attachStyles", "cloneNodeFix", "cmpDidLoad", "cmpDidRender", "cmpDidUnload", "cmpDidUpdate", "cmpShouldUpdate", "cmpWillLoad", "cmpWillRender", "cmpWillUpdate", "connectedCallback", "constructableCSS", "cssAnnotations", "devTools", "disconnectedCallback", "element", "event", "experimentalScopedSlotChanges", "experimentalSlotFixes", "formAssociated", "hasRenderFn", "hostListener", "hostListenerTarget", "hostListenerTargetBody", "hostListenerTargetDocument", "hostListenerTargetParent", "hostListenerTargetWindow", "hotModuleReplacement", "hydrateClientSide", "hydrateServerSide", "hydratedAttribute", "hydratedClass", "hydratedSelectorName", "initializeNextTick", "invisiblePrehydration", "isDebug", "isDev", "isTesting", "lazyLoad", "lifecycle", "lifecycleDOMEvents", "member", "method", "mode", "observeAttribute", "profile", "prop", "propBoolean", "propMutable", "propNumber", "propString", "reflect", "scoped", "scopedSlotTextContentFix", "scriptDataOpts", "shadowDelegatesFocus", "shadowDom", "slot", "slotChildNodesFix", "slotRelocation", "state", "style", "svg", "taskQueue", "transformTagName", "updatable", "vdomAttribute", "vdomClass", "vdomFunctional", "vdomKey", "vdomListener", "vdomPropOrAttr", "vdomRef", "v<PERSON><PERSON><PERSON>", "vdomStyle", "vdomText", "vdomXlink", "watchCallback", "__defProp", "Object", "defineProperty", "__export", "target", "all", "name", "get", "enumerable", "hostRefs", "WeakMap", "getHostRef", "ref", "registerInstance", "lazyInstance", "hostRef", "set", "$lazyInstance$", "registerHost", "hostElement", "cmpMeta", "$flags$", "$hostElement$", "$cmpMeta$", "$instanceValues$", "Map", "$onReadyPromise$", "Promise", "r", "$onReadyResolve$", "isMemberInElement", "elm", "memberName", "consoleError", "e", "el", "console", "error", "cmpModules", "loadModule", "hmrVersionId", "exportName", "$tagName$", "replace", "bundleId", "$lazyBundleId$", "module", "import", "then", "importedModule", "styles", "HYDRATED_CSS", "SLOT_FB_CSS", "win", "window", "doc", "document", "head", "plt", "$resourcesUrl$", "jmp", "h2", "raf", "requestAnimationFrame", "ael", "eventName", "listener", "opts", "addEventListener", "rel", "removeEventListener", "ce", "CustomEvent", "supportsListenerOptions", "supportsListenerOptions2", "promiseResolve", "v", "resolve", "supportsConstructableStylesheets", "CSSStyleSheet", "replaceSync", "queuePending", "queueDomReads", "queueDomWrites", "queueTask", "queue", "write", "cb", "push", "nextTick", "flush", "consume", "i2", "length", "performance", "now", "writeTask", "EMPTY_OBJ", "isDef", "isComplexType", "o", "queryNonceMetaTagContent", "doc2", "_a", "_b", "_c", "querySelector", "getAttribute", "result_exports", "err", "map", "ok", "unwrap", "unwrapErr", "value", "isOk", "isErr", "result", "fn", "val", "newVal", "createTime", "fnName", "tagName", "uniqueTime", "key", "measureText", "h", "nodeName", "vnodeData", "children", "child", "simple", "lastSimple", "vNodeChildren", "walk", "c", "Array", "isArray", "String", "$text$", "newVNode", "classData", "className", "class", "keys", "filter", "k", "join", "vdomFnUtils", "vnode", "$attrs$", "$children$", "$key$", "tag", "text", "$tag$", "$elm$", "Host", "isHost", "node", "for<PERSON>ach", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "$name$", "vtag", "vtext", "parsePropertyValue", "propValue", "propType", "parseFloat", "getElement", "createEvent", "flags", "emit", "detail", "emitEvent", "bubbles", "composed", "cancelable", "ev", "dispatchEvent", "rootAppliedStyles", "registerStyle", "scopeId2", "cssText", "allowCS", "addStyle", "styleContainerNode", "getScopeId", "nodeType", "appliedStyles", "styleElm", "Set", "has", "createElement", "innerHTML", "nonce", "$nonce$", "setAttribute", "preconnectLinks", "querySelectorAll", "referenceNode2", "nextS<PERSON>ling", "insertBefore", "stylesheet", "adoptedStyleSheets", "existingStyleContainer", "prepend", "append", "add", "includes", "endAttachStyles", "shadowRoot", "getRootNode", "classList", "cmp", "setAccessor", "oldValue", "newValue", "isSvg", "isProp", "ln", "toLowerCase", "oldClasses", "parseClassList", "newClasses", "remove", "removeProperty", "setProperty", "slice", "capture", "endsWith", "CAPTURE_EVENT_SUFFIX", "CAPTURE_EVENT_REGEX", "isComplex", "n", "__lookupSetter__", "removeAttribute", "parseClassListRegex", "split", "RegExp", "updateElement", "oldVnode", "newVnode", "isSvgMode2", "host", "oldVnodeAttrs", "newVnodeAttrs", "sortedAttrNames", "attrNames", "attr", "scopeId", "hostTagName", "useNativeShadowDom", "isSvgMode", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "parentElm", "newVNode2", "childNode", "createTextNode", "BUILD16", "rootNode", "isElementWithinShadowRoot", "append<PERSON><PERSON><PERSON>", "addVnodes", "before", "parentVNode", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "index", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "isInitialRender", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "parentNode", "leftVNode", "rightVNode", "oldVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "vNode", "parent", "newNode", "reference", "inserted", "renderVdom", "renderFnResults", "isInitialLoad", "hostElm", "$vnode$", "rootVnode", "hasAttribute", "attachToAncestor", "ancestorComponent", "$onRenderResolve$", "scheduleUpdate", "$ancestorComponent$", "dispatch", "dispatchHooks", "endSchedule", "instance", "Error", "<PERSON><PERSON><PERSON><PERSON>", "$queuedListeners$", "methodName", "safeCall", "enqueue", "updateComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catch", "err2", "async", "endUpdate", "rc", "endRender", "callRender", "childrenPromises", "postUpdate", "postUpdateComponent", "renderingRef", "render", "getRenderingRef", "endPostUpdate", "addHydratedFlag", "appDidLoad", "forceUpdate", "isConnected", "who", "documentElement", "namespace", "arg", "BUILD17", "getValue", "propName", "setValue", "oldVal", "$members$", "areBothNaN", "Number", "isNaN", "didValueChange", "$watchers$", "watchMethods", "watchMethodName", "proxyComponent", "Cstr", "prototype", "watchers", "members", "entries", "memberFlags", "this", "configurable", "attrNameToPropName", "attributeChangedCallback", "attrName", "_a2", "hasOwnProperty", "flags2", "entry", "callback<PERSON><PERSON>", "call", "observedAttributes", "from", "_", "m", "initializeComponent", "CstrImport", "endLoad", "$modeName$", "isProxied", "endNewInstance", "constructor", "cmpTag", "localName", "customElements", "whenDefined", "endRegisterStyles", "schedule", "fireConnectedCallback", "endConnected", "addHostEventListeners", "$listeners$", "disconnectInstance", "$rmListeners$", "rmListener", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "customElements2", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "appLoadFallback", "isBootstrapping", "assign", "URL", "resourcesUrl", "baseURI", "href", "hasSlotRelocation", "lazyBundle", "compactMeta", "HostElement", "HTMLElement", "self", "super", "hasRegisteredEventListeners", "attachShadow", "clearTimeout", "componentOnReady", "define", "sort", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "listeners", "attachParentListeners", "handler", "hostListenerProxy", "hostListenerOpts", "passive", "setNonce"], "sources": ["@stencil/core/internal/app-data", "node_modules/@stencil/core/internal/client/index.js?app-data=conditional"], "sourcesContent": ["export const NAMESPACE = 'app';\nexport const BUILD = /* app */ { allRenderFn: true, appendChildSlotFix: false, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: false, cmpDidLoad: true, cmpDidRender: false, cmpDidUnload: false, cmpDidUpdate: false, cmpShouldUpdate: false, cmpWillLoad: true, cmpWillRender: false, cmpWillUpdate: false, connectedCallback: false, constructableCSS: true, cssAnnotations: true, devTools: false, disconnectedCallback: false, element: false, event: true, experimentalScopedSlotChanges: false, experimentalSlotFixes: false, formAssociated: false, hasRenderFn: true, hostListener: true, hostListenerTarget: false, hostListenerTargetBody: false, hostListenerTargetDocument: false, hostListenerTargetParent: false, hostListenerTargetWindow: false, hotModuleReplacement: false, hydrateClientSide: false, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, hydratedSelectorName: \"hydrated\", initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: false, mode: false, observeAttribute: true, profile: false, prop: true, propBoolean: true, propMutable: false, propNumber: true, propString: true, reflect: false, scoped: false, scopedSlotTextContentFix: false, scriptDataOpts: false, shadowDelegatesFocus: false, shadowDom: true, slot: true, slotChildNodesFix: false, slotRelocation: false, state: true, style: true, svg: false, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: false, watchCallback: true };\nexport const Env = /* app */ {};\n", "/*\n Stencil Client Platform v4.22.2 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\nvar hostRefs = BUILD2.hotModuleReplacement ? window.__STENCIL_HOSTREFS__ || (window.__STENCIL_HOSTREFS__ = /* @__PURE__ */ new WeakMap()) : /* @__PURE__ */ new WeakMap();\nvar getHostRef = (ref) => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  if (BUILD2.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD2.method && BUILD2.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD2.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD3.isTesting ? [\"STENCIL:\"] : [\n  \"%cstencil\",\n  \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"\n];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = (handler) => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD4.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(\n      `Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`\n    );\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD4.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${BUILD4.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`\n  ).then((importedModule) => {\n    if (!BUILD4.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar STENCIL_DOC_DATA = \"_stencilDocData\";\nvar DEFAULT_DOC_DATA = {\n  hostIds: 0,\n  rootLevelIds: 0,\n  staticComponents: /* @__PURE__ */ new Set()\n};\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\n  \"formAssociatedCallback\",\n  \"formResetCallback\",\n  \"formDisabledCallback\",\n  \"formStateRestoreCallback\"\n];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || { head: {} };\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = (helpers) => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD5.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD5.constructableCSS ? /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD6.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD6.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD27, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = (path) => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD21 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD7.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD7.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = (ref) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD7.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = (ref) => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD8.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD8.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD8.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD8.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD8.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD8.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD8.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD8.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = (inputElm) => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = BUILD9.shadowDom && shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map((c) => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (BUILD9.shadowDom && shadowRoot) {\n    shadowRootNodes.map((shadowRootNode) => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId\n        );\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        node.childNodes[i2],\n        hostId\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD9.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD10.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD10.propNumber && propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (BUILD10.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD17, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar getElement = (ref) => BUILD11.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      if (BUILD12.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD13.attachStyles) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD13.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD13.hydrateServerSide || BUILD13.hotModuleReplacement) && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(styleElm, referenceNode2);\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\") {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD13.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    BUILD13.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if ((BUILD13.shadowDom || BUILD13.scoped) && BUILD13.cssAnnotations && flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (BUILD13.scoped && flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD13.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (BUILD14.vdomClass && memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    } else if (BUILD14.vdomStyle && memberName === \"style\") {\n      if (BUILD14.updatable) {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (BUILD14.vdomKey && memberName === \"key\") {\n    } else if (BUILD14.vdomRef && memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (BUILD14.vdomListener && (BUILD14.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else if (BUILD14.vdomPropOrAttr) {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n                elm[memberName] = n;\n              } else {\n                elm.setAttribute(memberName, n);\n              }\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {\n        }\n      }\n      let xlink = false;\n      if (BUILD14.vdomXlink) {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (BUILD14.vdomXlink && xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (BUILD14.vdomXlink && xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  if (BUILD15.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD16.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (BUILD16.isDev && newVNode2.$elm$) {\n    consoleDevError(\n      `The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`\n    );\n  }\n  if (BUILD16.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (BUILD16.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD16.isDebug || BUILD16.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : doc.createTextNode(\"\");\n  } else {\n    if (BUILD16.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = BUILD16.svg ? doc.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) : doc.createElement(\n      !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    );\n    if (BUILD16.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD16.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD16.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (BUILD16.scoped) {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD16.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD16.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD16.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find((ref) => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD16.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD16.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD16.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD16.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD16.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD16.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD16.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD16.slotRelocation) {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (BUILD16.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD16.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      if (\n        // The component gets hydrated and no VDOM has been initialized.\n        // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n        \"$nodeId$\" in leftVNode && isInitialRender && // `leftNode` is not from type HTMLComment which would cause many\n        // hydration comments to be removed\n        leftVNode.$elm$.nodeType !== 8\n      ) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD16.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = (node) => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD16.vdomText || text === null) {\n    if (BUILD16.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD16.vdomAttribute || BUILD16.reflect) {\n      if (BUILD16.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD16.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (BUILD16.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD16.updatable && BUILD16.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD16.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD16.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD16.vdomText && BUILD16.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD16.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD16.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = (vNode) => {\n  if (BUILD16.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  if (BUILD16.scoped) {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = (element) => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(\n      ...element[\"s-scs\"] || [],\n      element[\"s-si\"],\n      element[\"s-sc\"],\n      ...findScopeIds(element.parentElement)\n    );\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...element[\"s-scs\"] = [...scopeIds]);\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD16.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD16.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD16.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD16.scoped || BUILD16.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  if (BUILD16.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD16.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = BUILD16.isDebug || BUILD16.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD16.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD16.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD16.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = (slotVNode) => doc.createComment(\n  `<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`\n);\nvar originalLocationDebugNode = (nodeToRelocate) => doc.createComment(\n  `org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`)\n);\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD17.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise((r) => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD17.taskQueue && BUILD17.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD17.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD17.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD17.lazyLoad && BUILD17.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    if (BUILD17.cmpWillLoad) {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    if (BUILD17.cmpWillUpdate) {\n      maybePromise = safeCall(instance, \"componentWillUpdate\");\n    }\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  if (BUILD17.cmpWillRender) {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD17.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD17.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD17.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD17.asyncLoading && rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD17.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD17.allRenderFn ? true : false;\n  const lazyLoad = BUILD17.lazyLoad ? true : false;\n  const taskQueue = BUILD17.taskQueue ? true : false;\n  const updatable = BUILD17.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD17.hasRenderFn || BUILD17.reflect) {\n      if (BUILD17.vdomRender || BUILD17.reflect) {\n        if (BUILD17.hydrateServerSide) {\n          return Promise.resolve(instance).then((value) => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD17.cmpDidRender) {\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidRender\");\n    if (BUILD17.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD17.asyncLoading && BUILD17.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD17.cmpDidLoad) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n      }\n      safeCall(instance, \"componentDidLoad\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD17.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD17.cmpDidUpdate) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 1024 /* devOnRender */;\n      }\n      safeCall(instance, \"componentDidUpdate\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~1024 /* devOnRender */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD17.method && BUILD17.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD17.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = (ref) => {\n  if (BUILD17.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = (who) => {\n  if (BUILD17.cssAnnotations) {\n    addHydratedFlag(doc.documentElement);\n  }\n  if (BUILD17.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n  if (BUILD17.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD17.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = (elm) => {\n  var _a, _b;\n  return BUILD17.hydratedClass ? elm.classList.add((_a = BUILD17.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD17.hydratedAttribute ? elm.setAttribute((_b = BUILD17.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = (elm) => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD18.lazyLoad && !hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`\n    );\n  }\n  const elm = BUILD18.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD18.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD18.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD18.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      }\n    }\n    if (!BUILD18.lazyLoad || instance) {\n      if (BUILD18.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD18.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (BUILD18.cmpShouldUpdate && instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD19.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(\n      (cbName) => Object.defineProperty(prototype, cbName, {\n        value(...args) {\n          const hostRef = getHostRef(this);\n          const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n          const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n          if (!instance) {\n            hostRef.$onReadyPromise$.then((instance2) => {\n              const cb = instance2[cbName];\n              typeof cb === \"function\" && cb.call(instance2, ...args);\n            });\n          } else {\n            const cb = instance[cbName];\n            typeof cb === \"function\" && cb.call(instance, ...args);\n          }\n        }\n      })\n    );\n  }\n  if (BUILD19.member && cmpMeta.$members$ || BUILD19.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD19.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD19.prop || BUILD19.state) && (memberFlags & 31 /* Prop */ || (!BUILD19.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            if (BUILD19.isDev) {\n              const ref = getHostRef(this);\n              if (\n                // we are proxying the instance (not element)\n                (flags & 1 /* isElementConstructor */) === 0 && // the element is not constructing\n                (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 && // the member is a prop\n                (memberFlags & 31 /* Prop */) !== 0 && // the member is not mutable\n                (memberFlags & 1024 /* Mutable */) === 0\n              ) {\n                consoleDevWarn(\n                  `@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`\n                );\n              }\n            }\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (BUILD19.lazyLoad && BUILD19.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD19.observeAttribute && (!BUILD19.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (BUILD19.reflect && m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if ((BUILD20.lazyLoad || BUILD20.hydrateClientSide) && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(\n          `st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`,\n          `[Stencil] Load module for <${cmpMeta.$tagName$}>`\n        );\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD20.member && !Cstr.isProxied) {\n        if (BUILD20.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD20.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      if (BUILD20.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD20.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD20.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD20.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD20.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (!BUILD20.hydrateServerSide && BUILD20.shadowDom && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        BUILD20.shadowDomShim && cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n          style = await import(\"./shadow-css.js\").then((m) => m.scopeCss(style, scopeId2));\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD20.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance) => {\n  if (BUILD20.lazyLoad && BUILD20.connectedCallback) {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD21.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD21.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD21.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD21.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD21.slotRelocation && !hostId) {\n        if (BUILD21.hydrateServerSide || (BUILD21.slot || BUILD21.shadowDom) && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD21.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD21.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD21.prop && !BUILD21.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD21.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(\n    BUILD21.isDebug ? `content-ref (host=${elm.localName})` : \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = (instance) => {\n  if (BUILD22.lazyLoad && BUILD22.disconnectedCallback) {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n  if (BUILD22.cmpDidUnload) {\n    safeCall(instance, \"componentDidUnload\");\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD22.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD22.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD23.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD23.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD23.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotPlaceholder = document.createTextNode(\"\");\n      slotPlaceholder[\"s-nr\"] = newChild;\n      slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n      newChild[\"s-ol\"] = slotPlaceholder;\n      newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find((n) => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  if (BUILD23.experimentalScopedSlotChanges) {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map((node) => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter((ref) => ref !== \"\").join(\" \");\n        }).filter((text) => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach((node) => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  } else {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      get() {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          return slotNode.nextSibling.textContent;\n        } else if (slotNode) {\n          return slotNode.textContent;\n        } else {\n          return this.__textContent;\n        }\n      },\n      set(value) {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          slotNode.nextSibling.textContent = value;\n        } else if (slotNode) {\n          slotNode.textContent = value;\n        } else {\n          this.__textContent = value;\n          const contentRefElm = this[\"s-cr\"];\n          if (contentRefElm) {\n            insertBefore(this, contentRefElm, this.firstChild);\n          }\n        }\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map((n) => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = (childNodes) => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = (node) => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD24.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD24.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD24.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD24.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD24.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD24.experimentalSlotFixes) {\n    if (BUILD24.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n    }\n  } else {\n    if (BUILD24.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype, cmpMeta);\n    }\n    if (BUILD24.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD24.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD24.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      const hostRef = getHostRef(this);\n      addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n      connectedCallback(this);\n      if (BUILD24.connectedCallback && originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (BUILD24.disconnectedCallback && originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          if (BUILD24.shadowDelegatesFocus) {\n            this.attachShadow({\n              mode: \"open\",\n              delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n            });\n          } else {\n            this.attachShadow({ mode: \"open\" });\n          }\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(\n              `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`\n            );\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = (elm) => {\n  if (BUILD24.style && BUILD24.mode && !BUILD24.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD25.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  if (BUILD25.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD25.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD25.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD25.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD25.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD25.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD25.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD25.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD25.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                if (BUILD25.shadowDelegatesFocus) {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                } else {\n                  self.attachShadow({ mode: \"open\" });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            } else if (!BUILD25.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD25.experimentalSlotFixes) {\n        if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      } else {\n        if (BUILD25.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype, cmpMeta);\n        }\n        if (BUILD25.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD25.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD25.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD25.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD25.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function(hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD25.invisiblePrehydration && (BUILD25.hydratedClass || BUILD25.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    if (BUILD25.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD26.hostListener && listeners) {\n    if (BUILD26.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD26.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    if (BUILD26.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (BUILD26.hostListenerTargetDocument && flags & 4 /* TargetDocument */) return doc;\n  if (BUILD26.hostListenerTargetWindow && flags & 8 /* TargetWindow */) return win;\n  if (BUILD26.hostListenerTargetBody && flags & 16 /* TargetBody */) return doc.body;\n  if (BUILD26.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement)\n    return elm.parentElement;\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = (opts) => Object.assign(plt, opts);\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc2, staticComponents) => {\n  if (doc2 != null) {\n    const docData = STENCIL_DOC_DATA in doc2 ? doc2[STENCIL_DOC_DATA] : { ...DEFAULT_DOC_DATA };\n    docData.staticComponents = new Set(staticComponents);\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc2, doc2.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach((orgLocationNode) => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc2.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc2, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach((childNode) => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc2, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc2, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc2, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc2, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(\n          (node) => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]\n        );\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(\n            HYDRATE_CHILD_ID,\n            `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`\n          );\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc2, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc2.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc2, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport {\n  BUILD27 as BUILD,\n  Build,\n  Env,\n  Fragment,\n  H,\n  H as HTMLElement,\n  Host,\n  NAMESPACE2 as NAMESPACE,\n  STENCIL_DEV_MODE,\n  addHostEventListeners,\n  bootstrapLazy,\n  cmpModules,\n  connectedCallback,\n  consoleDevError,\n  consoleDevInfo,\n  consoleDevWarn,\n  consoleError,\n  createEvent,\n  defineCustomElement,\n  disconnectedCallback,\n  doc,\n  forceModeUpdate,\n  forceUpdate,\n  getAssetPath,\n  getElement,\n  getHostRef,\n  getMode,\n  getRenderingRef,\n  getValue,\n  h,\n  insertVdomAnnotations,\n  isMemberInElement,\n  loadModule,\n  modeResolutionChain,\n  nextTick,\n  parsePropertyValue,\n  plt,\n  postUpdateComponent,\n  promiseResolve,\n  proxyComponent,\n  proxyCustomElement,\n  readTask,\n  registerHost,\n  registerInstance,\n  renderVdom,\n  setAssetPath,\n  setErrorHandler,\n  setMode,\n  setNonce,\n  setPlatformHelpers,\n  setPlatformOptions,\n  setValue,\n  styles,\n  supportsConstructableStylesheets,\n  supportsListenerOptions,\n  supportsShadow,\n  win,\n  writeTask\n};\n"], "mappings": "AAAO,MAAMA,EAAY,MAClB,MAAMC,EAAkB,CAAEC,YAAa,KAAMC,mBAAoB,MAAOC,aAAc,KAAMC,WAAY,MAAOC,aAAc,KAAMC,aAAc,MAAOC,WAAY,KAAMC,aAAc,MAAOC,aAAc,MAAOC,aAAc,MAAOC,gBAAiB,MAAOC,YAAa,KAAMC,cAAe,MAAOC,cAAe,MAAOC,kBAAmB,MAAOC,iBAAkB,KAAMC,eAAgB,KAAMC,SAAU,MAAOC,qBAAsB,MAAOC,QAAS,MAAOC,MAAO,KAAMC,8BAA+B,MAAOC,sBAAuB,MAAOC,eAAgB,MAAOC,YAAa,KAAMC,aAAc,KAAMC,mBAAoB,MAAOC,uBAAwB,MAAOC,2BAA4B,MAAOC,yBAA0B,MAAOC,yBAA0B,MAAOC,qBAAsB,MAAOC,kBAAmB,MAAOC,kBAAmB,MAAOC,kBAAmB,MAAOC,cAAe,KAAMC,qBAAsB,WAAYC,mBAAoB,MAAOC,sBAAuB,KAAMC,QAAS,MAAOC,MAAO,MAAOC,UAAW,MAAOC,SAAU,KAAMC,UAAW,KAAMC,mBAAoB,MAAOC,OAAQ,KAAMC,OAAQ,MAAOC,KAAM,MAAOC,iBAAkB,KAAMC,QAAS,MAAOC,KAAM,KAAMC,YAAa,KAAMC,YAAa,MAAOC,WAAY,KAAMC,WAAY,KAAMC,QAAS,MAAOC,OAAQ,MAAOC,yBAA0B,MAAOC,eAAgB,MAAOC,qBAAsB,MAAOC,UAAW,KAAMC,KAAM,KAAMC,kBAAmB,MAAOC,eAAgB,MAAOC,MAAO,KAAMC,MAAO,KAAMC,IAAK,MAAOC,UAAW,KAAMC,iBAAkB,MAAOC,UAAW,KAAMC,cAAe,KAAMC,UAAW,KAAMC,eAAgB,KAAMC,QAAS,KAAMC,aAAc,KAAMC,eAAgB,KAAMC,QAAS,KAAMC,WAAY,KAAMC,UAAW,KAAMC,SAAU,KAAMC,UAAW,MAAOC,cAAe,MCEruD,IAAIC,EAAYC,OAAOC,eACvB,IAAIC,EAAW,CAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACfL,EAAUI,EAAQE,EAAM,CAAEC,IAAKF,EAAIC,GAAOE,WAAY,MAAO,EAcjE,IAAIC,EAAwJ,IAAIC,QAChK,IAAIC,EAAcC,GAAQH,EAASF,IAAIK,GACpC,IAACC,EAAmB,CAACC,EAAcC,IAAYN,EAASO,IAAID,EAAQE,EAAiBH,EAAcC,GACtG,IAAIG,EAAe,CAACC,EAAaC,KAC/B,MAAML,EAAU,CACdM,EAAS,EACTC,cAAeH,EACfI,EAAWH,EACXI,EAAkC,IAAIC,KAQf,CACvBV,EAAQW,EAAmB,IAAIC,SAASC,GAAMb,EAAQc,EAAmBD,IACzET,EAAY,OAAS,GACrBA,EAAY,QAAU,EAC1B,CACE,OAAOV,EAASO,IAAIG,EAAaJ,EAAQ,EAE3C,IAAIe,EAAoB,CAACC,EAAKC,IAAeA,KAAcD,EAQ3D,IAAIE,EAAe,CAACC,EAAGC,KAAO,EAAgBC,QAAQC,OAAOH,EAAGC,GAWhE,IAAIG,EAA6B,IAAIb,IAErC,IAAIc,EAAa,CAACnB,EAASL,EAASyB,KAClC,MAAMC,EAAarB,EAAQsB,EAAUC,QAAQ,KAAM,KACnD,MAAMC,EAAWxB,EAAQyB,EAMlB,IAAKD,EAAU,CACpB,YAAY,CAChB,CACE,MAAME,EAAwCR,EAAW/B,IAAIqC,GAC7D,GAAIE,EAAQ,CACV,OAAOA,EAAOL,EAClB;qCAEE,OAAOM,OAKL,KAAKH,aAA6F,MAClGI,MAAMC,IAC4B,CAChCX,EAAWtB,IAAI4B,EAAUK,EAC/B,CACI,OAAOA,EAAeR,EAAW,GAChCR,EAAa,EAIlB,IAAIiB,EAAyB,IAAIzB,IAcjC,IAAI0B,EAAe,mDAOnB,IAAIC,EAAc,yDAWlB,IAAIC,SAAaC,SAAW,YAAcA,OAAS,GACnD,IAAIC,EAAMF,EAAIG,UAAY,CAAEC,KAAM,IAGlC,IAAIC,EAAM,CACRrC,EAAS,EACTsC,EAAgB,GAChBC,IAAMC,GAAOA,IACbC,IAAMD,GAAOE,sBAAsBF,GACnCG,IAAK,CAAC7B,EAAI8B,EAAWC,EAAUC,IAAShC,EAAGiC,iBAAiBH,EAAWC,EAAUC,GACjFE,IAAK,CAAClC,EAAI8B,EAAWC,EAAUC,IAAShC,EAAGmC,oBAAoBL,EAAWC,EAAUC,GACpFI,GAAI,CAACN,EAAWE,IAAS,IAAIK,YAAYP,EAAWE,IAMtD,IAAIM,EAA0C,MAC5C,IAAIC,EAA2B,MAC/B,IACEnB,EAAIa,iBACF,IACA,KACAnE,OAAOC,eAAe,GAAI,UAAW,CACnC,GAAAK,GACEmE,EAA2B,IACrC,IAGA,CAAI,MAAOxC,GACX,CACE,OAAOwC,CACR,EAf6C,GAgB3C,IAACC,EAAkBC,GAAMjD,QAAQkD,QAAQD,GAC5C,IAAIE,EAA6E,MAC/E,IACE,IAAIC,cACJ,cAAc,IAAIA,eAAgBC,cAAgB,UACtD,CAAI,MAAO9C,GACX,CACE,OAAO,KACR,EAPgF,GAWjF,IAAI+C,EAAe,MACnB,IAAIC,EAAgB,GACpB,IAAIC,EAAiB,GAErB,IAAIC,EAAY,CAACC,EAAOC,IAAWC,IACjCF,EAAMG,KAAKD,GACX,IAAKN,EAAc,CACjBA,EAAe,KACf,GAAIK,GAAS5B,EAAIrC,EAAU,EAAmB,CAC5CoE,EAASC,EACf,KAAW,CACLhC,EAAII,IAAI4B,EACd,CACA,GAEA,IAAIC,EAAWN,IACb,IAAK,IAAIO,EAAK,EAAGA,EAAKP,EAAMQ,OAAQD,IAAM,CACxC,IACEP,EAAMO,GAAIE,YAAYC,MAC5B,CAAM,MAAO7D,GACPD,EAAaC,EACnB,CACA,CACEmD,EAAMQ,OAAS,CAAC,EAkBlB,IAAIH,EAAQ,KAIVC,EAAQT,GAcD,CACLS,EAAQR,GACR,GAAIF,EAAeC,EAAcW,OAAS,EAAG,CAC3CnC,EAAII,IAAI4B,EACd,CACA,GAEA,IAAID,EAAYF,GAAOZ,IAAiB3B,KAAKuC,GAE7C,IAAIS,EAA4BZ,EAAUD,EAAgB,MAgB1D,IAAIc,EAAY,GAKhB,IAAIC,EAAStB,GAAMA,GAAK,KACxB,IAAIuB,EAAiBC,IACnBA,SAAWA,EACX,OAAOA,IAAM,UAAYA,IAAM,UAAU,EAI3C,SAASC,EAAyBC,GAChC,IAAIC,EAAIC,EAAIC,EACZ,OAAQA,GAAMD,GAAMD,EAAKD,EAAK7C,OAAS,UAAY,EAAI8C,EAAGG,cAAc,4BAA8B,UAAY,EAAIF,EAAGG,aAAa,aAAe,KAAOF,OAAU,CACxK,CAGA,IAAIG,EAAiB,GACrBzG,EAASyG,EAAgB,CACvBC,IAAK,IAAMA,EACXC,IAAK,IAAMA,EACXC,GAAI,IAAMA,EACVC,OAAQ,IAAMA,EACdC,UAAW,IAAMA,IAEnB,IAAIF,EAAMG,IAAK,CACbC,KAAM,KACNC,MAAO,MACPF,UAEF,IAAIL,EAAOK,IAAK,CACdC,KAAM,MACNC,MAAO,KACPF,UAEF,SAASJ,EAAIO,EAAQC,GACnB,GAAID,EAAOF,KAAM,CACf,MAAMI,EAAMD,EAAGD,EAAOH,OACtB,GAAIK,aAAe5F,QAAS,CAC1B,OAAO4F,EAAIvE,MAAMwE,GAAWT,EAAGS,IACrC,KAAW,CACL,OAAOT,EAAGQ,EAChB,CACA,CACE,GAAIF,EAAOD,MAAO,CAChB,MAAMF,EAAQG,EAAOH,MACrB,OAAOL,EAAIK,EACf,CACE,KAAM,uBACR,CACA,IAAIF,EAAUK,IACZ,GAAIA,EAAOF,KAAM,CACf,OAAOE,EAAOH,KAClB,KAAS,CACL,MAAMG,EAAOH,KACjB,GAEA,IAAID,EAAaI,IACf,GAAIA,EAAOD,MAAO,CAChB,OAAOC,EAAOH,KAClB,KAAS,CACL,MAAMG,EAAOH,KACjB,GAYA,IAAIO,EAAa,CAACC,EAAQC,EAAU,MAK3B,CACL,MAAO,MAGX,GAEA,IAAIC,EAAa,CAACC,EAAKC,KAUd,CACL,MAAO,MAGX,GA4DG,IAACC,EAAI,CAACC,EAAUC,KAAcC,KAC/B,IAAIC,EAAQ,KACZ,IAAIN,EAAM,KAEV,IAAIO,EAAS,MACb,IAAIC,EAAa,MACjB,MAAMC,EAAgB,GACtB,MAAMC,EAAQC,IACZ,IAAK,IAAI5C,EAAK,EAAGA,EAAK4C,EAAE3C,OAAQD,IAAM,CACpCuC,EAAQK,EAAE5C,GACV,GAAI6C,MAAMC,QAAQP,GAAQ,CACxBI,EAAKJ,EACb,MAAa,GAAIA,GAAS,aAAeA,IAAU,UAAW,CACtD,GAAIC,SAAgBJ,IAAa,aAAe7B,EAAcgC,GAAQ,CACpEA,EAAQQ,OAAOR,EACzB,CAKQ,GAAIC,GAAUC,EAAY,CACxBC,EAAcA,EAAczC,OAAS,GAAG+C,GAAUT,CAC5D,KAAe,CACLG,EAAc9C,KAAK4C,EAASS,EAAS,KAAMV,GAASA,EAC9D,CACQE,EAAaD,CACrB,CACA,GAEEG,EAAKL,GACL,GAAID,EAAW,CAIb,GAAsBA,EAAUJ,IAAK,CACnCA,EAAMI,EAAUJ,GACtB,CAI0B,CACpB,MAAMiB,EAAYb,EAAUc,WAAad,EAAUe,MACnD,GAAIF,EAAW,CACbb,EAAUe,aAAeF,IAAc,SAAWA,EAAY7I,OAAOgJ,KAAKH,GAAWI,QAAQC,GAAML,EAAUK,KAAIC,KAAK,IAC9H,CACA,CACA,CAME,UAAoCpB,IAAa,WAAY,CAC3D,OAAOA,EACLC,IAAc,KAAO,GAAKA,EAC1BK,EACAe,EAEN,CACE,MAAMC,EAAQT,EAASb,EAAU,MACjCsB,EAAMC,EAAUtB,EAChB,GAAIK,EAAczC,OAAS,EAAG,CAC5ByD,EAAME,EAAalB,CACvB,CACsB,CAClBgB,EAAMG,EAAQ5B,CAClB,CAIE,OAAOyB,CAAK,EAEd,IAAIT,EAAW,CAACa,EAAKC,KACnB,MAAML,EAAQ,CACZjI,EAAS,EACTuI,EAAOF,EACPd,EAAQe,EACRE,EAAO,KACPL,EAAY,MAEY,CACxBF,EAAMC,EAAU,IACpB,CACsB,CAClBD,EAAMG,EAAQ,IAClB,CAIE,OAAOH,CAAK,EAEX,IAACQ,EAAO,GACX,IAAIC,EAAUC,GAASA,GAAQA,EAAKJ,IAAUE,EAC9C,IAAIT,EAAc,CAChBY,QAAS,CAAC/B,EAAU3C,IAAO2C,EAASpB,IAAIoD,GAAiBD,QAAQ1E,GACjEuB,IAAK,CAACoB,EAAU3C,IAAO2C,EAASpB,IAAIoD,GAAiBpD,IAAIvB,GAAIuB,IAAIqD,IAEnE,IAAID,EAAmBF,IAAI,CACzBI,OAAQJ,EAAKT,EACbc,UAAWL,EAAKR,EAChBc,KAAMN,EAAKP,EACXc,MAAOP,EAAKQ,EACZC,KAAMT,EAAKJ,EACXc,MAAOV,EAAKpB,IAEd,IAAIuB,EAAoBH,IACtB,UAAWA,EAAKS,OAAS,WAAY,CACnC,MAAMxC,EAAY,IAAK+B,EAAKI,QAC5B,GAAIJ,EAAKM,KAAM,CACbrC,EAAUJ,IAAMmC,EAAKM,IAC3B,CACI,GAAIN,EAAKO,MAAO,CACdtC,EAAU3H,KAAO0J,EAAKO,KAC5B,CACI,OAAOxC,EAAEiC,EAAKS,KAAMxC,KAAc+B,EAAKK,WAAa,GACxD,CACE,MAAMf,EAAQT,EAASmB,EAAKS,KAAMT,EAAKU,OACvCpB,EAAMC,EAAUS,EAAKI,OACrBd,EAAME,EAAaQ,EAAKK,UACxBf,EAAMG,EAAQO,EAAKM,KACnBhB,EAAMkB,EAASR,EAAKO,MACpB,OAAOjB,CAAK,EAmOd,IAAIqB,EAAqB,CAACC,EAAWC,KACnC,GAAID,GAAa,OAASzE,EAAcyE,GAAY,CAClD,GAA2BC,EAAW,EAAiB,CACrD,OAAOD,IAAc,QAAU,MAAQA,IAAc,MAAQA,CACnE,CACI,GAA0BC,EAAW,EAAgB,CACnD,OAAOC,WAAWF,EACxB,CACI,GAA0BC,EAAW,EAAgB,CACnD,OAAOlC,OAAOiC,EACpB,CACI,OAAOA,CACX,CACE,OAAOA,CAAS,EAWlB,IAAIG,EAAcnK,GAA2BD,EAAWC,GAAKU,cAG1D,IAAC0J,EAAc,CAACpK,EAAKN,EAAM2K,KAC5B,MAAMlJ,EAAMgJ,EAAWnK,GACvB,MAAO,CACLsK,KAAOC,GAIEC,EAAUrJ,EAAKzB,EAAM,CAC1B+K,WAAYJ,EAAQ,GACpBK,YAAaL,EAAQ,GACrBM,cAAeN,EAAQ,GACvBE,WAGL,EAEH,IAAIC,EAAY,CAACrJ,EAAKzB,EAAM6D,KAC1B,MAAMqH,EAAK9H,EAAIa,GAAGjE,EAAM6D,GACxBpC,EAAI0J,cAAcD,GAClB,OAAOA,CAAE,EAKX,IAAIE,EAAoC,IAAIhL,QAC5C,IAAIiL,EAAgB,CAACC,EAAUC,EAASC,KACtC,IAAI/M,EAAQmE,EAAO3C,IAAIqL,GACvB,GAAI9G,GAAoCgH,EAAS,CAC/C/M,EAAQA,GAAS,IAAIgG,cACrB,UAAWhG,IAAU,SAAU,CAC7BA,EAAQ8M,CACd,KAAW,CACL9M,EAAMiG,YAAY6G,EACxB,CACA,KAAS,CACL9M,EAAQ8M,CACZ,CACE3I,EAAOlC,IAAI4K,EAAU7M,EAAM,EAE7B,IAAIgN,GAAW,CAACC,EAAoB5K,EAASvD,KAC3C,IAAI0I,EACJ,MAAMqF,EAAWK,GAAW7K,GAC5B,MAAMrC,EAAQmE,EAAO3C,IAAIqL,GAIzBI,EAAqBA,EAAmBE,WAAa,GAA4BF,EAAqBzI,EACtG,GAAIxE,EAAO,CACT,UAAWA,IAAU,SAAU,CAC7BiN,EAAqBA,EAAmBvI,MAAQuI,EAChD,IAAIG,EAAgBT,EAAkBnL,IAAIyL,GAC1C,IAAII,EACJ,IAAKD,EAAe,CAClBT,EAAkB1K,IAAIgL,EAAoBG,EAAgC,IAAIE,IACtF,CACM,IAAKF,EAAcG,IAAIV,GAAW,CAGzB,CACLQ,EAAW7I,EAAIgJ,cAAc,SAC7BH,EAASI,UAAYzN,EACrB,MAAM0N,GAASlG,EAAK7C,EAAIgJ,IAAY,KAAOnG,EAAKF,EAAyB9C,GACzE,GAAIkJ,GAAS,KAAM,CACjBL,EAASO,aAAa,QAASF,EAC3C,CAIU,KAAMrL,EAAQC,EAAU,GAAiC,CACvD,GAAI2K,EAAmBhE,WAAa,OAAQ,CAC1C,MAAM4E,EAAkBZ,EAAmBa,iBAAiB,wBAC5D,MAAMC,EAAiBF,EAAgB/G,OAAS,EAAI+G,EAAgBA,EAAgB/G,OAAS,GAAGkH,YAAcf,EAAmBtF,cAAc,SAC/IsF,EAAmBgB,aAAaZ,EAAUU,EACxD,MAAmB,GAAI,SAAUd,EAAoB,CACvC,GAAIlH,EAAkC,CACpC,MAAMmI,EAAa,IAAIlI,cACvBkI,EAAWjI,YAAYjG,GACvBiN,EAAmBkB,mBAAqB,CAACD,KAAejB,EAAmBkB,mBAC3F,KAAqB,CACL,MAAMC,EAAyBnB,EAAmBtF,cAAc,SAChE,GAAIyG,EAAwB,CAC1BA,EAAuBX,UAAYzN,EAAQoO,EAAuBX,SACpF,KAAuB,CACLR,EAAmBoB,QAAQhB,EAC7C,CACA,CACA,KAAmB,CACLJ,EAAmBqB,OAAOjB,EACxC,CACA,CACU,GAAIhL,EAAQC,EAAU,GAAkC2K,EAAmBhE,WAAa,OAAQ,CAC9FgE,EAAmBgB,aAAaZ,EAAU,KACtD,CACA,CACQ,GAAIhL,EAAQC,EAAU,EAA2B,CAC/C+K,EAASI,WAAapJ,CAChC,CACQ,GAAI+I,EAAe,CACjBA,EAAcmB,IAAI1B,EAC5B,CACA,CACA,MAAW,IAAiCI,EAAmBkB,mBAAmBK,SAASxO,GAAQ,CAC7FiN,EAAmBkB,mBAAqB,IAAIlB,EAAmBkB,mBAAoBnO,EACzF,CACA,CACE,OAAO6M,CAAQ,EAEjB,IAAI1Q,GAAgB6F,IAClB,MAAMK,EAAUL,EAAQQ,EACxB,MAAMQ,EAAMhB,EAAQO,cACpB,MAAM2J,EAAQ7J,EAAQC,EACtB,MAAMmM,EAAkB/F,EAAW,eAAgBrG,EAAQsB,GAC3D,MAAMkJ,EAAWG,GACwBhK,EAAI0L,WAAa1L,EAAI0L,WAAa1L,EAAI2L,cAC7EtM,GAGF,GAAuE6J,EAAQ,IAAqCA,EAAQ,EAAgC,CAC1JlJ,EAAI,QAAU6J,EACd7J,EAAI4L,UAAUL,IAAI1B,EAAW,KAIjC,CACE4B,GAAiB,EAEnB,IAAIvB,GAAa,CAAC2B,EAAK/P,IAAS,MAA+F+P,EAAa,EAU5I,IAAIC,GAAc,CAAC9L,EAAKC,EAAY8L,EAAUC,EAAUC,EAAO/C,KAC7D,GAAI6C,IAAaC,EAAU,CACzB,IAAIE,EAASnM,EAAkBC,EAAKC,GACpC,IAAIkM,EAAKlM,EAAWmM,cACpB,GAAyBnM,IAAe,QAAS,CAC/C,MAAM2L,EAAY5L,EAAI4L,UACtB,MAAMS,EAAaC,GAAeP,GAClC,MAAMQ,EAAaD,GAAeN,GAClCJ,EAAUY,UAAUH,EAAWlF,QAAQV,GAAMA,IAAM8F,EAAWf,SAAS/E,MACvEmF,EAAUL,OAAOgB,EAAWpF,QAAQV,GAAMA,IAAM4F,EAAWb,SAAS/E,KAC1E,MAAW,GAAyBxG,IAAe,QAAS,CAC/B,CACrB,IAAK,MAAMhE,KAAQ8P,EAAU,CAC3B,IAAKC,GAAYA,EAAS/P,IAAS,KAAM,CACvC,GAAkCA,EAAKuP,SAAS,KAAM,CACpDxL,EAAIhD,MAAMyP,eAAexQ,EACvC,KAAmB,CACL+D,EAAIhD,MAAMf,GAAQ,EAChC,CACA,CACA,CACA,CACM,IAAK,MAAMA,KAAQ+P,EAAU,CAC3B,IAAKD,GAAYC,EAAS/P,KAAU8P,EAAS9P,GAAO,CAClD,GAAkCA,EAAKuP,SAAS,KAAM,CACpDxL,EAAIhD,MAAM0P,YAAYzQ,EAAM+P,EAAS/P,GACjD,KAAiB,CACL+D,EAAIhD,MAAMf,GAAQ+P,EAAS/P,EACvC,CACA,CACA,CACA,MAAW,GAAuBgE,IAAe,YACtC,GAAuBA,IAAe,MAAO,CAClD,GAAI+L,EAAU,CACZA,EAAShM,EACjB,CACA,MAAW,IAAiDkM,GAA+CjM,EAAW,KAAO,KAAOA,EAAW,KAAO,IAAK,CACrJ,GAAIA,EAAW,KAAO,IAAK,CACzBA,EAAaA,EAAW0M,MAAM,EACtC,MAAa,GAAI5M,EAAkBuB,EAAK6K,GAAK,CACrClM,EAAakM,EAAGQ,MAAM,EAC9B,KAAa,CACL1M,EAAakM,EAAG,GAAKlM,EAAW0M,MAAM,EAC9C,CACM,GAAIZ,GAAYC,EAAU,CACxB,MAAMY,EAAU3M,EAAW4M,SAASC,IACpC7M,EAAaA,EAAWW,QAAQmM,GAAqB,IACrD,GAAIhB,EAAU,CACZpK,EAAIW,IAAItC,EAAKC,EAAY8L,EAAUa,EAC7C,CACQ,GAAIZ,EAAU,CACZrK,EAAIM,IAAIjC,EAAKC,EAAY+L,EAAUY,EAC7C,CACA,CACA,KAAuC,CACjC,MAAMI,EAAY5I,EAAc4H,GAChC,IAAKE,GAAUc,GAAahB,IAAa,QAAUC,EAAO,CACxD,IACE,IAAKjM,EAAI4F,QAAQ4F,SAAS,KAAM,CAC9B,MAAMyB,EAAIjB,GAAY,KAAO,GAAKA,EAClC,GAAI/L,IAAe,OAAQ,CACzBiM,EAAS,KACvB,MAAmB,GAAIH,GAAY,MAAQ/L,EAAIC,IAAegN,EAAG,CACnD,UAAWjN,EAAIkN,iBAAiBjN,KAAgB,WAAY,CAC1DD,EAAIC,GAAcgN,CAClC,KAAqB,CACLjN,EAAI4K,aAAa3K,EAAYgN,EAC7C,CACA,CACA,KAAiB,CACLjN,EAAIC,GAAc+L,CAC9B,CACA,CAAU,MAAO7L,GACjB,CACA,CAQM,GAAI6L,GAAY,MAAQA,IAAa,MAAO,CAC1C,GAAIA,IAAa,OAAShM,EAAI4E,aAAa3E,KAAgB,GAAI,CAGtD,CACLD,EAAImN,gBAAgBlN,EAChC,CACA,CACA,MAAa,KAAMiM,GAAUhD,EAAQ,GAAkB+C,KAAWe,EAAW,CACrEhB,EAAWA,IAAa,KAAO,GAAKA,EAG7B,CACLhM,EAAI4K,aAAa3K,EAAY+L,EACvC,CACA,CACA,CACA,GAEA,IAAIoB,GAAsB,KAC1B,IAAId,GAAkBnH,IAAWA,EAAQ,GAAKA,EAAMkI,MAAMD,IAC1D,IAAIN,GAAuB,UAC3B,IAAIC,GAAsB,IAAIO,OAAOR,GAAuB,KAG5D,IAAIS,GAAgB,CAACC,EAAUC,EAAUC,KACvC,MAAM1N,EAAMyN,EAAS3F,EAAMqC,WAAa,IAA6BsD,EAAS3F,EAAM6F,KAAOF,EAAS3F,EAAM6F,KAAOF,EAAS3F,EAC1H,MAAM8F,EAAgBJ,GAAYA,EAAShG,GAAWtD,EACtD,MAAM2J,EAAgBJ,EAASjG,GAAWtD,EACnB,CACrB,IAAK,MAAMjE,KAAc6N,GAAgB5P,OAAOgJ,KAAK0G,IAAiB,CACpE,KAAM3N,KAAc4N,GAAgB,CAClC/B,GAAY9L,EAAKC,EAAY2N,EAAc3N,QAAkB,EAAGyN,EAAYD,EAASnO,EAC7F,CACA,CACA,CACE,IAAK,MAAMW,KAAc6N,GAAgB5P,OAAOgJ,KAAK2G,IAAiB,CACpE/B,GAAY9L,EAAKC,EAAY2N,EAAc3N,GAAa4N,EAAc5N,GAAayN,EAAYD,EAASnO,EAC5G,GAEA,SAASwO,GAAgBC,GACvB,OAAOA,EAAUvC,SAAS,OAAM,IAE1BuC,EAAU5G,QAAQ6G,GAASA,IAAS,QAAQ,OACpD,CAIA,CAGA,IAAIC,GAEJ,IAAIC,GACJ,IAAIC,GAAqB,MAGzB,IAAIC,GAAY,MAChB,IAAIC,GAAY,CAACC,EAAgBC,EAAgBC,EAAYC,KAE3D,MAAMC,EAAYH,EAAe9G,EAAW+G,GAC5C,IAAI3K,EAAK,EACT,IAAI7D,EACJ,IAAI2O,EAyBJ,GAAwBD,EAAU7H,IAAW,KAAM,CACjD7G,EAAM0O,EAAU5G,EAAQtG,EAAIoN,eAAeF,EAAU7H,EACzD,KAES,CAIL7G,EAAM0O,EAAU5G,EAGZtG,EAAIgJ,eACL2D,IAAsBU,EAAQ/R,gBAAkB4R,EAAUpP,EAAU,EAAyB,UAAYoP,EAAU7G,GAK3F,CACzB0F,GAAc,KAAMmB,EAAWN,GACrC,CACI,MAAMU,EAAW9O,EAAI2L,cACrB,MAAMoD,GAA6BD,EAASnK,cAAc,QAC1D,IAAKoK,GAA6BF,EAAQtS,QAAU4H,EAAM8J,KAAYjO,EAAI,UAAYiO,GAAS,CAC7FjO,EAAI4L,UAAUL,IAAIvL,EAAI,QAAUiO,GACtC,CAII,GAAIS,EAAUjH,EAAY,CACxB,IAAK5D,EAAK,EAAGA,EAAK6K,EAAUjH,EAAW3D,SAAUD,EAAI,CACnD8K,EAAYN,GAAUC,EAAgBI,EAAW7K,GACjD,GAAI8K,EAAW,CACb3O,EAAIgP,YAAYL,EAC1B,CACA,CACA,CAQA,CACE3O,EAAI,QAAUkO,GAiBd,OAAOlO,CAAG,EA4CZ,IAAIiP,GAAY,CAACR,EAAWS,EAAQC,EAAaC,EAAQC,EAAUC,KACjE,IAAIC,EAA8Fd,EAClG,IAAIE,EACJ,GAAyBY,EAAa7D,YAAc6D,EAAa3J,UAAYsI,GAAa,CACxFqB,EAAeA,EAAa7D,UAChC,CACE,KAAO2D,GAAYC,IAAUD,EAAU,CACrC,GAAID,EAAOC,GAAW,CACpBV,EAAYN,GAAU,KAAMc,EAAaE,GACzC,GAAIV,EAAW,CACbS,EAAOC,GAAUvH,EAAQ6G,EACzB1D,GAAasE,EAAcZ,EAA4DO,EAC/F,CACA,CACA,GAEA,IAAIM,GAAe,CAACJ,EAAQC,EAAUC,KACpC,IAAK,IAAIG,EAAQJ,EAAUI,GAASH,IAAUG,EAAO,CACnD,MAAMlI,EAAQ6H,EAAOK,GACrB,GAAIlI,EAAO,CACT,MAAMvH,EAAMuH,EAAMO,EAClB4H,GAAiBnI,GACjB,GAAIvH,EAAK,CASPA,EAAIwM,QACZ,CACA,CACA,GAEA,IAAImD,GAAiB,CAAClB,EAAWmB,EAAOlB,EAAWmB,EAAOC,EAAkB,SAC1E,IAAIC,EAAc,EAClB,IAAIC,EAAc,EAClB,IAAIC,EAAW,EACf,IAAIpM,EAAK,EACT,IAAIqM,EAAYN,EAAM9L,OAAS,EAC/B,IAAIqM,EAAgBP,EAAM,GAC1B,IAAIQ,EAAcR,EAAMM,GACxB,IAAIG,EAAYR,EAAM/L,OAAS,EAC/B,IAAIwM,EAAgBT,EAAM,GAC1B,IAAIU,EAAcV,EAAMQ,GACxB,IAAIpI,EACJ,IAAIuI,EACJ,MAAOT,GAAeG,GAAaF,GAAeK,EAAW,CAC3D,GAAIF,GAAiB,KAAM,CACzBA,EAAgBP,IAAQG,EAC9B,MAAW,GAAIK,GAAe,KAAM,CAC9BA,EAAcR,IAAQM,EAC5B,MAAW,GAAII,GAAiB,KAAM,CAChCA,EAAgBT,IAAQG,EAC9B,MAAW,GAAIO,GAAe,KAAM,CAC9BA,EAAcV,IAAQQ,EAC5B,MAAW,GAAII,GAAYN,EAAeG,EAAeR,GAAkB,CACrEY,GAAMP,EAAeG,EAAeR,GACpCK,EAAgBP,IAAQG,GACxBO,EAAgBT,IAAQG,EAC9B,MAAW,GAAIS,GAAYL,EAAaG,EAAaT,GAAkB,CACjEY,GAAMN,EAAaG,EAAaT,GAChCM,EAAcR,IAAQM,GACtBK,EAAcV,IAAQQ,EAC5B,MAAW,GAAII,GAAYN,EAAeI,EAAaT,GAAkB,CAInEY,GAAMP,EAAeI,EAAaT,GAClC7E,GAAawD,EAAW0B,EAAcrI,EAAOsI,EAAYtI,EAAMkD,aAC/DmF,EAAgBP,IAAQG,GACxBQ,EAAcV,IAAQQ,EAC5B,MAAW,GAAII,GAAYL,EAAaE,EAAeR,GAAkB,CAInEY,GAAMN,EAAaE,EAAeR,GAClC7E,GAAawD,EAAW2B,EAAYtI,EAAOqI,EAAcrI,GACzDsI,EAAcR,IAAQM,GACtBI,EAAgBT,IAAQG,EAC9B,KAAW,CACLC,GAAY,EACS,CACnB,IAAKpM,EAAKkM,EAAalM,GAAMqM,IAAarM,EAAI,CAC5C,GAAI+L,EAAM/L,IAAO+L,EAAM/L,GAAI6D,IAAU,MAAQkI,EAAM/L,GAAI6D,IAAU4I,EAAc5I,EAAO,CACpFuI,EAAWpM,EACX,KACZ,CACA,CACA,CACM,GAAuBoM,GAAY,EAAG,CACpCO,EAAYZ,EAAMK,GAClB,GAAIO,EAAU3I,IAAUyI,EAAczI,EAAO,CAC3CI,EAAOoG,GAAUuB,GAASA,EAAMI,GAActB,EAAWuB,EACnE,KAAe,CACLS,GAAMF,EAAWF,EAAeR,GAChCF,EAAMK,QAAiB,EACvBhI,EAAOuI,EAAU1I,CAC3B,CACQwI,EAAgBT,IAAQG,EAChC,KAAa,CACL/H,EAAOoG,GAAUuB,GAASA,EAAMI,GAActB,EAAWsB,GACzDM,EAAgBT,IAAQG,EAChC,CACM,GAAI/H,EAAM,CAGD,CACLgD,GAAakF,EAAcrI,EAAM6I,WAAY1I,EAAMkI,EAAcrI,EAC3E,CACA,CACA,CACA,CACE,GAAIiI,EAAcG,EAAW,CAC3BjB,GACER,EACAoB,EAAMQ,EAAY,IAAM,KAAO,KAAOR,EAAMQ,EAAY,GAAGvI,EAC3D4G,EACAmB,EACAG,EACAK,EAEN,MAAS,GAAyBL,EAAcK,EAAW,CACvDb,GAAaI,EAAOG,EAAaG,EACrC,GAEA,IAAIO,GAAc,CAACG,EAAWC,EAAYf,EAAkB,SAC1D,GAAIc,EAAU/I,IAAUgJ,EAAWhJ,EAAO,CAaxC,IAAwBiI,EAAiB,CACvC,OAAOc,EAAUlJ,IAAUmJ,EAAWnJ,CAC5C,CACI,OAAO,IACX,CACE,OAAO,KAAK,EAMd,IAAIgJ,GAAQ,CAACI,EAAUpC,EAAWoB,EAAkB,SAClD,MAAM9P,EAAM0O,EAAU5G,EAAQgJ,EAAShJ,EACvC,MAAMiJ,EAAcD,EAASrJ,EAC7B,MAAMuJ,EAActC,EAAUjH,EAC9B,MAAME,EAAM+G,EAAU7G,EACtB,MAAMD,EAAO8G,EAAU7H,EAEvB,GAAyBe,IAAS,KAAM,CAIQ,CAC5C,GAAoBD,IAAQ,SAAWwG,QAKhC,CACLZ,GAAcuD,EAAUpC,EAAWN,GAC3C,CACA,CACI,GAAyB2C,IAAgB,MAAQC,IAAgB,KAAM,CACrErB,GAAe3P,EAAK+Q,EAAarC,EAAWsC,EAAalB,EAC/D,MAAW,GAAIkB,IAAgB,KAAM,CAC/B,GAA6CF,EAASjK,IAAW,KAAM,CACrE7G,EAAIiR,YAAc,EAC1B,CACMhC,GAAUjP,EAAK,KAAM0O,EAAWsC,EAAa,EAAGA,EAAYlN,OAAS,EAC3E,MAAW,IAEJgM,GAAmBjB,EAAQzR,WAAa2T,IAAgB,KACzD,CACAvB,GAAauB,EAAa,EAAGA,EAAYjN,OAAS,EACxD,CAIA,MAES,GAAwBgN,EAASjK,IAAWe,EAAM,CACvD5H,EAAIkR,KAAOtJ,CACf,GA6FA,IAAI8H,GAAoByB,IACD,CACnBA,EAAM3J,GAAW2J,EAAM3J,EAAQ3I,KAAOsS,EAAM3J,EAAQ3I,IAAI,MACxDsS,EAAM1J,GAAc0J,EAAM1J,EAAW1C,IAAI2K,GAC7C,GAEA,IAAIzE,GAAe,CAACmG,EAAQC,EAASC,KACnC,MAAMC,EAAWH,GAAU,UAAY,EAAIA,EAAOnG,aAAaoG,EAASC,GAIxE,OAAOC,CAAQ,EA4BjB,IAAIC,GAAa,CAACxS,EAASyS,EAAiBC,EAAgB,SAE1D,MAAMC,EAAU3S,EAAQO,cACxB,MAAMF,EAAUL,EAAQQ,EACxB,MAAMsR,EAAW9R,EAAQ4S,GAAW9K,EAAS,KAAM,MACnD,MAAM+K,EAAY7J,EAAOyJ,GAAmBA,EAAkBzL,EAAE,KAAM,KAAMyL,GAC5EvD,GAAcyD,EAAQ/L,QAqBtB,GAAI8L,GAAiBG,EAAUrK,EAAS,CACtC,IAAK,MAAM1B,KAAO5H,OAAOgJ,KAAK2K,EAAUrK,GAAU,CAChD,GAAImK,EAAQG,aAAahM,KAAS,CAAC,MAAO,MAAO,QAAS,SAAS0F,SAAS1F,GAAM,CAChF+L,EAAUrK,EAAQ1B,GAAO6L,EAAQ7L,EACzC,CACA,CACA,CACE+L,EAAUhK,EAAQ,KAClBgK,EAAUvS,GAAW,EACrBN,EAAQ4S,EAAUC,EAClBA,EAAU/J,EAAQgJ,EAAShJ,EAA4B6J,EAAQjG,YAAciG,EACpC,CACvC1D,GAAU0D,EAAQ,OACtB,CACExD,IAAwC9O,EAAQC,EAAU,KAAoC,EAK9FoR,GAAMI,EAAUe,EAAWH,EAAc,EAoF3C,IAAIK,GAAmB,CAAC/S,EAASgT,KAC/B,GAA4BA,IAAsBhT,EAAQiT,GAAqBD,EAAkB,OAAQ,CACvGA,EAAkB,OAAOvO,KAAK,IAAI7D,SAASC,GAAMb,EAAQiT,EAAoBpS,IACjF,GAEA,IAAIqS,GAAiB,CAAClT,EAAS0S,KACe,CAC1C1S,EAAQM,GAAW,EACvB,CACE,GAA4BN,EAAQM,EAAU,EAA8B,CAC1EN,EAAQM,GAAW,IACnB,MACJ,CACEyS,GAAiB/S,EAASA,EAAQmT,GAClC,MAAMC,EAAW,IAAMC,GAAcrT,EAAS0S,GAC9C,OAA2BzN,EAAUmO,EAAsB,EAE7D,IAAIC,GAAgB,CAACrT,EAAS0S,KAC5B,MAAM1R,EAAMhB,EAAQO,cACpB,MAAM+S,EAAc5M,EAAW,iBAAkB1G,EAAQQ,EAAUmB,GACnE,MAAM4R,EAA8BvT,EAAQE,EAC5C,IAAKqT,EAAU,CACb,MAAM,IAAIC,MACR,2BAA2BxS,EAAI4F,QAAQwG,uOAE7C,CACE,IAAIqG,EACJ,GAAIf,EAAe,CAC6B,CAC5C1S,EAAQM,GAAW,IACnB,GAAIN,EAAQ0T,EAAmB,CAC7B1T,EAAQ0T,EAAkB3N,KAAI,EAAE4N,EAAYxY,KAAWyY,GAASL,EAAUI,EAAYxY,KACtF6E,EAAQ0T,OAAyB,CACzC,CACA,CAE6B,CACvBD,EAAeG,GAASL,EAAU,oBACxC,CACA,CAUED,IACA,OAAOO,GAAQJ,GAAc,IAAMK,GAAgB9T,EAASuT,EAAUb,IAAe,EAEvF,IAAImB,GAAU,CAACJ,EAAclN,IAAOwN,GAAWN,GAAgBA,EAAaxR,KAAKsE,GAAIyN,OAAOC,IAC1F5S,QAAQC,MAAM2S,GACd1N,GAAI,IACDA,IACL,IAAIwN,GAAcN,GAAiBA,aAAwB7S,SAAW6S,GAAgBA,EAAaxR,aAAewR,EAAaxR,OAAS,WACxI,IAAI6R,GAAkBI,MAAOlU,EAASuT,EAAUb,KAC9C,IAAIlN,EACJ,MAAMxE,EAAMhB,EAAQO,cACpB,MAAM4T,EAAYzN,EAAW,SAAU1G,EAAQQ,EAAUmB,GACzD,MAAMyS,EAAKpT,EAAI,QACf,GAAqB0R,EAAe,CAClCvY,GAAa6F,EACjB,CACE,MAAMqU,EAAY3N,EAAW,SAAU1G,EAAQQ,EAAUmB,GAMlD,CACL2S,GAAWtU,EAASuT,EAAUvS,EAAK0R,EACvC,CAmBE,GAA4B0B,EAAI,CAC9BA,EAAGrO,KAAKvB,GAAOA,MACfxD,EAAI,aAAe,CACvB,CACEqT,IACAF,IAC0B,CACxB,MAAMI,GAAoB/O,EAAKxE,EAAI,SAAW,KAAOwE,EAAK,GAC1D,MAAMgP,EAAa,IAAMC,GAAoBzU,GAC7C,GAAIuU,EAAiBzP,SAAW,EAAG,CACjC0P,GACN,KAAW,CACL5T,QAAQtB,IAAIiV,GAAkBtS,KAAKuS,GACnCxU,EAAQM,GAAW,EACnBiU,EAAiBzP,OAAS,CAChC,CACA,GAIA,IAAI4P,GAAe,KACnB,IAAIJ,GAAa,CAACtU,EAASuT,EAAUvS,EAAK0R,KAKxC,IACEgC,GAAenB,EACfA,EAAyBA,EAASoB,SACN,CAC1B3U,EAAQM,IAAY,EAC1B,CAC+B,CACzBN,EAAQM,GAAW,CACzB,CACgD,CACC,CAGlC,CACLkS,GAAWxS,EAASuT,EAAUb,EACxC,CACA,CAQA,CACA,CAAI,MAAOvR,GACPD,EAAaC,EAAGnB,EAAQO,cAC5B,CACEmU,GAAe,KACf,OAAO,IAAI,EAEV,IAACE,GAAkB,IAAMF,GAC5B,IAAID,GAAuBzU,IACzB,MAAM4G,EAAU5G,EAAQQ,EAAUmB,EAClC,MAAMX,EAAMhB,EAAQO,cACpB,MAAMsU,EAAgBnO,EAAW,aAAcE,GAC/C,MAAM2M,EAA8BvT,EAAQE,EAC5C,MAAM8S,EAAoBhT,EAAQmT,EAWlC,KAAMnT,EAAQM,EAAU,IAA8B,CACpDN,EAAQM,GAAW,GACiC,CAClDwU,GAAgB9T,EACtB,CAC4B,CAItB4S,GAASL,EAAU,mBAIzB,CAEIsB,IAC0B,CACxB7U,EAAQc,EAAiBE,GACzB,IAAKgS,EAAmB,CACtB+B,IACR,CACA,CACA,KAAS,CAWLF,GACJ,CAI4B,CACxB,GAAI7U,EAAQiT,EAAmB,CAC7BjT,EAAQiT,IACRjT,EAAQiT,OAAyB,CACvC,CACI,GAAIjT,EAAQM,EAAU,IAAyB,CAC7CoE,GAAS,IAAMwO,GAAelT,EAAS,QAC7C,CACIA,EAAQM,KAAa,EAA+B,IACxD,GAEG,IAAC0U,GAAenV,IAC8C,CAC7D,MAAMG,EAAUJ,EAAWC,GAC3B,MAAMoV,EAAcjV,EAAQO,cAAc0U,YAC1C,GAAIA,IAAgBjV,EAAQM,GAAW,EAAsB,OAAiC,EAAqB,CACjH4S,GAAelT,EAAS,MAC9B,CACI,OAAOiV,CACX,GAGA,IAAIF,GAAcG,IACY,CAC1BJ,GAAgBtS,EAAI2S,gBACxB,CAIEzQ,GAAS,IAAM2F,EAAU/H,EAAK,UAAW,CAAE8H,OAAQ,CAAEgL,UAAWvb,MAAe,EAKjF,IAAI+Z,GAAW,CAACL,EAAU1W,EAAQwY,KAChC,GAAI9B,GAAYA,EAAS1W,GAAS,CAChC,IACE,OAAO0W,EAAS1W,GAAQwY,EAC9B,CAAM,MAAOlU,GACPD,EAAaC,EACnB,CACA,CACE,YAAY,CAAC,EAaf,IAAI2T,GAAmB9T,IAClB,IAACwE,EACJ,OAA+BxE,EAAI4L,UAAUL,KAAK/G,EAAK8P,EAAQnZ,uBAAyB,KAAOqJ,EAAK,WAAsI,EAgB5O,IAAI+P,GAAW,CAAC1V,EAAK2V,IAAa5V,EAAWC,GAAKY,EAAiBjB,IAAIgW,GACvE,IAAIC,GAAW,CAAC5V,EAAK2V,EAAU/O,EAAQpG,KACrC,MAAML,EAAUJ,EAAWC,GAC3B,IAAyBG,EAAS,CAChC,MAAM,IAAIwT,MACR,mCAAmCnT,EAAQsB,iZAEjD,CACE,MAAMX,EAAyBhB,EAAQO,cACvC,MAAMmV,EAAS1V,EAAQS,EAAiBjB,IAAIgW,GAC5C,MAAMtL,EAAQlK,EAAQM,EACtB,MAAMiT,EAA8BvT,EAAQE,EAC5CuG,EAASmD,EAAmBnD,EAAQpG,EAAQsV,EAAUH,GAAU,IAChE,MAAMI,EAAaC,OAAOC,MAAMJ,IAAWG,OAAOC,MAAMrP,GACxD,MAAMsP,EAAiBtP,IAAWiP,IAAWE,EAC7C,MAA4B1L,EAAQ,IAAmCwL,SAAgB,IAAMK,EAAgB,CAC3G/V,EAAQS,EAAiBR,IAAIuV,EAAU/O,GAwBvC,GAAyB8M,EAAU,CACjC,GAA6BlT,EAAQ2V,GAAc9L,EAAQ,IAAwB,CACjF,MAAM+L,EAAe5V,EAAQ2V,EAAWR,GACxC,GAAIS,EAAc,CAChBA,EAAalQ,KAAKmQ,IAChB,IACE3C,EAAS2C,GAAiBzP,EAAQiP,EAAQF,EACxD,CAAc,MAAOrU,GACPD,EAAaC,EAAGH,EAC9B,IAEA,CACA,CACM,IAA0BkJ,GAAS,EAAsB,OAAiC,EAAqB,CAM7GgJ,GAAelT,EAAS,MAChC,CACA,CACA,GAIA,IAAImW,GAAiB,CAACC,EAAM/V,EAAS6J,KACnC,IAAI1E,EAAIC,EACR,MAAM4Q,EAAYD,EAAKC,UAqBvB,GAAsBhW,EAAQsV,IAAuCtV,EAAQ2V,GAAcI,EAAKE,UAAW,CACzG,GAA6BF,EAAKE,WAAajW,EAAQ2V,EAAY,CACjE3V,EAAQ2V,EAAaI,EAAKE,QAChC,CACI,MAAMC,EAAUrX,OAAOsX,SAAShR,EAAKnF,EAAQsV,IAAc,KAAOnQ,EAAK,IACvE+Q,EAAQxQ,KAAI,EAAE9E,GAAawV,OACzB,GAAwCA,EAAc,IAAuCvM,EAAQ,GAAuBuM,EAAc,GAAiB,CACzJvX,OAAOC,eAAekX,EAAWpV,EAAY,CAC3C,GAAAzB,GACE,OAAO+V,GAASmB,KAAMzV,EAClC,EACU,GAAAhB,CAAI+M,GAgBFyI,GAASiB,KAAMzV,EAAY+L,EAAU3M,EACjD,EACUsW,aAAc,KACdlX,WAAY,MAEtB,KAaI,GAAsDyK,EAAQ,EAA+B,CAC3F,MAAM0M,EAAqC,IAAIlW,IAC/C2V,EAAUQ,yBAA2B,SAASC,EAAU/J,EAAUC,GAChErK,EAAIE,KAAI,KACN,IAAIkU,EACJ,MAAMvB,EAAWoB,EAAmBpX,IAAIsX,GACxC,GAAIJ,KAAKM,eAAexB,GAAW,CACjCxI,EAAW0J,KAAKlB,UACTkB,KAAKlB,EACxB,MAAiB,GAAIa,EAAUW,eAAexB,WAAoBkB,KAAKlB,KAAc,UAC3EkB,KAAKlB,IAAaxI,EAAU,CAC1B,MACZ,MAAiB,GAAIwI,GAAY,KAAM,CAC3B,MAAMxV,EAAUJ,EAAW8W,MAC3B,MAAMO,EAASjX,GAAW,UAAY,EAAIA,EAAQM,EAClD,GAAI2W,KAAYA,EAAS,IAAmCA,EAAS,KAA0BjK,IAAaD,EAAU,CAEpH,MAAMwG,EAA8BvT,EAAQE,EAC5C,MAAMgX,GAASH,EAAM1W,EAAQ2V,IAAe,UAAY,EAAIe,EAAID,GAChEI,GAAS,UAAY,EAAIA,EAAMhO,SAASiO,IACtC,GAAI5D,EAAS4D,IAAiB,KAAM,CAClC5D,EAAS4D,GAAcC,KAAK7D,EAAUvG,EAAUD,EAAU+J,EAC5E,IAEA,CACY,MACZ,CACUJ,KAAKlB,GAAYxI,IAAa,aAAe0J,KAAKlB,KAAc,UAAY,MAAQxI,CAAQ,GAEtG,EACMoJ,EAAKiB,mBAAqB3P,MAAM4P,KACd,IAAIhM,IAAI,IACnBpM,OAAOgJ,MAAMzC,EAAKpF,EAAQ2V,IAAe,KAAOvQ,EAAK,OACrD8Q,EAAQpO,QAAO,EAAEoP,EAAGC,KAAOA,EAAE,GAAK,KAAuBzR,KAAI,EAAEyP,EAAUgC,MAE1E,MAAMV,EAAWU,EAAE,IAAMhC,EACzBoB,EAAmB3W,IAAI6W,EAAUtB,GAIjC,OAAOsB,CAAQ,MAI3B,CACA,CACE,OAAOV,CAAI,EAIb,IAAIqB,GAAsBvD,MAAOlT,EAAKhB,EAASK,EAASoB,KACtD,IAAI2U,EACJ,IAAKpW,EAAQM,EAAU,MAAsC,EAAG,CAC9DN,EAAQM,GAAW,GACnB,MAAMuB,EAAWxB,EAAQyB,EACzB,GAAuDD,EAAU,CAC/D,MAAM6V,EAAalW,EAAWnB,GAC9B,GAAIqX,GAAc,SAAUA,EAAY,CACtC,MAAMC,EAAU9Q,IAIhBuP,QAAasB,EACbC,GACR,KAAa,CACLvB,EAAOsB,CACf,CACM,IAAKtB,EAAM,CACT,MAAM,IAAI5C,MAAM,oBAAoBnT,EAAQsB,KAAa3B,EAAQ4X,mBACzE,CACM,IAAuBxB,EAAKyB,UAAW,CACV,CACzBxX,EAAQ2V,EAAaI,EAAKE,QACpC,CACQH,GAAeC,EAAM/V,EAAS,GAC9B+V,EAAKyB,UAAY,IACzB,CACM,MAAMC,EAAiBpR,EAAW,iBAAkBrG,EAAQsB,GACxC,CAClB3B,EAAQM,GAAW,CAC3B,CACM,IACE,IAAI8V,EAAKpW,EACjB,CAAQ,MAAOmB,GACPD,EAAaC,EACrB,CAC0B,CAClBnB,EAAQM,IAAY,CAC5B,CACiC,CACzBN,EAAQM,GAAW,GAC3B,CACMwX,GAEN,KAAW,CACL1B,EAAOpV,EAAI+W,YACX,MAAMC,EAAShX,EAAIiX,UACnBC,eAAeC,YAAYH,GAAQ/V,MAAK,IAAMjC,EAAQM,GAAW,KACvE,CACI,GAAqB8V,GAAQA,EAAKpY,MAAO,CACvC,IAAIA,EACJ,UAAWoY,EAAKpY,QAAU,SAAU,CAClCA,EAAQoY,EAAKpY,KACrB,CASM,MAAM6M,EAAWK,GAAW7K,GAC5B,IAAK8B,EAAOoJ,IAAIV,GAAW,CACzB,MAAMuN,EAAoB1R,EAAW,iBAAkBrG,EAAQsB,GAK/DiJ,EAAcC,EAAU7M,KAAUqC,EAAQC,EAAU,IACpD8X,GACR,CACA,CACA,CACE,MAAMpF,EAAoBhT,EAAQmT,EAClC,MAAMkF,EAAW,IAAMnF,GAAelT,EAAS,MAC/C,GAA4BgT,GAAqBA,EAAkB,QAAS,CAC1EA,EAAkB,QAAQvO,KAAK4T,EACnC,KAAS,CACLA,GACJ,GAEA,IAAIC,GAAyB/E,IAAD,EAO5B,IAAI1Y,GAAqBmG,IACvB,IAAK2B,EAAIrC,EAAU,KAA+B,EAAG,CACnD,MAAMN,EAAUJ,EAAWoB,GAC3B,MAAMX,EAAUL,EAAQQ,EACxB,MAAM+X,EAAe7R,EAAW,oBAAqBrG,EAAQsB,GAI7D,KAAM3B,EAAQM,EAAU,GAAuB,CAC7CN,EAAQM,GAAW,EAkBO,CACxB,IAAI0S,EAAoBhS,EACxB,MAAOgS,EAAoBA,EAAkBrB,YAAcqB,EAAkBrE,KAAM,CACjF,GAA6JqE,EAAkB,OAAQ,CACrLD,GAAiB/S,EAASA,EAAQmT,EAAsBH,GACxD,KACZ,CACA,CACA,CACM,GAAkD3S,EAAQsV,EAAW,CACnEzW,OAAOsX,QAAQnW,EAAQsV,GAAW5P,KAAI,EAAE9E,GAAawV,OACnD,GAAIA,EAAc,IAAiBzV,EAAIgW,eAAe/V,GAAa,CACjE,MAAMkF,EAAQnF,EAAIC,UACXD,EAAIC,GACXD,EAAIC,GAAckF,CAC9B,IAEA,CAGa,CACLsR,GAAoBzW,EAAKhB,EAASK,EAC1C,CACA,KAAW,CACLmY,GAAsBxX,EAAKhB,EAASK,EAAQoY,GAC5C,GAAIzY,GAAW,UAAY,EAAIA,EAAQE,QAEhC,GAAIF,GAAW,UAAY,EAAIA,EAAQW,EAAkB,CAC9DX,EAAQW,EAAiBsB,MAAK,IAAMqW,MAC5C,CACA,CACIC,GACJ,GAYA,IAAIG,GAAsBnF,IAAD,EAQzB,IAAItY,GAAuBiZ,MAAOlT,IAChC,IAAK2B,EAAIrC,EAAU,KAA+B,EAAG,CACnD,MAAMN,EAAUJ,EAAWoB,GACD,CACxB,GAAIhB,EAAQ2Y,EAAe,CACzB3Y,EAAQ2Y,EAAc5S,KAAK6S,GAAeA,MAC1C5Y,EAAQ2Y,OAAqB,CACrC,CACA,CAGW,GAAI3Y,GAAW,UAAY,EAAIA,EAAQE,QAEvC,GAAIF,GAAW,UAAY,EAAIA,EAAQW,EAAkB,CAC9DX,EAAQW,EAAiBsB,MAAK,IAAMyW,MAC1C,CACA,GA4bG,IAACG,GAAgB,CAACC,EAAaC,EAAU,MAC1C,IAAIvT,EAKJ,MAAMwT,EAAetS,IACrB,MAAMuS,EAAU,GAChB,MAAMC,EAAUH,EAAQG,SAAW,GACnC,MAAMC,EAAkB7W,EAAI4V,eAC5B,MAAMxV,EAAOF,EAAIE,KACjB,MAAM0W,EAA8B1W,EAAKiD,cAAc,iBACvD,MAAM0T,EAA6B7W,EAAIgJ,cAAc,SACrD,MAAM8N,EAA6B,GACnC,IAAIC,EACJ,IAAIC,EAAkB,KACtBta,OAAOua,OAAO9W,EAAKoW,GACnBpW,EAAIC,EAAiB,IAAI8W,IAAIX,EAAQY,cAAgB,KAAMnX,EAAIoX,SAASC,KASxE,IAAIC,EAAoB,MACxBhB,EAAY/S,KAAKgU,IACfA,EAAW,GAAGhU,KAAKiU,IACjB,IAAIjD,EACJ,MAAM1W,EAAU,CACdC,EAAS0Z,EAAY,GACrBrY,EAAWqY,EAAY,GACvBrE,EAAWqE,EAAY,GACvBvB,EAAauB,EAAY,IAE3B,GAAI3Z,EAAQC,EAAU,EAA2B,CAC/CwZ,EAAoB,IAC5B,CAC0B,CAClBzZ,EAAQsV,EAAYqE,EAAY,EACxC,CACgC,CACxB3Z,EAAQoY,EAAcuB,EAAY,EAC1C,CAIiC,CACzB3Z,EAAQ2V,GAAce,EAAMiD,EAAY,KAAO,KAAOjD,EAAM,EACpE,CAIM,MAAMnQ,EAA+GvG,EAAQsB,EAC7H,MAAMsY,EAAc,cAAcC,YAEhC,WAAAnC,CAAYoC,GACVC,MAAMD,GACNzD,KAAK2D,4BAA8B,MACnCF,EAAOzD,KACPvW,EAAaga,EAAM9Z,GACnB,GAAyBA,EAAQC,EAAU,EAAgC,CACrD,CAClB,IAAK6Z,EAAKzN,WAAY,CAMb,CACLyN,EAAKG,aAAa,CAAExd,KAAM,QAC5C,CACA,KAAqB,CACL,GAAIqd,EAAKzN,WAAW5P,OAAS,OAAQ,CACnC,MAAM,IAAI0W,MACR,6CAA6CnT,EAAQsB,qBAA6BwY,EAAKzN,WAAW5P,oDAEtH,CACA,CACA,CAGA,CACA,CACQ,iBAAAjC,GACE,MAAMmF,EAAUJ,EAAW8W,MAC3B,IAAKA,KAAK2D,4BAA6B,CACrC3D,KAAK2D,4BAA8B,KACnC7B,GAAsB9B,KAAM1W,EAASK,EAAQoY,EACzD,CACU,GAAIc,EAAiB,CACnBgB,aAAahB,GACbA,EAAkB,IAC9B,CACU,GAAIC,EAAiB,CACnBF,EAA2B7U,KAAKiS,KAC5C,KAAiB,CACL/T,EAAIE,KAAI,IAAMhI,GAAkB6b,OAC5C,CACA,CACQ,oBAAAzb,GACE0H,EAAIE,KAAI,IAAM5H,GAAqByb,OAC7C,CACQ,gBAAA8D,GACE,OAAO5a,EAAW8W,MAAM/V,CAClC,GA4BMN,EAAQyB,EAAiBiY,EAAW,GACpC,IAAKb,EAAQ1M,SAAS5F,KAAauS,EAAgB3Z,IAAIoH,GAAU,CAC/DqS,EAAQxU,KAAKmC,GACbuS,EAAgBsB,OACd7T,EACAuP,GAAe8D,EAAa5Z,EAAS,GAE/C,IACM,IAEJ,GAAI4Y,EAAQnU,OAAS,EAAG,CACtB,GAAIgV,EAAmB,CACrBT,EAAWpH,aAAe5P,CAChC,CAC+F,CACzFgX,EAAWpH,aAAegH,EAAQyB,OAAStY,CACjD,CACI,GAAIiX,EAAW5N,UAAU3G,OAAQ,CAC/BuU,EAAWzN,aAAa,cAAe,IACvC,MAAMF,GAASlG,EAAK7C,EAAIgJ,IAAY,KAAOnG,EAAKF,EAAyB9C,GACzE,GAAIkJ,GAAS,KAAM,CACjB2N,EAAWzN,aAAa,QAASF,EACzC,CACMhJ,EAAKuJ,aAAaoN,EAAYD,EAAcA,EAAYpN,YAActJ,EAAKiY,WACjF,CACA,CACEnB,EAAkB,MAClB,GAAIF,EAA2BxU,OAAQ,CACrCwU,EAA2BvT,KAAK4I,GAASA,EAAK9T,qBAClD,KAAS,CAGE,CACL8H,EAAIE,KAAI,IAAM0W,EAAkBqB,WAAW7F,GAAY,KAC7D,CACA,CACEiE,GAAc,EAQhB,IAAIR,GAAwB,CAACxX,EAAKhB,EAAS6a,EAAWC,KACpD,GAA4BD,EAAW,CAQrCA,EAAU9U,KAAI,EAAEmE,EAAO3K,EAAM1C,MAC3B,MAAMwC,EAA0E2B,EAChF,MAAM+Z,EAAUC,GAAkBhb,EAASnD,GAC3C,MAAMuG,EAAO6X,GAAiB/Q,GAC9BvH,EAAIM,IAAI5D,EAAQE,EAAMwb,EAAS3X,IAC9BpD,EAAQ2Y,EAAgB3Y,EAAQ2Y,GAAiB,IAAIlU,MAAK,IAAM9B,EAAIW,IAAIjE,EAAQE,EAAMwb,EAAS3X,IAAM,GAE5G,GAEA,IAAI4X,GAAoB,CAAChb,EAAS2T,IAAgBlJ,IAChD,IAAIjF,EACJ,IACwB,CACpB,GAAIxF,EAAQM,EAAU,IAAyB,EAC5CkF,EAAKxF,EAAQE,IAAmB,UAAY,EAAIsF,EAAGmO,GAAYlJ,EACxE,KAAa,EACJzK,EAAQ0T,EAAoB1T,EAAQ0T,GAAqB,IAAIjP,KAAK,CAACkP,EAAYlJ,GACxF,CACA,CAGA,CAAI,MAAOtJ,GACPD,EAAaC,EACjB,GAUA,IAAI8Z,GAAoB/Q,GAAUxG,EAA0B,CAC1DwX,SAAUhR,EAAQ,KAAqB,EACvC0D,SAAU1D,EAAQ,KAAqB,IACpCA,EAAQ,KAAqB,EAG/B,IAACiR,GAAYzP,GAAU/I,EAAIgJ,EAAUD,S", "ignoreList": []}