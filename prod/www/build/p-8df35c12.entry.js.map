{"version": 3, "names": ["eSelectCss", "ESelectStyle0", "ESelect", "componentWillLoad", "this", "parseOptionsString", "init", "parsedOptions", "JSON", "parse", "options", "selectChangeEventEmitter", "emit", "name", "value", "id", "trim", "handleSelectChange", "e", "target", "render", "h", "key", "onChange", "map", "option", "title"], "sources": ["src/components/elements/e-select/e-select.css?tag=e-select&encapsulation=shadow", "src/components/elements/e-select/e-select.tsx"], "sourcesContent": ["select {\n  display: block;\n  width: 100%;\n  border: var(--border);\n  border-radius: var(--border-radius);\n  padding: var(--padding);\n}\n\nselect:hover {\n  cursor: pointer;\n  border: 1px solid var(--color__grey--300);\n}\n", "import { Component, Prop, Event, EventEmitter, h } from '@stencil/core';\n\n@Component({\n  tag: 'e-select',\n  styleUrl: 'e-select.css',\n  shadow: true,\n})\nexport class ESelect {\n  @Event({\n    eventName: 'selectChangeEvent',\n    bubbles: true,\n  })\n  selectChangeEventEmitter: EventEmitter;\n\n  @Prop() options: any;\n  @Prop() name: string;\n\n  private parsedOptions: any;\n\n  componentWillLoad() {\n    this.parseOptionsString();\n    this.init();\n  }\n\n  parseOptionsString() {\n    this.parsedOptions = JSON.parse(this.options);\n  }\n\n  init() {\n    this.selectChangeEventEmitter.emit({\n      name: this.name,\n      value: this.parsedOptions[0].id.trim(),\n    });\n  }\n\n  handleSelectChange(e) {\n    this.selectChangeEventEmitter.emit({\n      name: this.name,\n      value: e.target.value.trim(),\n    });\n  }\n\n  render() {\n    return (\n      <select onChange={e => this.handleSelectChange(e)}>\n        {this.parsedOptions.map(option => (\n          <option value={option.id}>{option.title}</option>\n        ))}\n      </select>\n    );\n  }\n}\n"], "mappings": "kDAAA,MAAMA,EAAa,uLACnB,MAAAC,EAAeD,E,MCMFE,EAAO,M,gIAYlB,iBAAAC,GACEC,KAAKC,qBACLD,KAAKE,M,CAGP,kBAAAD,GACED,KAAKG,cAAgBC,KAAKC,MAAML,KAAKM,Q,CAGvC,IAAAJ,GACEF,KAAKO,yBAAyBC,KAAK,CACjCC,KAAMT,KAAKS,KACXC,MAAOV,KAAKG,cAAc,GAAGQ,GAAGC,Q,CAIpC,kBAAAC,CAAmBC,GACjBd,KAAKO,yBAAyBC,KAAK,CACjCC,KAAMT,KAAKS,KACXC,MAAOI,EAAEC,OAAOL,MAAME,Q,CAI1B,MAAAI,GACE,OACEC,EAAA,UAAAC,IAAA,2CAAQC,SAAUL,GAAKd,KAAKa,mBAAmBC,IAC5Cd,KAAKG,cAAciB,KAAIC,GACtBJ,EAAA,UAAQP,MAAOW,EAAOV,IAAKU,EAAOC,S", "ignoreList": []}