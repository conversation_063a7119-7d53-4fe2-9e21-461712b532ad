import{g as t,f as e}from"./p-59f4c6e6.js";const n=(t,e,n)=>{const r=t.get(e);if(!r){t.set(e,[n])}else if(!r.includes(n)){r.push(n)}};const r=(t,e)=>{let n;return(...r)=>{if(n){clearTimeout(n)}n=setTimeout((()=>{n=0;t(...r)}),e)}};const s=t=>!("isConnected"in t)||t.isConnected;const o=r((t=>{for(let e of t.keys()){t.set(e,t.get(e).filter(s))}}),2e3);const c=()=>{if(typeof t!=="function"){return{}}const r=new Map;return{dispose:()=>r.clear(),get:e=>{const s=t();if(s){n(r,e,s)}},set:t=>{const n=r.get(t);if(n){r.set(t,n.filter(e))}o(r)},reset:()=>{r.forEach((t=>t.forEach(e)));o(r)}}};const i=(t,e=((t,e)=>t!==e))=>{let n=new Map(Object.entries(t!==null&&t!==void 0?t:{}));const r={dispose:[],get:[],set:[],reset:[]};const s=()=>{n=new Map(Object.entries(t!==null&&t!==void 0?t:{}));r.reset.forEach((t=>t()))};const o=()=>{r.dispose.forEach((t=>t()));s()};const c=t=>{r.get.forEach((e=>e(t)));return n.get(t)};const i=(t,s)=>{const o=n.get(t);if(e(s,o,t)){n.set(t,s);r.set.forEach((e=>e(t,s,o)))}};const f=typeof Proxy==="undefined"?{}:new Proxy(t,{get(t,e){return c(e)},ownKeys(t){return Array.from(n.keys())},getOwnPropertyDescriptor(){return{enumerable:true,configurable:true}},has(t,e){return n.has(e)},set(t,e,n){i(e,n);return true}});const u=(t,e)=>{r[t].push(e);return()=>{a(r[t],e)}};const l=(e,n)=>{const r=u("set",((t,r)=>{if(t===e){n(r)}}));const s=u("reset",(()=>n(t[e])));return()=>{r();s()}};const v=(...t)=>{const e=t.reduce(((t,e)=>{if(e.set){t.push(u("set",e.set))}if(e.get){t.push(u("get",e.get))}if(e.reset){t.push(u("reset",e.reset))}if(e.dispose){t.push(u("dispose",e.dispose))}return t}),[]);return()=>e.forEach((t=>t()))};const p=t=>{const e=n.get(t);r.set.forEach((n=>n(t,e,e)))};return{state:f,get:c,set:i,on:u,onChange:l,use:v,dispose:o,reset:s,forceUpdate:p}};const a=(t,e)=>{const n=t.indexOf(e);if(n>=0){t[n]=t[t.length-1];t.length--}};const f=(t,e)=>{const n=i(t,e);n.use(c());return n};const u=t=>{var e;const n=window;const r=new URL(n.location.href);const s=(e=t===null||t===void 0?void 0:t.parseURL)!==null&&e!==void 0?e:p;const{state:o,onChange:c,dispose:i}=f({url:r,activePath:s(r)},((t,e,n)=>{if(n==="url"){return t.href!==e.href}return t!==e}));const a=t=>{history.pushState(null,null,t);const e=new URL(t,document.baseURI);o.url=e;o.activePath=s(e)};const u=t=>{const{activePath:e}=o;for(let n of t){const r=v(e,n.path);if(r){if(n.to!=null){const r=typeof n.to==="string"?n.to:n.to(e);a(r);return u(t)}else{return{params:r,route:n}}}}return undefined};const l=()=>{const t=new URL(n.location.href);o.url=t;o.activePath=s(t)};const d=(t,e)=>{const n=u(e);if(n){if(typeof n.route.jsx==="function"){return n.route.jsx(n.params)}else{return n.route.jsx}}};const g=()=>{n.removeEventListener("popstate",l);i()};const w={Switch:d,get url(){return o.url},get activePath(){return o.activePath},push:a,onChange:c,dispose:g};l();n.addEventListener("popstate",l);return w};const l=(t,e)=>{var n;if("to"in t){return{path:t.path,to:t.to}}return{path:t.path,id:t.id,jsx:(n=t.render)!==null&&n!==void 0?n:e}};const v=(t,e)=>{if(typeof e==="string"){if(e===t){return{}}}else if(typeof e==="function"){const n=e(t);if(n){return n===true?{}:{...n}}}else{const n=e.exec(t);if(n){e.lastIndex=0;return{...n}}}return undefined};const p=t=>t.pathname.toLowerCase();const d="/";const g="./";const w=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");const y=(t,e)=>{var n=[];var r=0;var s=0;var o="";var c=e&&e.delimiter||d;var i=e&&e.delimiters||g;var a=false;var f;while((f=w.exec(t))!==null){var u=f[0];var l=f[1];var v=f.index;o+=t.slice(s,v);s=v+u.length;if(l){o+=l[1];a=true;continue}var p="";var y=t[s];var x=f[2];var $=f[3];var R=f[4];var P=f[5];if(!a&&o.length){var b=o.length-1;if(i.indexOf(o[b])>-1){p=o[b];o=o.slice(0,b)}}if(o){n.push(o);o="";a=false}var j=p!==""&&y!==undefined&&y!==p;var C=P==="+"||P==="*";var E=P==="?"||P==="*";var O=p||c;var U=$||R;n.push({name:x||r++,prefix:p,delimiter:O,optional:E,repeat:C,partial:j,pattern:U?m(U):"[^"+h(O)+"]+?"})}if(o||s<t.length){n.push(o+t.substr(s))}return n};const h=t=>t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1");const m=t=>t.replace(/([=!:$/()])/g,"\\$1");const x=t=>t&&t.sensitive?"":"i";const $=(t,e)=>{if(!e)return t;var n=t.source.match(/\((?!\?)/g);if(n){for(var r=0;r<n.length;r++){e.push({name:r,prefix:null,delimiter:null,optional:false,repeat:false,partial:false,pattern:null})}}return t};const R=(t,e,n)=>{var r=[];for(var s=0;s<t.length;s++){r.push(j(t[s],e,n).source)}return new RegExp("(?:"+r.join("|")+")",x(n))};const P=(t,e,n)=>b(y(t,n),e,n);const b=(t,e,n)=>{n=n||{};var r=n.strict;var s=n.end!==false;var o=h(n.delimiter||d);var c=n.delimiters||g;var i=[].concat(n.endsWith||[]).map(h).concat("$").join("|");var a="";var f=false;for(var u=0;u<t.length;u++){var l=t[u];if(typeof l==="string"){a+=h(l);f=u===t.length-1&&c.indexOf(l[l.length-1])>-1}else{var v=h(l.prefix||"");var p=l.repeat?"(?:"+l.pattern+")(?:"+v+"(?:"+l.pattern+"))*":l.pattern;if(e)e.push(l);if(l.optional){if(l.partial){a+=v+"("+p+")?"}else{a+="(?:"+v+"("+p+"))?"}}else{a+=v+"("+p+")"}}}if(s){if(!r)a+="(?:"+o+")?";a+=i==="$"?"$":"(?="+i+")"}else{if(!r)a+="(?:"+o+"(?="+i+"))?";if(!f)a+="(?="+o+"|"+i+")"}return new RegExp("^"+a,x(n))};const j=(t,e,n)=>{if(t instanceof RegExp){return $(t,e)}if(Array.isArray(t)){return R(t,e,n)}return P(t,e,n)};let C=0;const E={};const O=1e4;const U=(t,e)=>{const n=`${e.end}${e.strict}`;const r=E[n]||(E[n]={});const s=JSON.stringify(t);if(r[s]){return r[s]}const o=[];const c=j(t,o,e);const i={re:c,keys:o};if(C<O){r[s]=i;C+=1}return i};const L=(t,e={})=>{const{exact:n=false,strict:r=false}=e;const{re:s,keys:o}=U(t,{end:n,strict:r});return t=>{const e=s.exec(t);if(!e){return undefined}const[r,...c]=e;const i=t===r;if(n&&!i){return undefined}return o.reduce(((t,e,n)=>{t[e.name]=c[n];return t}),{})}};const M=u();export{M as R,l as a,L as m};
//# sourceMappingURL=p-2f22787f.js.map