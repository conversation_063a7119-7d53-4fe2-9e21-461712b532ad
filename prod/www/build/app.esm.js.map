{"version": 3, "names": ["patchBrowser", "importMeta", "url", "opts", "resourcesUrl", "URL", "href", "promiseResolve", "then", "async", "options", "globalScripts", "bootstrapLazy", "name", "isMailingEmailVerificationCode", "isSessionChecked", "isVerifyingEmail", "compState", "isMobileMenuOpen", "orderId", "isViewDataFetched", "isConfirmAndPayButtonActive", "isConfirmAndPayButtonDisabled", "isLoggingIn", "isSigningUp", "isMailingPasswordResetCode", "isSavingNewPassword", "isDeletingAccount", "sessionId", "service", "activeView", "isExpanded", "wizard<PERSON><PERSON><PERSON>", "currentStageIndex", "surveyType", "surveyTitle", "websiteUrl", "surveyEmbed", "isCreatingSurvey", "stages", "type", "entity", "attribute", "value", "label", "active", "isEditModeOn", "isSaveButtonDisabled", "isSaveButtonActive", "theme", "position", "variant", "clickable", "placeholder", "checked", "isRadioChecked", "action", "size", "disabled", "isActive", "classList", "weight", "src", "width", "justifyContent", "align", "direction"], "sources": ["node_modules/@stencil/core/internal/client/patch-browser.js", "@lazy-browser-entrypoint?app-data=conditional"], "sourcesContent": ["/*\n Stencil Client Patch Browser v4.22.2 | MIT Licensed | https://stenciljs.com\n */\n\n// src/client/client-patch-browser.ts\nimport { BUILD, NAMESPACE } from \"@stencil/core/internal/app-data\";\nimport { consoleDevInfo, doc, H, promiseResolve } from \"@stencil/core\";\nvar patchBrowser = () => {\n  if (BUILD.isDev && !BUILD.isTesting) {\n    consoleDevInfo(\"Running in development mode.\");\n  }\n  if (BUILD.cloneNodeFix) {\n    patchCloneNodeFix(H.prototype);\n  }\n  const scriptElm = BUILD.scriptDataOpts ? Array.from(doc.querySelectorAll(\"script\")).find(\n    (s) => new RegExp(`/${NAMESPACE}(\\\\.esm)?\\\\.js($|\\\\?|#)`).test(s.src) || s.getAttribute(\"data-stencil-namespace\") === NAMESPACE\n  ) : null;\n  const importMeta = import.meta.url;\n  const opts = BUILD.scriptDataOpts ? (scriptElm || {})[\"data-opts\"] || {} : {};\n  if (importMeta !== \"\") {\n    opts.resourcesUrl = new URL(\".\", importMeta).href;\n  }\n  return promiseResolve(opts);\n};\nvar patchCloneNodeFix = (HTMLElementPrototype) => {\n  const nativeCloneNodeFn = HTMLElementPrototype.cloneNode;\n  HTMLElementPrototype.cloneNode = function(deep) {\n    if (this.nodeName === \"TEMPLATE\") {\n      return nativeCloneNodeFn.call(this, deep);\n    }\n    const clonedNode = nativeCloneNodeFn.call(this, false);\n    const srcChildNodes = this.childNodes;\n    if (deep) {\n      for (let i = 0; i < srcChildNodes.length; i++) {\n        if (srcChildNodes[i].nodeType !== 2) {\n          clonedNode.appendChild(srcChildNodes[i].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nexport {\n  patchBrowser\n};\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { patchBrowser } from '@stencil/core/internal/client/patch-browser';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\npatchBrowser().then(async (options) => {\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n});\n"], "mappings": "0HAOA,IAAIA,EAAe,KAUjB,MAAMC,cAAyBC,IAC/B,MAAMC,EAAqE,GAC3E,GAAIF,IAAe,GAAI,CACrBE,EAAKC,aAAe,IAAIC,IAAI,IAAKJ,GAAYK,IACjD,CACE,OAAOC,EAAeJ,EAAK,EClB7BH,IAAeQ,MAAKC,MAAOC,UACnBC,IACN,OAAOC,EAAc,8BAA8B,CAAAF,QAAS,IAAAG,KAAA,sCAAAC,+BAAA,KAAAC,iBAAA,KAAAC,iBAAA,uRAAAC,UAAA,kIAAAC,iBAAA,0FAAAC,QAAA,eAAAC,kBAAA,KAAAC,4BAAA,KAAAC,8BAAA,uEAAAC,YAAA,4GAAAC,YAAA,oHAAAC,2BAAA,KAAAC,oBAAA,KAAAT,UAAA,oHAAAU,kBAAA,wHAAAC,UAAA,iBAAAR,kBAAA,0BAAAS,QAAA,IAAAC,WAAA,8CAAAC,WAAA,wEAAAC,YAAA,KAAAC,kBAAA,KAAAC,WAAA,KAAAC,YAAA,KAAAC,WAAA,KAAAC,YAAA,KAAAC,iBAAA,KAAAC,OAAA,mHAAAC,KAAA,IAAAC,OAAA,IAAAC,UAAA,IAAAC,MAAA,IAAAC,MAAA,IAAAC,OAAA,IAAAC,aAAA,KAAAC,qBAAA,KAAAC,mBAAA,4GAAAC,MAAA,IAAAC,SAAA,2BAAAC,QAAA,kDAAAA,QAAA,mBAAAC,UAAA,oBAAAR,MAAA,IAAAJ,KAAA,IAAA3B,KAAA,IAAAwC,YAAA,IAAAV,MAAA,IAAAW,QAAA,IAAAC,eAAA,YAAAD,QAAA,2CAAAH,QAAA,qBAAAK,OAAA,IAAAb,MAAA,IAAAQ,QAAA,IAAAM,KAAA,IAAAC,SAAA,IAAAb,OAAA,IAAAI,MAAA,IAAAU,SAAA,KAAAC,UAAA,YAAAf,OAAA,wCAAAI,MAAA,mBAAAE,QAAA,IAAAF,MAAA,IAAA/C,IAAA,IAAA2C,OAAA,IAAAc,SAAA,YAAAd,OAAA,qCAAAM,QAAA,IAAAF,MAAA,IAAAY,OAAA,qBAAAlB,MAAA,IAAAQ,QAAA,oBAAAA,QAAA,IAAAW,IAAA,IAAAC,MAAA,kBAAAZ,QAAA,IAAAa,eAAA,sBAAAC,MAAA,IAAAC,UAAA,SAAAxD,EAAA", "ignoreList": []}