import{r as e,c as s,h as t}from"./p-59f4c6e6.js";const r="select{display:block;width:100%;border:var(--border);border-radius:var(--border-radius);padding:var(--padding)}select:hover{cursor:pointer;border:1px solid var(--color__grey--300)}";const i=r;const n=class{constructor(t){e(this,t);this.selectChangeEventEmitter=s(this,"selectChangeEvent",7);this.options=undefined;this.name=undefined}componentWillLoad(){this.parseOptionsString();this.init()}parseOptionsString(){this.parsedOptions=JSON.parse(this.options)}init(){this.selectChangeEventEmitter.emit({name:this.name,value:this.parsedOptions[0].id.trim()})}handleSelectChange(e){this.selectChangeEventEmitter.emit({name:this.name,value:e.target.value.trim()})}render(){return t("select",{key:"cf70cf9afc6c09024810f19d9557b6f512bb0f48",onChange:e=>this.handleSelectChange(e)},this.parsedOptions.map((e=>t("option",{value:e.id},e.title))))}};n.style=i;export{n as e_select};
//# sourceMappingURL=p-8df35c12.entry.js.map