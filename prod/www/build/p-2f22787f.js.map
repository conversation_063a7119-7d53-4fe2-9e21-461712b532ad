{"version": 3, "names": ["appendToMap", "map", "propName", "value", "items", "get", "set", "includes", "push", "debounce", "fn", "ms", "timeoutId", "args", "clearTimeout", "setTimeout", "isConnected", "maybeElement", "cleanupElements", "key", "keys", "filter", "stencilSubscription", "getRenderingRef", "elmsToUpdate", "Map", "dispose", "clear", "elm", "elements", "forceUpdate", "reset", "for<PERSON>ach", "elms", "createObservableMap", "defaultState", "shouldUpdate", "a", "b", "states", "Object", "entries", "handlers", "cb", "oldValue", "state", "Proxy", "_", "ownKeys", "Array", "from", "getOwnPropertyDescriptor", "enumerable", "configurable", "has", "on", "eventName", "callback", "removeFromArray", "onChange", "unSet", "newValue", "unReset", "use", "subscriptions", "unsubs", "reduce", "subscription", "unsub", "array", "item", "index", "indexOf", "length", "createStore", "createRouter", "opts", "_a", "win", "window", "url", "URL", "location", "href", "parseURL", "DEFAULT_PARSE_URL", "activePath", "newV", "oldV", "prop", "history", "pushState", "document", "baseURI", "match", "routes", "route", "params", "matchPath", "path", "to", "undefined", "navigationChanged", "Switch", "childrenRoutes", "result", "jsx", "dispose<PERSON><PERSON><PERSON>", "removeEventListener", "router", "addEventListener", "Route", "props", "children", "id", "render", "pathname", "results", "exec", "lastIndex", "toLowerCase", "DEFAULT_DELIMITER", "DEFAULT_DELIMITERS", "PATH_REGEXP", "RegExp", "join", "parse", "str", "options", "tokens", "defaultDelimiter", "delimiter", "delimiters", "pathEscaped", "res", "m", "escaped", "offset", "slice", "prev", "next", "name", "capture", "group", "modifier", "k", "partial", "repeat", "optional", "pattern", "prefix", "escapeGroup", "escapeString", "substr", "replace", "flags", "sensitive", "regexpToRegexp", "groups", "source", "i", "arrayToRegexp", "parts", "pathToRegexp", "stringToRegexp", "tokensToRegExp", "strict", "end", "endsWith", "concat", "isEndDelimited", "token", "isArray", "cacheCount", "patternCache", "cacheLimit", "compilePath", "cache<PERSON>ey", "cache", "cachePattern", "JSON", "stringify", "re", "compiledPattern", "exact", "values", "isExact", "memo", "Router"], "sources": ["node_modules/stencil-router-v2/node_modules/@stencil/store/dist/index.mjs", "node_modules/stencil-router-v2/dist/index.mjs", "src/index.ts"], "sourcesContent": ["import { getRenderingRef, forceUpdate } from '@stencil/core';\n\nconst appendToMap = (map, propName, value) => {\n    const items = map.get(propName);\n    if (!items) {\n        map.set(propName, [value]);\n    }\n    else if (!items.includes(value)) {\n        items.push(value);\n    }\n};\nconst debounce = (fn, ms) => {\n    let timeoutId;\n    return (...args) => {\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            timeoutId = 0;\n            fn(...args);\n        }, ms);\n    };\n};\n\n/**\n * Check if a possible element isConnected.\n * The property might not be there, so we check for it.\n *\n * We want it to return true if isConnected is not a property,\n * otherwise we would remove these elements and would not update.\n *\n * Better leak in Edge than to be useless.\n */\nconst isConnected = (maybeElement) => !('isConnected' in maybeElement) || maybeElement.isConnected;\nconst cleanupElements = debounce((map) => {\n    for (let key of map.keys()) {\n        map.set(key, map.get(key).filter(isConnected));\n    }\n}, 2000);\nconst stencilSubscription = () => {\n    if (typeof getRenderingRef !== 'function') {\n        // If we are not in a stencil project, we do nothing.\n        // This function is not really exported by @stencil/core.\n        return {};\n    }\n    const elmsToUpdate = new Map();\n    return {\n        dispose: () => elmsToUpdate.clear(),\n        get: (propName) => {\n            const elm = getRenderingRef();\n            if (elm) {\n                appendToMap(elmsToUpdate, propName, elm);\n            }\n        },\n        set: (propName) => {\n            const elements = elmsToUpdate.get(propName);\n            if (elements) {\n                elmsToUpdate.set(propName, elements.filter(forceUpdate));\n            }\n            cleanupElements(elmsToUpdate);\n        },\n        reset: () => {\n            elmsToUpdate.forEach((elms) => elms.forEach(forceUpdate));\n            cleanupElements(elmsToUpdate);\n        },\n    };\n};\n\nconst createObservableMap = (defaultState, shouldUpdate = (a, b) => a !== b) => {\n    let states = new Map(Object.entries(defaultState !== null && defaultState !== void 0 ? defaultState : {}));\n    const handlers = {\n        dispose: [],\n        get: [],\n        set: [],\n        reset: [],\n    };\n    const reset = () => {\n        states = new Map(Object.entries(defaultState !== null && defaultState !== void 0 ? defaultState : {}));\n        handlers.reset.forEach((cb) => cb());\n    };\n    const dispose = () => {\n        // Call first dispose as resetting the state would\n        // cause less updates ;)\n        handlers.dispose.forEach((cb) => cb());\n        reset();\n    };\n    const get = (propName) => {\n        handlers.get.forEach((cb) => cb(propName));\n        return states.get(propName);\n    };\n    const set = (propName, value) => {\n        const oldValue = states.get(propName);\n        if (shouldUpdate(value, oldValue, propName)) {\n            states.set(propName, value);\n            handlers.set.forEach((cb) => cb(propName, value, oldValue));\n        }\n    };\n    const state = (typeof Proxy === 'undefined'\n        ? {}\n        : new Proxy(defaultState, {\n            get(_, propName) {\n                return get(propName);\n            },\n            ownKeys(_) {\n                return Array.from(states.keys());\n            },\n            getOwnPropertyDescriptor() {\n                return {\n                    enumerable: true,\n                    configurable: true,\n                };\n            },\n            has(_, propName) {\n                return states.has(propName);\n            },\n            set(_, propName, value) {\n                set(propName, value);\n                return true;\n            },\n        }));\n    const on = (eventName, callback) => {\n        handlers[eventName].push(callback);\n        return () => {\n            removeFromArray(handlers[eventName], callback);\n        };\n    };\n    const onChange = (propName, cb) => {\n        const unSet = on('set', (key, newValue) => {\n            if (key === propName) {\n                cb(newValue);\n            }\n        });\n        const unReset = on('reset', () => cb(defaultState[propName]));\n        return () => {\n            unSet();\n            unReset();\n        };\n    };\n    const use = (...subscriptions) => {\n        const unsubs = subscriptions.reduce((unsubs, subscription) => {\n            if (subscription.set) {\n                unsubs.push(on('set', subscription.set));\n            }\n            if (subscription.get) {\n                unsubs.push(on('get', subscription.get));\n            }\n            if (subscription.reset) {\n                unsubs.push(on('reset', subscription.reset));\n            }\n            if (subscription.dispose) {\n                unsubs.push(on('dispose', subscription.dispose));\n            }\n            return unsubs;\n        }, []);\n        return () => unsubs.forEach((unsub) => unsub());\n    };\n    const forceUpdate = (key) => {\n        const oldValue = states.get(key);\n        handlers.set.forEach((cb) => cb(key, oldValue, oldValue));\n    };\n    return {\n        state,\n        get,\n        set,\n        on,\n        onChange,\n        use,\n        dispose,\n        reset,\n        forceUpdate,\n    };\n};\nconst removeFromArray = (array, item) => {\n    const index = array.indexOf(item);\n    if (index >= 0) {\n        array[index] = array[array.length - 1];\n        array.length--;\n    }\n};\n\nconst createStore = (defaultState, shouldUpdate) => {\n    const map = createObservableMap(defaultState, shouldUpdate);\n    map.use(stencilSubscription());\n    return map;\n};\n\nexport { createObservableMap, createStore };\n", "import { Build } from '@stencil/core';\nimport { createStore } from '@stencil/store';\n\nlet defaultRouter;\nconst createRouter = (opts) => {\n    var _a;\n    const win = window;\n    const url = new URL(win.location.href);\n    const parseURL = (_a = opts === null || opts === void 0 ? void 0 : opts.parseURL) !== null && _a !== void 0 ? _a : DEFAULT_PARSE_URL;\n    const { state, onChange, dispose } = createStore({\n        url,\n        activePath: parseURL(url)\n    }, (newV, oldV, prop) => {\n        if (prop === 'url') {\n            return newV.href !== oldV.href;\n        }\n        return newV !== oldV;\n    });\n    const push = (href) => {\n        history.pushState(null, null, href);\n        const url = new URL(href, document.baseURI);\n        state.url = url;\n        state.activePath = parseURL(url);\n    };\n    const match = (routes) => {\n        const { activePath } = state;\n        for (let route of routes) {\n            const params = matchPath(activePath, route.path);\n            if (params) {\n                if (route.to != null) {\n                    const to = (typeof route.to === 'string')\n                        ? route.to\n                        : route.to(activePath);\n                    push(to);\n                    return match(routes);\n                }\n                else {\n                    return { params, route };\n                }\n            }\n        }\n        return undefined;\n    };\n    const navigationChanged = () => {\n        const url = new URL(win.location.href);\n        state.url = url;\n        state.activePath = parseURL(url);\n    };\n    const Switch = (_, childrenRoutes) => {\n        const result = match(childrenRoutes);\n        if (result) {\n            if (typeof result.route.jsx === 'function') {\n                return result.route.jsx(result.params);\n            }\n            else {\n                return result.route.jsx;\n            }\n        }\n    };\n    const disposeRouter = () => {\n        defaultRouter = undefined;\n        win.removeEventListener('popstate', navigationChanged);\n        dispose();\n    };\n    const router = defaultRouter = {\n        Switch,\n        get url() {\n            return state.url;\n        },\n        get activePath() {\n            return state.activePath;\n        },\n        push,\n        onChange: onChange,\n        dispose: disposeRouter,\n    };\n    // Initial update\n    navigationChanged();\n    // Listen URL changes\n    win.addEventListener('popstate', navigationChanged);\n    return router;\n};\nconst Route = (props, children) => {\n    var _a;\n    if ('to' in props) {\n        return {\n            path: props.path,\n            to: props.to,\n        };\n    }\n    if (Build.isDev && props.render && children.length > 0) {\n        console.warn('Route: if `render` is provided, the component should not have any children');\n    }\n    return {\n        path: props.path,\n        id: props.id,\n        jsx: (_a = props.render) !== null && _a !== void 0 ? _a : children,\n    };\n};\nconst href = (href, router = defaultRouter) => {\n    if (Build.isDev && !router) {\n        throw new Error('Router must be defined in href');\n    }\n    return {\n        href,\n        onClick: (ev) => {\n            if (ev.metaKey || ev.ctrlKey) {\n                return;\n            }\n            if (ev.which == 2 || ev.button == 1) {\n                return;\n            }\n            ev.preventDefault();\n            router.push(href);\n        },\n    };\n};\nconst matchPath = (pathname, path) => {\n    if (typeof path === 'string') {\n        if (path === pathname) {\n            return {};\n        }\n    }\n    else if (typeof path === 'function') {\n        const params = path(pathname);\n        if (params) {\n            return params === true\n                ? {}\n                : { ...params };\n        }\n    }\n    else {\n        const results = path.exec(pathname);\n        if (results) {\n            path.lastIndex = 0;\n            return { ...results };\n        }\n    }\n    return undefined;\n};\nconst DEFAULT_PARSE_URL = (url) => {\n    return url.pathname.toLowerCase();\n};\nconst NotFound = () => ({});\n\n/**\n * TS adaption of https://github.com/pillarjs/path-to-regexp/blob/master/index.js\n */\n/**\n * Default configs.\n */\nconst DEFAULT_DELIMITER = '/';\nconst DEFAULT_DELIMITERS = './';\n/**\n * The main path matching regexp utility.\n */\nconst PATH_REGEXP = new RegExp([\n    // Match escaped characters that would otherwise appear in future matches.\n    // This allows the user to escape special characters that won't transform.\n    '(\\\\\\\\.)',\n    // Match Express-style parameters and un-named parameters with a prefix\n    // and optional suffixes. Matches appear as:\n    //\n    // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\"]\n    // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined]\n    '(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?'\n].join('|'), 'g');\n/**\n * Parse a string for the raw tokens.\n */\nconst parse = (str, options) => {\n    var tokens = [];\n    var key = 0;\n    var index = 0;\n    var path = '';\n    var defaultDelimiter = (options && options.delimiter) || DEFAULT_DELIMITER;\n    var delimiters = (options && options.delimiters) || DEFAULT_DELIMITERS;\n    var pathEscaped = false;\n    var res;\n    while ((res = PATH_REGEXP.exec(str)) !== null) {\n        var m = res[0];\n        var escaped = res[1];\n        var offset = res.index;\n        path += str.slice(index, offset);\n        index = offset + m.length;\n        // Ignore already escaped sequences.\n        if (escaped) {\n            path += escaped[1];\n            pathEscaped = true;\n            continue;\n        }\n        var prev = '';\n        var next = str[index];\n        var name = res[2];\n        var capture = res[3];\n        var group = res[4];\n        var modifier = res[5];\n        if (!pathEscaped && path.length) {\n            var k = path.length - 1;\n            if (delimiters.indexOf(path[k]) > -1) {\n                prev = path[k];\n                path = path.slice(0, k);\n            }\n        }\n        // Push the current path onto the tokens.\n        if (path) {\n            tokens.push(path);\n            path = '';\n            pathEscaped = false;\n        }\n        var partial = prev !== '' && next !== undefined && next !== prev;\n        var repeat = modifier === '+' || modifier === '*';\n        var optional = modifier === '?' || modifier === '*';\n        var delimiter = prev || defaultDelimiter;\n        var pattern = capture || group;\n        tokens.push({\n            name: name || key++,\n            prefix: prev,\n            delimiter: delimiter,\n            optional: optional,\n            repeat: repeat,\n            partial: partial,\n            pattern: pattern ? escapeGroup(pattern) : '[^' + escapeString(delimiter) + ']+?'\n        });\n    }\n    // Push any remaining characters.\n    if (path || index < str.length) {\n        tokens.push(path + str.substr(index));\n    }\n    return tokens;\n};\n/**\n * Escape a regular expression string.\n */\nconst escapeString = (str) => {\n    return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n};\n/**\n * Escape the capturing group by escaping special characters and meaning.\n */\nconst escapeGroup = (group) => {\n    return group.replace(/([=!:$/()])/g, '\\\\$1');\n};\n/**\n * Get the flags for a regexp from the options.\n */\nconst flags = (options) => {\n    return options && options.sensitive ? '' : 'i';\n};\n/**\n * Pull out keys from a regexp.\n */\nconst regexpToRegexp = (path, keys) => {\n    if (!keys)\n        return path;\n    // Use a negative lookahead to match only capturing groups.\n    var groups = path.source.match(/\\((?!\\?)/g);\n    if (groups) {\n        for (var i = 0; i < groups.length; i++) {\n            keys.push({\n                name: i,\n                prefix: null,\n                delimiter: null,\n                optional: false,\n                repeat: false,\n                partial: false,\n                pattern: null\n            });\n        }\n    }\n    return path;\n};\n/**\n * Transform an array into a regexp.\n */\nconst arrayToRegexp = (path, keys, options) => {\n    var parts = [];\n    for (var i = 0; i < path.length; i++) {\n        parts.push(pathToRegexp(path[i], keys, options).source);\n    }\n    return new RegExp('(?:' + parts.join('|') + ')', flags(options));\n};\n/**\n * Create a path regexp from string input.\n */\nconst stringToRegexp = (path, keys, options) => {\n    return tokensToRegExp(parse(path, options), keys, options);\n};\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\nconst tokensToRegExp = (tokens, keys, options) => {\n    options = options || {};\n    var strict = options.strict;\n    var end = options.end !== false;\n    var delimiter = escapeString(options.delimiter || DEFAULT_DELIMITER);\n    var delimiters = options.delimiters || DEFAULT_DELIMITERS;\n    var endsWith = [].concat(options.endsWith || []).map(escapeString).concat('$').join('|');\n    var route = '';\n    var isEndDelimited = false;\n    // Iterate over the tokens and create our regexp string.\n    for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i];\n        if (typeof token === 'string') {\n            route += escapeString(token);\n            isEndDelimited = i === tokens.length - 1 && delimiters.indexOf(token[token.length - 1]) > -1;\n        }\n        else {\n            var prefix = escapeString(token.prefix || '');\n            var capture = token.repeat\n                ? '(?:' + token.pattern + ')(?:' + prefix + '(?:' + token.pattern + '))*'\n                : token.pattern;\n            if (keys)\n                keys.push(token);\n            if (token.optional) {\n                if (token.partial) {\n                    route += prefix + '(' + capture + ')?';\n                }\n                else {\n                    route += '(?:' + prefix + '(' + capture + '))?';\n                }\n            }\n            else {\n                route += prefix + '(' + capture + ')';\n            }\n        }\n    }\n    if (end) {\n        if (!strict)\n            route += '(?:' + delimiter + ')?';\n        route += endsWith === '$' ? '$' : '(?=' + endsWith + ')';\n    }\n    else {\n        if (!strict)\n            route += '(?:' + delimiter + '(?=' + endsWith + '))?';\n        if (!isEndDelimited)\n            route += '(?=' + delimiter + '|' + endsWith + ')';\n    }\n    return new RegExp('^' + route, flags(options));\n};\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\nconst pathToRegexp = (path, keys, options) => {\n    if (path instanceof RegExp) {\n        return regexpToRegexp(path, keys);\n    }\n    if (Array.isArray(path)) {\n        return arrayToRegexp(path, keys, options);\n    }\n    return stringToRegexp(path, keys, options);\n};\n\nlet cacheCount = 0;\nconst patternCache = {};\nconst cacheLimit = 10000;\n// Memoized function for creating the path match regex\nconst compilePath = (pattern, options) => {\n    const cacheKey = `${options.end}${options.strict}`;\n    const cache = patternCache[cacheKey] || (patternCache[cacheKey] = {});\n    const cachePattern = JSON.stringify(pattern);\n    if (cache[cachePattern]) {\n        return cache[cachePattern];\n    }\n    const keys = [];\n    const re = pathToRegexp(pattern, keys, options);\n    const compiledPattern = { re, keys };\n    if (cacheCount < cacheLimit) {\n        cache[cachePattern] = compiledPattern;\n        cacheCount += 1;\n    }\n    return compiledPattern;\n};\nconst match = (pathname, options = {}) => {\n    const { exact = false, strict = false } = options;\n    const { re, keys } = compilePath(pathname, { end: exact, strict });\n    return (path) => {\n        const match = re.exec(path);\n        if (!match) {\n            return undefined;\n        }\n        const [url, ...values] = match;\n        const isExact = path === url;\n        if (exact && !isExact) {\n            return undefined;\n        }\n        return keys.reduce((memo, key, index) => {\n            memo[key.name] = values[index];\n            return memo;\n        }, {});\n    };\n};\n\nexport { NotFound, Route, createRouter, href, match };\n", "import { createRouter } from 'stencil-router-v2';\n\nexport { Components, JSX } from './components';\n\nexport const Router = createRouter();\n"], "mappings": "2CAEA,MAAMA,EAAc,CAACC,EAAKC,EAAUC,KAChC,MAAMC,EAAQH,EAAII,IAAIH,GACtB,IAAKE,EAAO,CACRH,EAAIK,IAAIJ,EAAU,CAACC,GAC3B,MACS,IAAKC,EAAMG,SAASJ,GAAQ,CAC7BC,EAAMI,KAAKL,EACnB,GAEA,MAAMM,EAAW,CAACC,EAAIC,KAClB,IAAIC,EACJ,MAAO,IAAIC,KACP,GAAID,EAAW,CACXE,aAAaF,EACzB,CACQA,EAAYG,YAAW,KACnBH,EAAY,EACZF,KAAMG,EAAK,GACZF,EAAG,CACT,EAYL,MAAMK,EAAeC,KAAmB,gBAAiBA,IAAiBA,EAAaD,YACvF,MAAME,EAAkBT,GAAUR,IAC9B,IAAK,IAAIkB,KAAOlB,EAAImB,OAAQ,CACxBnB,EAAIK,IAAIa,EAAKlB,EAAII,IAAIc,GAAKE,OAAOL,GACzC,IACG,KACH,MAAMM,EAAsB,KACxB,UAAWC,IAAoB,WAAY,CAGvC,MAAO,EACf,CACI,MAAMC,EAAe,IAAIC,IACzB,MAAO,CACHC,QAAS,IAAMF,EAAaG,QAC5BtB,IAAMH,IACF,MAAM0B,EAAML,IACZ,GAAIK,EAAK,CACL5B,EAAYwB,EAActB,EAAU0B,EACpD,GAEQtB,IAAMJ,IACF,MAAM2B,EAAWL,EAAanB,IAAIH,GAClC,GAAI2B,EAAU,CACVL,EAAalB,IAAIJ,EAAU2B,EAASR,OAAOS,GAC3D,CACYZ,EAAgBM,EAAa,EAEjCO,MAAO,KACHP,EAAaQ,SAASC,GAASA,EAAKD,QAAQF,KAC5CZ,EAAgBM,EAAa,EAEpC,EAGL,MAAMU,EAAsB,CAACC,EAAcC,EAAe,EAACC,EAAGC,IAAMD,IAAMC,MACtE,IAAIC,EAAS,IAAId,IAAIe,OAAOC,QAAQN,IAAiB,MAAQA,SAAsB,EAAIA,EAAe,KACtG,MAAMO,EAAW,CACbhB,QAAS,GACTrB,IAAK,GACLC,IAAK,GACLyB,MAAO,IAEX,MAAMA,EAAQ,KACVQ,EAAS,IAAId,IAAIe,OAAOC,QAAQN,IAAiB,MAAQA,SAAsB,EAAIA,EAAe,KAClGO,EAASX,MAAMC,SAASW,GAAOA,KAAK,EAExC,MAAMjB,EAAU,KAGZgB,EAAShB,QAAQM,SAASW,GAAOA,MACjCZ,GAAO,EAEX,MAAM1B,EAAOH,IACTwC,EAASrC,IAAI2B,SAASW,GAAOA,EAAGzC,KAChC,OAAOqC,EAAOlC,IAAIH,EAAS,EAE/B,MAAMI,EAAM,CAACJ,EAAUC,KACnB,MAAMyC,EAAWL,EAAOlC,IAAIH,GAC5B,GAAIkC,EAAajC,EAAOyC,EAAU1C,GAAW,CACzCqC,EAAOjC,IAAIJ,EAAUC,GACrBuC,EAASpC,IAAI0B,SAASW,GAAOA,EAAGzC,EAAUC,EAAOyC,IAC7D,GAEI,MAAMC,SAAgBC,QAAU,YAC1B,GACA,IAAIA,MAAMX,EAAc,CACtB,GAAA9B,CAAI0C,EAAG7C,GACH,OAAOG,EAAIH,EAC3B,EACY,OAAA8C,CAAQD,GACJ,OAAOE,MAAMC,KAAKX,EAAOnB,OACzC,EACY,wBAAA+B,GACI,MAAO,CACHC,WAAY,KACZC,aAAc,KAElC,EACY,GAAAC,CAAIP,EAAG7C,GACH,OAAOqC,EAAOe,IAAIpD,EAClC,EACY,GAAAI,CAAIyC,EAAG7C,EAAUC,GACbG,EAAIJ,EAAUC,GACd,OAAO,IACvB,IAEI,MAAMoD,EAAK,CAACC,EAAWC,KACnBf,EAASc,GAAWhD,KAAKiD,GACzB,MAAO,KACHC,EAAgBhB,EAASc,GAAYC,EAAS,CACjD,EAEL,MAAME,EAAW,CAACzD,EAAUyC,KACxB,MAAMiB,EAAQL,EAAG,OAAO,CAACpC,EAAK0C,KAC1B,GAAI1C,IAAQjB,EAAU,CAClByC,EAAGkB,EACnB,KAEQ,MAAMC,EAAUP,EAAG,SAAS,IAAMZ,EAAGR,EAAajC,MAClD,MAAO,KACH0D,IACAE,GAAS,CACZ,EAEL,MAAMC,EAAM,IAAIC,KACZ,MAAMC,EAASD,EAAcE,QAAO,CAACD,EAAQE,KACzC,GAAIA,EAAa7D,IAAK,CAClB2D,EAAOzD,KAAK+C,EAAG,MAAOY,EAAa7D,KACnD,CACY,GAAI6D,EAAa9D,IAAK,CAClB4D,EAAOzD,KAAK+C,EAAG,MAAOY,EAAa9D,KACnD,CACY,GAAI8D,EAAapC,MAAO,CACpBkC,EAAOzD,KAAK+C,EAAG,QAASY,EAAapC,OACrD,CACY,GAAIoC,EAAazC,QAAS,CACtBuC,EAAOzD,KAAK+C,EAAG,UAAWY,EAAazC,SACvD,CACY,OAAOuC,CAAM,GACd,IACH,MAAO,IAAMA,EAAOjC,SAASoC,GAAUA,KAAQ,EAEnD,MAAMtC,EAAeX,IACjB,MAAMyB,EAAWL,EAAOlC,IAAIc,GAC5BuB,EAASpC,IAAI0B,SAASW,GAAOA,EAAGxB,EAAKyB,EAAUA,IAAU,EAE7D,MAAO,CACHC,QACAxC,MACAC,MACAiD,KACAI,WACAI,MACArC,UACAK,QACAD,cACH,EAEL,MAAM4B,EAAkB,CAACW,EAAOC,KAC5B,MAAMC,EAAQF,EAAMG,QAAQF,GAC5B,GAAIC,GAAS,EAAG,CACZF,EAAME,GAASF,EAAMA,EAAMI,OAAS,GACpCJ,EAAMI,QACd,GAGA,MAAMC,EAAc,CAACvC,EAAcC,KAC/B,MAAMnC,EAAMiC,EAAoBC,EAAcC,GAC9CnC,EAAI8D,IAAIzC,KACR,OAAOrB,CAAG,ECnLd,MAAM0E,EAAgBC,IAClB,IAAIC,EACJ,MAAMC,EAAMC,OACZ,MAAMC,EAAM,IAAIC,IAAIH,EAAII,SAASC,MACjC,MAAMC,GAAYP,EAAKD,IAAS,MAAQA,SAAc,OAAS,EAAIA,EAAKQ,YAAc,MAAQP,SAAY,EAAIA,EAAKQ,EACnH,MAAMxC,MAAEA,EAAKc,SAAEA,EAAQjC,QAAEA,GAAYgD,EAAY,CAC7CM,MACAM,WAAYF,EAASJ,KACtB,CAACO,EAAMC,EAAMC,KACZ,GAAIA,IAAS,MAAO,CAChB,OAAOF,EAAKJ,OAASK,EAAKL,IACtC,CACQ,OAAOI,IAASC,CAAI,IAExB,MAAMhF,EAAQ2E,IACVO,QAAQC,UAAU,KAAM,KAAMR,GAC9B,MAAMH,EAAM,IAAIC,IAAIE,EAAMS,SAASC,SACnChD,EAAMmC,IAAMA,EACZnC,EAAMyC,WAAaF,EAASJ,EAAI,EAEpC,MAAMc,EAASC,IACX,MAAMT,WAAEA,GAAezC,EACvB,IAAK,IAAImD,KAASD,EAAQ,CACtB,MAAME,EAASC,EAAUZ,EAAYU,EAAMG,MAC3C,GAAIF,EAAQ,CACR,GAAID,EAAMI,IAAM,KAAM,CAClB,MAAMA,SAAaJ,EAAMI,KAAO,SAC1BJ,EAAMI,GACNJ,EAAMI,GAAGd,GACf9E,EAAK4F,GACL,OAAON,EAAMC,EACjC,KACqB,CACD,MAAO,CAAEE,SAAQD,QACrC,CACA,CACA,CACQ,OAAOK,SAAS,EAEpB,MAAMC,EAAoB,KACtB,MAAMtB,EAAM,IAAIC,IAAIH,EAAII,SAASC,MACjCtC,EAAMmC,IAAMA,EACZnC,EAAMyC,WAAaF,EAASJ,EAAI,EAEpC,MAAMuB,EAAS,CAACxD,EAAGyD,KACf,MAAMC,EAASX,EAAMU,GACrB,GAAIC,EAAQ,CACR,UAAWA,EAAOT,MAAMU,MAAQ,WAAY,CACxC,OAAOD,EAAOT,MAAMU,IAAID,EAAOR,OAC/C,KACiB,CACD,OAAOQ,EAAOT,MAAMU,GACpC,CACA,GAEI,MAAMC,EAAgB,KAElB7B,EAAI8B,oBAAoB,WAAYN,GACpC5E,GAAS,EAEb,MAAMmF,EAAyB,CAC3BN,SACA,OAAIvB,GACA,OAAOnC,EAAMmC,GACzB,EACQ,cAAIM,GACA,OAAOzC,EAAMyC,UACzB,EACQ9E,OACAmD,SAAUA,EACVjC,QAASiF,GAGbL,IAEAxB,EAAIgC,iBAAiB,WAAYR,GACjC,OAAOO,CAAM,EAEZ,MAACE,EAAQ,CAACC,EAAOC,KAClB,IAAIpC,EACJ,GAAI,OAAQmC,EAAO,CACf,MAAO,CACHb,KAAMa,EAAMb,KACZC,GAAIY,EAAMZ,GAEtB,CAII,MAAO,CACHD,KAAMa,EAAMb,KACZe,GAAIF,EAAME,GACVR,KAAM7B,EAAKmC,EAAMG,UAAY,MAAQtC,SAAY,EAAIA,EAAKoC,EAC7D,EAoBL,MAAMf,EAAY,CAACkB,EAAUjB,KACzB,UAAWA,IAAS,SAAU,CAC1B,GAAIA,IAASiB,EAAU,CACnB,MAAO,EACnB,CACA,MACS,UAAWjB,IAAS,WAAY,CACjC,MAAMF,EAASE,EAAKiB,GACpB,GAAInB,EAAQ,CACR,OAAOA,IAAW,KACZ,GACA,IAAKA,EACvB,CACA,KACS,CACD,MAAMoB,EAAUlB,EAAKmB,KAAKF,GAC1B,GAAIC,EAAS,CACTlB,EAAKoB,UAAY,EACjB,MAAO,IAAKF,EACxB,CACA,CACI,OAAOhB,SAAS,EAEpB,MAAMhB,EAAqBL,GAChBA,EAAIoC,SAASI,cAUxB,MAAMC,EAAoB,IAC1B,MAAMC,EAAqB,KAI3B,MAAMC,EAAc,IAAIC,OAAO,CAG3B,UAMA,uFACFC,KAAK,KAAM,KAIb,MAAMC,EAAQ,CAACC,EAAKC,KAChB,IAAIC,EAAS,GACb,IAAI9G,EAAM,EACV,IAAIoD,EAAQ,EACZ,IAAI4B,EAAO,GACX,IAAI+B,EAAoBF,GAAWA,EAAQG,WAAcV,EACzD,IAAIW,EAAcJ,GAAWA,EAAQI,YAAeV,EACpD,IAAIW,EAAc,MAClB,IAAIC,EACJ,OAAQA,EAAMX,EAAYL,KAAKS,MAAU,KAAM,CAC3C,IAAIQ,EAAID,EAAI,GACZ,IAAIE,EAAUF,EAAI,GAClB,IAAIG,EAASH,EAAI/D,MACjB4B,GAAQ4B,EAAIW,MAAMnE,EAAOkE,GACzBlE,EAAQkE,EAASF,EAAE9D,OAEnB,GAAI+D,EAAS,CACTrC,GAAQqC,EAAQ,GAChBH,EAAc,KACd,QACZ,CACQ,IAAIM,EAAO,GACX,IAAIC,EAAOb,EAAIxD,GACf,IAAIsE,EAAOP,EAAI,GACf,IAAIQ,EAAUR,EAAI,GAClB,IAAIS,EAAQT,EAAI,GAChB,IAAIU,EAAWV,EAAI,GACnB,IAAKD,GAAelC,EAAK1B,OAAQ,CAC7B,IAAIwE,EAAI9C,EAAK1B,OAAS,EACtB,GAAI2D,EAAW5D,QAAQ2B,EAAK8C,KAAO,EAAG,CAClCN,EAAOxC,EAAK8C,GACZ9C,EAAOA,EAAKuC,MAAM,EAAGO,EACrC,CACA,CAEQ,GAAI9C,EAAM,CACN8B,EAAOzH,KAAK2F,GACZA,EAAO,GACPkC,EAAc,KAC1B,CACQ,IAAIa,EAAUP,IAAS,IAAMC,IAASvC,WAAauC,IAASD,EAC5D,IAAIQ,EAASH,IAAa,KAAOA,IAAa,IAC9C,IAAII,EAAWJ,IAAa,KAAOA,IAAa,IAChD,IAAIb,EAAYQ,GAAQT,EACxB,IAAImB,EAAUP,GAAWC,EACzBd,EAAOzH,KAAK,CACRqI,KAAMA,GAAQ1H,IACdmI,OAAQX,EACRR,UAAWA,EACXiB,SAAUA,EACVD,OAAQA,EACRD,QAASA,EACTG,QAASA,EAAUE,EAAYF,GAAW,KAAOG,EAAarB,GAAa,OAEvF,CAEI,GAAIhC,GAAQ5B,EAAQwD,EAAItD,OAAQ,CAC5BwD,EAAOzH,KAAK2F,EAAO4B,EAAI0B,OAAOlF,GACtC,CACI,OAAO0D,CAAM,EAKjB,MAAMuB,EAAgBzB,GACXA,EAAI2B,QAAQ,4BAA6B,QAKpD,MAAMH,EAAeR,GACVA,EAAMW,QAAQ,eAAgB,QAKzC,MAAMC,EAAS3B,GACJA,GAAWA,EAAQ4B,UAAY,GAAK,IAK/C,MAAMC,EAAiB,CAAC1D,EAAM/E,KAC1B,IAAKA,EACD,OAAO+E,EAEX,IAAI2D,EAAS3D,EAAK4D,OAAOjE,MAAM,aAC/B,GAAIgE,EAAQ,CACR,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOrF,OAAQuF,IAAK,CACpC5I,EAAKZ,KAAK,CACNqI,KAAMmB,EACNV,OAAQ,KACRnB,UAAW,KACXiB,SAAU,MACVD,OAAQ,MACRD,QAAS,MACTG,QAAS,MAEzB,CACA,CACI,OAAOlD,CAAI,EAKf,MAAM8D,EAAgB,CAAC9D,EAAM/E,EAAM4G,KAC/B,IAAIkC,EAAQ,GACZ,IAAK,IAAIF,EAAI,EAAGA,EAAI7D,EAAK1B,OAAQuF,IAAK,CAClCE,EAAM1J,KAAK2J,EAAahE,EAAK6D,GAAI5I,EAAM4G,GAAS+B,OACxD,CACI,OAAO,IAAInC,OAAO,MAAQsC,EAAMrC,KAAK,KAAO,IAAK8B,EAAM3B,GAAS,EAKpE,MAAMoC,EAAiB,CAACjE,EAAM/E,EAAM4G,IACzBqC,EAAevC,EAAM3B,EAAM6B,GAAU5G,EAAM4G,GAKtD,MAAMqC,EAAiB,CAACpC,EAAQ7G,EAAM4G,KAClCA,EAAUA,GAAW,GACrB,IAAIsC,EAAStC,EAAQsC,OACrB,IAAIC,EAAMvC,EAAQuC,MAAQ,MAC1B,IAAIpC,EAAYqB,EAAaxB,EAAQG,WAAaV,GAClD,IAAIW,EAAaJ,EAAQI,YAAcV,EACvC,IAAI8C,EAAW,GAAGC,OAAOzC,EAAQwC,UAAY,IAAIvK,IAAIuJ,GAAciB,OAAO,KAAK5C,KAAK,KACpF,IAAI7B,EAAQ,GACZ,IAAI0E,EAAiB,MAErB,IAAK,IAAIV,EAAI,EAAGA,EAAI/B,EAAOxD,OAAQuF,IAAK,CACpC,IAAIW,EAAQ1C,EAAO+B,GACnB,UAAWW,IAAU,SAAU,CAC3B3E,GAASwD,EAAamB,GACtBD,EAAiBV,IAAM/B,EAAOxD,OAAS,GAAK2D,EAAW5D,QAAQmG,EAAMA,EAAMlG,OAAS,KAAO,CACvG,KACa,CACD,IAAI6E,EAASE,EAAamB,EAAMrB,QAAU,IAC1C,IAAIR,EAAU6B,EAAMxB,OACd,MAAQwB,EAAMtB,QAAU,OAASC,EAAS,MAAQqB,EAAMtB,QAAU,MAClEsB,EAAMtB,QACZ,GAAIjI,EACAA,EAAKZ,KAAKmK,GACd,GAAIA,EAAMvB,SAAU,CAChB,GAAIuB,EAAMzB,QAAS,CACflD,GAASsD,EAAS,IAAMR,EAAU,IACtD,KACqB,CACD9C,GAAS,MAAQsD,EAAS,IAAMR,EAAU,KAC9D,CACA,KACiB,CACD9C,GAASsD,EAAS,IAAMR,EAAU,GAClD,CACA,CACA,CACI,GAAIyB,EAAK,CACL,IAAKD,EACDtE,GAAS,MAAQmC,EAAY,KACjCnC,GAASwE,IAAa,IAAM,IAAM,MAAQA,EAAW,GAC7D,KACS,CACD,IAAKF,EACDtE,GAAS,MAAQmC,EAAY,MAAQqC,EAAW,MACpD,IAAKE,EACD1E,GAAS,MAAQmC,EAAY,IAAMqC,EAAW,GAC1D,CACI,OAAO,IAAI5C,OAAO,IAAM5B,EAAO2D,EAAM3B,GAAS,EASlD,MAAMmC,EAAe,CAAChE,EAAM/E,EAAM4G,KAC9B,GAAI7B,aAAgByB,OAAQ,CACxB,OAAOiC,EAAe1D,EAAM/E,EACpC,CACI,GAAI6B,MAAM2H,QAAQzE,GAAO,CACrB,OAAO8D,EAAc9D,EAAM/E,EAAM4G,EACzC,CACI,OAAOoC,EAAejE,EAAM/E,EAAM4G,EAAQ,EAG9C,IAAI6C,EAAa,EACjB,MAAMC,EAAe,GACrB,MAAMC,EAAa,IAEnB,MAAMC,EAAc,CAAC3B,EAASrB,KAC1B,MAAMiD,EAAW,GAAGjD,EAAQuC,MAAMvC,EAAQsC,SAC1C,MAAMY,EAAQJ,EAAaG,KAAcH,EAAaG,GAAY,IAClE,MAAME,EAAeC,KAAKC,UAAUhC,GACpC,GAAI6B,EAAMC,GAAe,CACrB,OAAOD,EAAMC,EACrB,CACI,MAAM/J,EAAO,GACb,MAAMkK,EAAKnB,EAAad,EAASjI,EAAM4G,GACvC,MAAMuD,EAAkB,CAAED,KAAIlK,QAC9B,GAAIyJ,EAAaE,EAAY,CACzBG,EAAMC,GAAgBI,EACtBV,GAAc,CACtB,CACI,OAAOU,CAAe,EAErB,MAACzF,EAAQ,CAACsB,EAAUY,EAAU,MAC/B,MAAMwD,MAAEA,EAAQ,MAAKlB,OAAEA,EAAS,OAAUtC,EAC1C,MAAMsD,GAAEA,EAAElK,KAAEA,GAAS4J,EAAY5D,EAAU,CAAEmD,IAAKiB,EAAOlB,WACzD,OAAQnE,IACJ,MAAML,EAAQwF,EAAGhE,KAAKnB,GACtB,IAAKL,EAAO,CACR,OAAOO,SACnB,CACQ,MAAOrB,KAAQyG,GAAU3F,EACzB,MAAM4F,EAAUvF,IAASnB,EACzB,GAAIwG,IAAUE,EAAS,CACnB,OAAOrF,SACnB,CACQ,OAAOjF,EAAK8C,QAAO,CAACyH,EAAMxK,EAAKoD,KAC3BoH,EAAKxK,EAAI0H,MAAQ4C,EAAOlH,GACxB,OAAOoH,CAAI,GACZ,GAAG,CACT,E,MCtYQC,EAASjH,W", "ignoreList": []}