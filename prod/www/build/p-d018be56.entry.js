import{g as e,f as t,r as n,h as r,H as s,c as i}from"./p-59f4c6e6.js";import{R as o,a,m as c}from"./p-2f22787f.js";const l=(e,t,n)=>{const r=e.get(t);if(!r){e.set(t,[n])}else if(!r.includes(n)){r.push(n)}};const u=(e,t)=>{let n;return(...r)=>{if(n){clearTimeout(n)}n=setTimeout((()=>{n=0;e(...r)}),t)}};const d=e=>!("isConnected"in e)||e.isConnected;const f=u((e=>{for(let t of e.keys()){e.set(t,e.get(t).filter(d))}}),2e3);const h=()=>{if(typeof e!=="function"){return{}}const n=new Map;return{dispose:()=>n.clear(),get:t=>{const r=e();if(r){l(n,t,r)}},set:e=>{const r=n.get(e);if(r){n.set(e,r.filter(t))}f(n)},reset:()=>{n.forEach((e=>e.forEach(t)));f(n)}}};const b=e=>typeof e==="function"?e():e;const p=(e,t=((e,t)=>e!==t))=>{const n=b(e);let r=new Map(Object.entries(n??{}));const s={dispose:[],get:[],set:[],reset:[]};const i=()=>{r=new Map(Object.entries(b(e)??{}));s.reset.forEach((e=>e()))};const o=()=>{s.dispose.forEach((e=>e()));i()};const a=e=>{s.get.forEach((t=>t(e)));return r.get(e)};const c=(e,n)=>{const i=r.get(e);if(t(n,i,e)){r.set(e,n);s.set.forEach((t=>t(e,n,i)))}};const l=typeof Proxy==="undefined"?{}:new Proxy(n,{get(e,t){return a(t)},ownKeys(e){return Array.from(r.keys())},getOwnPropertyDescriptor(){return{enumerable:true,configurable:true}},has(e,t){return r.has(t)},set(e,t,n){c(t,n);return true}});const u=(e,t)=>{s[e].push(t);return()=>{m(s[e],t)}};const d=(t,n)=>{const r=u("set",((e,r)=>{if(e===t){n(r)}}));const s=u("reset",(()=>n(b(e)[t])));return()=>{r();s()}};const f=(...e)=>{const t=e.reduce(((e,t)=>{if(t.set){e.push(u("set",t.set))}if(t.get){e.push(u("get",t.get))}if(t.reset){e.push(u("reset",t.reset))}if(t.dispose){e.push(u("dispose",t.dispose))}return e}),[]);return()=>t.forEach((e=>e()))};const h=e=>{const t=r.get(e);s.set.forEach((n=>n(e,t,t)))};return{state:l,get:a,set:c,on:u,onChange:d,use:f,dispose:o,reset:i,forceUpdate:h}};const m=(e,t)=>{const n=e.indexOf(t);if(n>=0){e[n]=e[e.length-1];e.length--}};const y=(e,t)=>{const n=p(e,t);n.use(h());return n};const v=y({activeView:"home",isSessionActive:false,accountName:"Tuhin Bhuyan",accountEmail:"",isEmailVerified:true,isWizardActive:false,wizardType:"",currentLocation:"IN",oauthToken:""}).state;const g={app:{name:"Sensefolks",contact:{email:"<EMAIL>",url:"https://sensefolks.com/support"},domain:"sensefolks.com",branding:{logo:{logomark:{withoutBg:"https://res.cloudinary.com/dyygc6dx2/image/upload/v1743454433/logomark_eukmle.svg",withBg:"https://res.cloudinary.com/dyygc6dx2/image/upload/v1743454443/logomark_with_bg_tzpppc.svg"},logotype:{withBg:"https://res.cloudinary.com/dyygc6dx2/image/upload/v1743456084/logotype_with_bg_n9t098.svg",withoutBg:"https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg"}}},policy:{tos:{url:"https://sensefolks.com/policies/terms-of-service"},privacy:{url:"https://sensefolks.com/policies/privacy-policy"},cancellationAndRefund:{url:"https://sensefolks.com/policies/cancellation-and-refund"}},url:"https://sensefolks.com",owner:{name:"Projckt",website:{url:"https://projckt.com"},contact:{address:"Guwahati, Assam, India",email:"<EMAIL>"}}},api:{url:document.domain==="localhost"?"http://localhost:4444":"https://api.sensefolks.com",endpoint:{account:{details:"/account",auth:{login:"/login",logout:"/logout",signup:"/signup",oauth:{google:"/google-oauth"}},password:"/password"},mail:{code:{emailVerification:"/mail-email-verification-code",passwordReset:"/mail-password-reset-code"}},payment:{stripe:{session:{create:"/stripe-create-session",check:"/stripe-check-session"},price:{get:"/stripe-get-price"}}},survey:{create:"/survey",getAll:"/surveys"},verify:{email:"/verify-email"}}},keys:{oauth:{google:{clientId:"************-uqsfujfmi72a80r7u6d4bnhpdrepo7qf.apps.googleusercontent.com"}}}};const w=async e=>{let t=`${g.api.url}${g.api.endpoint.verify.email}`;let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const k=(e,t)=>{let n={email:e,emailVerificationCode:t};return n};var x={exports:{}};(function(e,t){!function(t,n){e.exports=n()}(self,(()=>{return e={7629:(e,t,n)=>{const r=n(375),s=n(8571),i=n(9474),o=n(1687),a=n(8652),c=n(8160),l=n(3292),u=n(6354),d=n(8901),f=n(9708),h=n(6914),b=n(2294),p=n(6133),m=n(1152),y=n(8863),v=n(2036),g={Base:class{constructor(e){this.type=e,this.$_root=null,this._definition={},this._reset()}_reset(){this._ids=new b.Ids,this._preferences=null,this._refs=new p.Manager,this._cache=null,this._valids=null,this._invalids=null,this._flags={},this._rules=[],this._singleRules=new Map,this.$_terms={},this.$_temp={ruleset:null,whens:{}}}describe(){return r("function"==typeof f.describe,"Manifest functionality disabled"),f.describe(this)}allow(...e){return c.verifyFlat(e,"allow"),this._values(e,"_valids")}alter(e){r(e&&"object"==typeof e&&!Array.isArray(e),"Invalid targets argument"),r(!this._inRuleset(),"Cannot set alterations inside a ruleset");const t=this.clone();t.$_terms.alterations=t.$_terms.alterations||[];for(const n in e){const s=e[n];r("function"==typeof s,"Alteration adjuster for",n,"must be a function"),t.$_terms.alterations.push({target:n,adjuster:s})}return t.$_temp.ruleset=!1,t}artifact(e){return r(void 0!==e,"Artifact cannot be undefined"),r(!this._cache,"Cannot set an artifact with a rule cache"),this.$_setFlag("artifact",e)}cast(e){return r(!1===e||"string"==typeof e,"Invalid to value"),r(!1===e||this._definition.cast[e],"Type",this.type,"does not support casting to",e),this.$_setFlag("cast",!1===e?void 0:e)}default(e,t){return this._default("default",e,t)}description(e){return r(e&&"string"==typeof e,"Description must be a non-empty string"),this.$_setFlag("description",e)}empty(e){const t=this.clone();return void 0!==e&&(e=t.$_compile(e,{override:!1})),t.$_setFlag("empty",e,{clone:!1})}error(e){return r(e,"Missing error"),r(e instanceof Error||"function"==typeof e,"Must provide a valid Error object or a function"),this.$_setFlag("error",e)}example(e,t={}){return r(void 0!==e,"Missing example"),c.assertOptions(t,["override"]),this._inner("examples",e,{single:!0,override:t.override})}external(e,t){return"object"==typeof e&&(r(!t,"Cannot combine options with description"),t=e.description,e=e.method),r("function"==typeof e,"Method must be a function"),r(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this._inner("externals",{method:e,description:t},{single:!0})}failover(e,t){return this._default("failover",e,t)}forbidden(){return this.presence("forbidden")}id(e){return e?(r("string"==typeof e,"id must be a non-empty string"),r(/^[^\.]+$/.test(e),"id cannot contain period character"),this.$_setFlag("id",e)):this.$_setFlag("id",void 0)}invalid(...e){return this._values(e,"_invalids")}label(e){return r(e&&"string"==typeof e,"Label name must be a non-empty string"),this.$_setFlag("label",e)}meta(e){return r(void 0!==e,"Meta cannot be undefined"),this._inner("metas",e,{single:!0})}note(...e){r(e.length,"Missing notes");for(const t of e)r(t&&"string"==typeof t,"Notes must be non-empty strings");return this._inner("notes",e)}only(e=!0){return r("boolean"==typeof e,"Invalid mode:",e),this.$_setFlag("only",e)}optional(){return this.presence("optional")}prefs(e){r(e,"Missing preferences"),r(void 0===e.context,"Cannot override context"),r(void 0===e.externals,"Cannot override externals"),r(void 0===e.warnings,"Cannot override warnings"),r(void 0===e.debug,"Cannot override debug"),c.checkPreferences(e);const t=this.clone();return t._preferences=c.preferences(t._preferences,e),t}presence(e){return r(["optional","required","forbidden"].includes(e),"Unknown presence mode",e),this.$_setFlag("presence",e)}raw(e=!0){return this.$_setFlag("result",e?"raw":void 0)}result(e){return r(["raw","strip"].includes(e),"Unknown result mode",e),this.$_setFlag("result",e)}required(){return this.presence("required")}strict(e){const t=this.clone(),n=void 0!==e&&!e;return t._preferences=c.preferences(t._preferences,{convert:n}),t}strip(e=!0){return this.$_setFlag("result",e?"strip":void 0)}tag(...e){r(e.length,"Missing tags");for(const t of e)r(t&&"string"==typeof t,"Tags must be non-empty strings");return this._inner("tags",e)}unit(e){return r(e&&"string"==typeof e,"Unit name must be a non-empty string"),this.$_setFlag("unit",e)}valid(...e){c.verifyFlat(e,"valid");const t=this.allow(...e);return t.$_setFlag("only",!!t._valids,{clone:!1}),t}when(e,t){const n=this.clone();n.$_terms.whens||(n.$_terms.whens=[]);const s=l.when(n,e,t);if(!["any","link"].includes(n.type)){const e=s.is?[s]:s.switch;for(const t of e)r(!t.then||"any"===t.then.type||t.then.type===n.type,"Cannot combine",n.type,"with",t.then&&t.then.type),r(!t.otherwise||"any"===t.otherwise.type||t.otherwise.type===n.type,"Cannot combine",n.type,"with",t.otherwise&&t.otherwise.type)}return n.$_terms.whens.push(s),n.$_mutateRebuild()}cache(e){r(!this._inRuleset(),"Cannot set caching inside a ruleset"),r(!this._cache,"Cannot override schema cache"),r(void 0===this._flags.artifact,"Cannot cache a rule with an artifact");const t=this.clone();return t._cache=e||a.provider.provision(),t.$_temp.ruleset=!1,t}clone(){const e=Object.create(Object.getPrototypeOf(this));return this._assign(e)}concat(e){r(c.isSchema(e),"Invalid schema object"),r("any"===this.type||"any"===e.type||e.type===this.type,"Cannot merge type",this.type,"with another type:",e.type),r(!this._inRuleset(),"Cannot concatenate onto a schema with open ruleset"),r(!e._inRuleset(),"Cannot concatenate a schema with open ruleset");let t=this.clone();if("any"===this.type&&"any"!==e.type){const n=e.clone();for(const e of Object.keys(t))"type"!==e&&(n[e]=t[e]);t=n}t._ids.concat(e._ids),t._refs.register(e,p.toSibling),t._preferences=t._preferences?c.preferences(t._preferences,e._preferences):e._preferences,t._valids=v.merge(t._valids,e._valids,e._invalids),t._invalids=v.merge(t._invalids,e._invalids,e._valids);for(const n of e._singleRules.keys())t._singleRules.has(n)&&(t._rules=t._rules.filter((e=>e.keep||e.name!==n)),t._singleRules.delete(n));for(const n of e._rules)e._definition.rules[n.method].multi||t._singleRules.set(n.name,n),t._rules.push(n);if(t._flags.empty&&e._flags.empty){t._flags.empty=t._flags.empty.concat(e._flags.empty);const n=Object.assign({},e._flags);delete n.empty,o(t._flags,n)}else if(e._flags.empty){t._flags.empty=e._flags.empty;const n=Object.assign({},e._flags);delete n.empty,o(t._flags,n)}else o(t._flags,e._flags);for(const n in e.$_terms){const r=e.$_terms[n];r?t.$_terms[n]?t.$_terms[n]=t.$_terms[n].concat(r):t.$_terms[n]=r.slice():t.$_terms[n]||(t.$_terms[n]=r)}return this.$_root._tracer&&this.$_root._tracer._combine(t,[this,e]),t.$_mutateRebuild()}extend(e){return r(!e.base,"Cannot extend type with another base"),d.type(this,e)}extract(e){return e=Array.isArray(e)?e:e.split("."),this._ids.reach(e)}fork(e,t){r(!this._inRuleset(),"Cannot fork inside a ruleset");let n=this;for(let r of[].concat(e))r=Array.isArray(r)?r:r.split("."),n=n._ids.fork(r,t,n);return n.$_temp.ruleset=!1,n}rule(e){const t=this._definition;c.assertOptions(e,Object.keys(t.modifiers)),r(!1!==this.$_temp.ruleset,"Cannot apply rules to empty ruleset or the last rule added does not support rule properties");const n=null===this.$_temp.ruleset?this._rules.length-1:this.$_temp.ruleset;r(n>=0&&n<this._rules.length,"Cannot apply rules to empty ruleset");const i=this.clone();for(let o=n;o<i._rules.length;++o){const n=i._rules[o],a=s(n);for(const s in e)t.modifiers[s](a,e[s]),r(a.name===n.name,"Cannot change rule name");i._rules[o]=a,i._singleRules.get(a.name)===n&&i._singleRules.set(a.name,a)}return i.$_temp.ruleset=!1,i.$_mutateRebuild()}get ruleset(){r(!this._inRuleset(),"Cannot start a new ruleset without closing the previous one");const e=this.clone();return e.$_temp.ruleset=e._rules.length,e}get $(){return this.ruleset}tailor(e){e=[].concat(e),r(!this._inRuleset(),"Cannot tailor inside a ruleset");let t=this;if(this.$_terms.alterations)for(const{target:n,adjuster:s}of this.$_terms.alterations)e.includes(n)&&(t=s(t),r(c.isSchema(t),"Alteration adjuster for",n,"failed to return a schema object"));return t=t.$_modify({each:t=>t.tailor(e),ref:!1}),t.$_temp.ruleset=!1,t.$_mutateRebuild()}tracer(){return m.location?m.location(this):this}validate(e,t){return y.entry(e,this,t)}validateAsync(e,t){return y.entryAsync(e,this,t)}$_addRule(e){"string"==typeof e&&(e={name:e}),r(e&&"object"==typeof e,"Invalid options"),r(e.name&&"string"==typeof e.name,"Invalid rule name");for(const t in e)r("_"!==t[0],"Cannot set private rule properties");const t=Object.assign({},e);t._resolve=[],t.method=t.method||t.name;const n=this._definition.rules[t.method],s=t.args;r(n,"Unknown rule",t.method);const i=this.clone();if(s){r(1===Object.keys(s).length||Object.keys(s).length===this._definition.rules[t.name].args.length,"Invalid rule definition for",this.type,t.name);for(const e in s){let o=s[e];if(n.argsByName){const a=n.argsByName.get(e);if(a.ref&&c.isResolvable(o))t._resolve.push(e),i.$_mutateRegister(o);else if(a.normalize&&(o=a.normalize(o),s[e]=o),a.assert){const t=c.validateArg(o,e,a);r(!t,t,"or reference")}}void 0!==o?s[e]=o:delete s[e]}}return n.multi||(i._ruleRemove(t.name,{clone:!1}),i._singleRules.set(t.name,t)),!1===i.$_temp.ruleset&&(i.$_temp.ruleset=null),n.priority?i._rules.unshift(t):i._rules.push(t),i}$_compile(e,t){return l.schema(this.$_root,e,t)}$_createError(e,t,n,r,s,i={}){const o=!1!==i.flags?this._flags:{},a=i.messages?h.merge(this._definition.messages,i.messages):this._definition.messages;return new u.Report(e,t,n,o,a,r,s)}$_getFlag(e){return this._flags[e]}$_getRule(e){return this._singleRules.get(e)}$_mapLabels(e){return e=Array.isArray(e)?e:e.split("."),this._ids.labels(e)}$_match(e,t,n,r){(n=Object.assign({},n)).abortEarly=!0,n._externals=!1,t.snapshot();const s=!y.validate(e,this,t,n,r).errors;return t.restore(),s}$_modify(e){return c.assertOptions(e,["each","once","ref","schema"]),b.schema(this,e)||this}$_mutateRebuild(){return r(!this._inRuleset(),"Cannot add this rule inside a ruleset"),this._refs.reset(),this._ids.reset(),this.$_modify({each:(e,{source:t,name:n,path:r,key:s})=>{const i=this._definition[t][n]&&this._definition[t][n].register;!1!==i&&this.$_mutateRegister(e,{family:i,key:s})}}),this._definition.rebuild&&this._definition.rebuild(this),this.$_temp.ruleset=!1,this}$_mutateRegister(e,{family:t,key:n}={}){this._refs.register(e,t),this._ids.register(e,{key:n})}$_property(e){return this._definition.properties[e]}$_reach(e){return this._ids.reach(e)}$_rootReferences(){return this._refs.roots()}$_setFlag(e,t,n={}){r("_"===e[0]||!this._inRuleset(),"Cannot set flag inside a ruleset");const s=this._definition.flags[e]||{};if(i(t,s.default)&&(t=void 0),i(t,this._flags[e]))return this;const o=!1!==n.clone?this.clone():this;return void 0!==t?(o._flags[e]=t,o.$_mutateRegister(t)):delete o._flags[e],"_"!==e[0]&&(o.$_temp.ruleset=!1),o}$_parent(e,...t){return this[e][c.symbols.parent].call(this,...t)}$_validate(e,t,n){return y.validate(e,this,t,n)}_assign(e){e.type=this.type,e.$_root=this.$_root,e.$_temp=Object.assign({},this.$_temp),e.$_temp.whens={},e._ids=this._ids.clone(),e._preferences=this._preferences,e._valids=this._valids&&this._valids.clone(),e._invalids=this._invalids&&this._invalids.clone(),e._rules=this._rules.slice(),e._singleRules=s(this._singleRules,{shallow:!0}),e._refs=this._refs.clone(),e._flags=Object.assign({},this._flags),e._cache=null,e.$_terms={};for(const t in this.$_terms)e.$_terms[t]=this.$_terms[t]?this.$_terms[t].slice():null;e.$_super={};for(const t in this.$_super)e.$_super[t]=this._super[t].bind(e);return e}_bare(){const e=this.clone();e._reset();const t=e._definition.terms;for(const n in t){const r=t[n];e.$_terms[n]=r.init}return e.$_mutateRebuild()}_default(e,t,n={}){return c.assertOptions(n,"literal"),r(void 0!==t,"Missing",e,"value"),r("function"==typeof t||!n.literal,"Only function value supports literal option"),"function"==typeof t&&n.literal&&(t={[c.symbols.literal]:!0,literal:t}),this.$_setFlag(e,t)}_generate(e,t,n){if(!this.$_terms.whens)return{schema:this};const r=[],s=[];for(let i=0;i<this.$_terms.whens.length;++i){const o=this.$_terms.whens[i];if(o.concat){r.push(o.concat),s.push(`${i}.concat`);continue}const a=o.ref?o.ref.resolve(e,t,n):e,c=o.is?[o]:o.switch,l=s.length;for(let l=0;l<c.length;++l){const{is:u,then:d,otherwise:f}=c[l],h=`${i}${o.switch?"."+l:""}`;if(u.$_match(a,t.nest(u,`${h}.is`),n)){if(d){const i=t.localize([...t.path,`${h}.then`],t.ancestors,t.schemas),{schema:o,id:a}=d._generate(e,i,n);r.push(o),s.push(`${h}.then${a?`(${a})`:""}`);break}}else if(f){const i=t.localize([...t.path,`${h}.otherwise`],t.ancestors,t.schemas),{schema:o,id:a}=f._generate(e,i,n);r.push(o),s.push(`${h}.otherwise${a?`(${a})`:""}`);break}}if(o.break&&s.length>l)break}const i=s.join(", ");if(t.mainstay.tracer.debug(t,"rule","when",i),!i)return{schema:this};if(!t.mainstay.tracer.active&&this.$_temp.whens[i])return{schema:this.$_temp.whens[i],id:i};let o=this;this._definition.generate&&(o=this._definition.generate(this,e,t,n));for(const e of r)o=o.concat(e);return this.$_root._tracer&&this.$_root._tracer._combine(o,[this,...r]),this.$_temp.whens[i]=o,{schema:o,id:i}}_inner(e,t,n={}){r(!this._inRuleset(),`Cannot set ${e} inside a ruleset`);const s=this.clone();return s.$_terms[e]&&!n.override||(s.$_terms[e]=[]),n.single?s.$_terms[e].push(t):s.$_terms[e].push(...t),s.$_temp.ruleset=!1,s}_inRuleset(){return null!==this.$_temp.ruleset&&!1!==this.$_temp.ruleset}_ruleRemove(e,t={}){if(!this._singleRules.has(e))return this;const n=!1!==t.clone?this.clone():this;n._singleRules.delete(e);const r=[];for(let t=0;t<n._rules.length;++t){const s=n._rules[t];s.name!==e||s.keep?r.push(s):n._inRuleset()&&t<n.$_temp.ruleset&&--n.$_temp.ruleset}return n._rules=r,n}_values(e,t){c.verifyFlat(e,t.slice(1,-1));const n=this.clone(),s=e[0]===c.symbols.override;if(s&&(e=e.slice(1)),!n[t]&&e.length?n[t]=new v:s&&(n[t]=e.length?new v:null,n.$_mutateRebuild()),!n[t])return n;s&&n[t].override();for(const s of e){r(void 0!==s,"Cannot call allow/valid/invalid with undefined"),r(s!==c.symbols.override,"Override must be the first value");const e="_invalids"===t?"_valids":"_invalids";n[e]&&(n[e].remove(s),n[e].length||(r("_valids"===t||!n._flags.only,"Setting invalid value",s,"leaves schema rejecting all values due to previous valid rule"),n[e]=null)),n[t].add(s,n._refs)}return n}}};g.Base.prototype[c.symbols.any]={version:c.version,compile:l.compile,root:"$_root"},g.Base.prototype.isImmutable=!0,g.Base.prototype.deny=g.Base.prototype.invalid,g.Base.prototype.disallow=g.Base.prototype.invalid,g.Base.prototype.equal=g.Base.prototype.valid,g.Base.prototype.exist=g.Base.prototype.required,g.Base.prototype.not=g.Base.prototype.invalid,g.Base.prototype.options=g.Base.prototype.prefs,g.Base.prototype.preferences=g.Base.prototype.prefs,e.exports=new g.Base},8652:(e,t,n)=>{const r=n(375),s=n(8571),i=n(8160),o={max:1e3,supported:new Set(["undefined","boolean","number","string"])};t.provider={provision:e=>new o.Cache(e)},o.Cache=class{constructor(e={}){i.assertOptions(e,["max"]),r(void 0===e.max||e.max&&e.max>0&&isFinite(e.max),"Invalid max cache size"),this._max=e.max||o.max,this._map=new Map,this._list=new o.List}get length(){return this._map.size}set(e,t){if(null!==e&&!o.supported.has(typeof e))return;let n=this._map.get(e);if(n)return n.value=t,void this._list.first(n);n=this._list.unshift({key:e,value:t}),this._map.set(e,n),this._compact()}get(e){const t=this._map.get(e);if(t)return this._list.first(t),s(t.value)}_compact(){if(this._map.size>this._max){const e=this._list.pop();this._map.delete(e.key)}}},o.List=class{constructor(){this.tail=null,this.head=null}unshift(e){return e.next=null,e.prev=this.head,this.head&&(this.head.next=e),this.head=e,this.tail||(this.tail=e),e}first(e){e!==this.head&&(this._remove(e),this.unshift(e))}pop(){return this._remove(this.tail)}_remove(e){const{next:t,prev:n}=e;return t.prev=n,n&&(n.next=t),e===this.tail&&(this.tail=t),e.prev=null,e.next=null,e}}},8160:(e,t,n)=>{const r=n(375),s=n(7916),i=n(5934);let o,a;const c={isoDate:/^(?:[-+]\d{2})?(?:\d{4}(?!\d{2}\b))(?:(-?)(?:(?:0[1-9]|1[0-2])(?:\1(?:[12]\d|0[1-9]|3[01]))?|W(?:[0-4]\d|5[0-2])(?:-?[1-7])?|(?:00[1-9]|0[1-9]\d|[12]\d{2}|3(?:[0-5]\d|6[1-6])))(?![T]$|[T][\d]+Z$)(?:[T\s](?:(?:(?:[01]\d|2[0-3])(?:(:?)[0-5]\d)?|24\:?00)(?:[.,]\d+(?!:))?)(?:\2[0-5]\d(?:[.,]\d+)?)?(?:[Z]|(?:[+-])(?:[01]\d|2[0-3])(?::?[0-5]\d)?)?)?)?$/};t.version=i.version,t.defaults={abortEarly:!0,allowUnknown:!1,artifacts:!1,cache:!0,context:null,convert:!0,dateFormat:"iso",errors:{escapeHtml:!1,label:"path",language:null,render:!0,stack:!1,wrap:{label:'"',array:"[]"}},externals:!0,messages:{},nonEnumerables:!1,noDefaults:!1,presence:"optional",skipFunctions:!1,stripUnknown:!1,warnings:!1},t.symbols={any:Symbol.for("@hapi/joi/schema"),arraySingle:Symbol("arraySingle"),deepDefault:Symbol("deepDefault"),errors:Symbol("errors"),literal:Symbol("literal"),override:Symbol("override"),parent:Symbol("parent"),prefs:Symbol("prefs"),ref:Symbol("ref"),template:Symbol("template"),values:Symbol("values")},t.assertOptions=function(e,t,n="Options"){r(e&&"object"==typeof e&&!Array.isArray(e),"Options must be of type object");const s=Object.keys(e).filter((e=>!t.includes(e)));r(0===s.length,`${n} contain unknown keys: ${s}`)},t.checkPreferences=function(e){a=a||n(3378);const t=a.preferences.validate(e);if(t.error)throw new s([t.error.details[0].message])},t.compare=function(e,t,n){switch(n){case"=":return e===t;case">":return e>t;case"<":return e<t;case">=":return e>=t;case"<=":return e<=t}},t.default=function(e,t){return void 0===e?t:e},t.isIsoDate=function(e){return c.isoDate.test(e)},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isResolvable=function(e){return!!e&&(e[t.symbols.ref]||e[t.symbols.template])},t.isSchema=function(e,n={}){const s=e&&e[t.symbols.any];return!!s&&(r(n.legacy||s.version===t.version,"Cannot mix different versions of joi schemas"),!0)},t.isValues=function(e){return e[t.symbols.values]},t.limit=function(e){return Number.isSafeInteger(e)&&e>=0},t.preferences=function(e,r){o=o||n(6914),e=e||{},r=r||{};const s=Object.assign({},e,r);return r.errors&&e.errors&&(s.errors=Object.assign({},e.errors,r.errors),s.errors.wrap=Object.assign({},e.errors.wrap,r.errors.wrap)),r.messages&&(s.messages=o.compile(r.messages,e.messages)),delete s[t.symbols.prefs],s},t.tryWithPath=function(e,t,n={}){try{return e()}catch(e){throw void 0!==e.path?e.path=t+"."+e.path:e.path=t,n.append&&(e.message=`${e.message} (${e.path})`),e}},t.validateArg=function(e,n,{assert:r,message:s}){if(t.isSchema(r)){const t=r.validate(e);if(!t.error)return;return t.error.message}if(!r(e))return n?`${n} ${s}`:s},t.verifyFlat=function(e,t){for(const n of e)r(!Array.isArray(n),"Method no longer accepts array arguments:",t)}},3292:(e,t,n)=>{const r=n(375),s=n(8160),i=n(6133),o={};t.schema=function(e,t,n={}){s.assertOptions(n,["appendPath","override"]);try{return o.schema(e,t,n)}catch(e){throw n.appendPath&&void 0!==e.path&&(e.message=`${e.message} (${e.path})`),e}},o.schema=function(e,t,n){r(void 0!==t,"Invalid undefined schema"),Array.isArray(t)&&(r(t.length,"Invalid empty array schema"),1===t.length&&(t=t[0]));const i=(t,...r)=>!1!==n.override?t.valid(e.override,...r):t.valid(...r);if(o.simple(t))return i(e,t);if("function"==typeof t)return e.custom(t);if(r("object"==typeof t,"Invalid schema content:",typeof t),s.isResolvable(t))return i(e,t);if(s.isSchema(t))return t;if(Array.isArray(t)){for(const n of t)if(!o.simple(n))return e.alternatives().try(...t);return i(e,...t)}return t instanceof RegExp?e.string().regex(t):t instanceof Date?i(e.date(),t):(r(Object.getPrototypeOf(t)===Object.getPrototypeOf({}),"Schema can only contain plain objects"),e.object().keys(t))},t.ref=function(e,t){return i.isRef(e)?e:i.create(e,t)},t.compile=function(e,n,i={}){s.assertOptions(i,["legacy"]);const a=n&&n[s.symbols.any];if(a)return r(i.legacy||a.version===s.version,"Cannot mix different versions of joi schemas:",a.version,s.version),n;if("object"!=typeof n||!i.legacy)return t.schema(e,n,{appendPath:!0});const c=o.walk(n);return c?c.compile(c.root,n):t.schema(e,n,{appendPath:!0})},o.walk=function(e){if("object"!=typeof e)return null;if(Array.isArray(e)){for(const t of e){const e=o.walk(t);if(e)return e}return null}const t=e[s.symbols.any];if(t)return{root:e[t.root],compile:t.compile};r(Object.getPrototypeOf(e)===Object.getPrototypeOf({}),"Schema can only contain plain objects");for(const t in e){const n=o.walk(e[t]);if(n)return n}return null},o.simple=function(e){return null===e||["boolean","string","number"].includes(typeof e)},t.when=function(e,n,a){if(void 0===a&&(r(n&&"object"==typeof n,"Missing options"),a=n,n=i.create(".")),Array.isArray(a)&&(a={switch:a}),s.assertOptions(a,["is","not","then","otherwise","switch","break"]),s.isSchema(n))return r(void 0===a.is,'"is" can not be used with a schema condition'),r(void 0===a.not,'"not" can not be used with a schema condition'),r(void 0===a.switch,'"switch" can not be used with a schema condition'),o.condition(e,{is:n,then:a.then,otherwise:a.otherwise,break:a.break});if(r(i.isRef(n)||"string"==typeof n,"Invalid condition:",n),r(void 0===a.not||void 0===a.is,'Cannot combine "is" with "not"'),void 0===a.switch){let c=a;void 0!==a.not&&(c={is:a.not,then:a.otherwise,otherwise:a.then,break:a.break});let l=void 0!==c.is?e.$_compile(c.is):e.$_root.invalid(null,!1,0,"").required();return r(void 0!==c.then||void 0!==c.otherwise,'options must have at least one of "then", "otherwise", or "switch"'),r(void 0===c.break||void 0===c.then||void 0===c.otherwise,"Cannot specify then, otherwise, and break all together"),void 0===a.is||i.isRef(a.is)||s.isSchema(a.is)||(l=l.required()),o.condition(e,{ref:t.ref(n),is:l,then:c.then,otherwise:c.otherwise,break:c.break})}r(Array.isArray(a.switch),'"switch" must be an array'),r(void 0===a.is,'Cannot combine "switch" with "is"'),r(void 0===a.not,'Cannot combine "switch" with "not"'),r(void 0===a.then,'Cannot combine "switch" with "then"');const c={ref:t.ref(n),switch:[],break:a.break};for(let t=0;t<a.switch.length;++t){const n=a.switch[t],o=t===a.switch.length-1;s.assertOptions(n,o?["is","then","otherwise"]:["is","then"]),r(void 0!==n.is,'Switch statement missing "is"'),r(void 0!==n.then,'Switch statement missing "then"');const l={is:e.$_compile(n.is),then:e.$_compile(n.then)};if(i.isRef(n.is)||s.isSchema(n.is)||(l.is=l.is.required()),o){r(void 0===a.otherwise||void 0===n.otherwise,'Cannot specify "otherwise" inside and outside a "switch"');const t=void 0!==a.otherwise?a.otherwise:n.otherwise;void 0!==t&&(r(void 0===c.break,"Cannot specify both otherwise and break"),l.otherwise=e.$_compile(t))}c.switch.push(l)}return c},o.condition=function(e,t){for(const n of["then","otherwise"])void 0===t[n]?delete t[n]:t[n]=e.$_compile(t[n]);return t}},6354:(e,t,n)=>{const r=n(5688),s=n(8160),i=n(3328);t.Report=class{constructor(e,n,r,s,i,o,a){if(this.code=e,this.flags=s,this.messages=i,this.path=o.path,this.prefs=a,this.state=o,this.value=n,this.message=null,this.template=null,this.local=r||{},this.local.label=t.label(this.flags,this.state,this.prefs,this.messages),void 0===this.value||this.local.hasOwnProperty("value")||(this.local.value=this.value),this.path.length){const e=this.path[this.path.length-1];"object"!=typeof e&&(this.local.key=e)}}_setTemplate(e){if(this.template=e,!this.flags.label&&0===this.path.length){const e=this._template(this.template,"root");e&&(this.local.label=e)}}toString(){if(this.message)return this.message;const e=this.code;if(!this.prefs.errors.render)return this.code;const t=this._template(this.template)||this._template(this.prefs.messages)||this._template(this.messages);return void 0===t?`Error code "${e}" is not defined, your custom type is missing the correct messages definition`:(this.message=t.render(this.value,this.state,this.prefs,this.local,{errors:this.prefs.errors,messages:[this.prefs.messages,this.messages]}),this.prefs.errors.label||(this.message=this.message.replace(/^"" /,"").trim()),this.message)}_template(e,n){return t.template(this.value,e,n||this.code,this.state,this.prefs)}},t.path=function(e){let t="";for(const n of e)"object"!=typeof n&&("string"==typeof n?(t&&(t+="."),t+=n):t+=`[${n}]`);return t},t.template=function(e,t,n,r,o){if(!t)return;if(i.isTemplate(t))return"root"!==n?t:null;let a=o.errors.language;if(s.isResolvable(a)&&(a=a.resolve(e,r,o)),a&&t[a]){if(void 0!==t[a][n])return t[a][n];if(void 0!==t[a]["*"])return t[a]["*"]}return t[n]?t[n]:t["*"]},t.label=function(e,n,r,s){if(!r.errors.label)return"";if(e.label)return e.label;let i=n.path;"key"===r.errors.label&&n.path.length>1&&(i=n.path.slice(-1));return t.path(i)||t.template(null,r.messages,"root",n,r)||s&&t.template(null,s,"root",n,r)||"value"},t.process=function(e,n,r){if(!e)return null;const{override:s,message:i,details:o}=t.details(e);if(s)return s;if(r.errors.stack)return new t.ValidationError(i,o,n);const a=Error.stackTraceLimit;Error.stackTraceLimit=0;const c=new t.ValidationError(i,o,n);return Error.stackTraceLimit=a,c},t.details=function(e,t={}){let n=[];const r=[];for(const s of e){if(s instanceof Error){if(!1!==t.override)return{override:s};const e=s.toString();n.push(e),r.push({message:e,type:"override",context:{error:s}});continue}const e=s.toString();n.push(e),r.push({message:e,path:s.path.filter((e=>"object"!=typeof e)),type:s.code,context:s.local})}return n.length>1&&(n=[...new Set(n)]),{message:n.join(". "),details:r}},t.ValidationError=class extends Error{constructor(e,t,n){super(e),this._original=n,this.details=t}static isError(e){return e instanceof t.ValidationError}},t.ValidationError.prototype.isJoi=!0,t.ValidationError.prototype.name="ValidationError",t.ValidationError.prototype.annotate=r.error},8901:(e,t,n)=>{const r=n(375),s=n(8571),i=n(8160),o=n(6914),a={};t.type=function(e,t){const n=Object.getPrototypeOf(e),c=s(n),l=e._assign(Object.create(c)),u=Object.assign({},t);delete u.base,c._definition=u;const d=n._definition||{};u.messages=o.merge(d.messages,u.messages),u.properties=Object.assign({},d.properties,u.properties),l.type=u.type,u.flags=Object.assign({},d.flags,u.flags);const f=Object.assign({},d.terms);if(u.terms)for(const e in u.terms){const t=u.terms[e];r(void 0===l.$_terms[e],"Invalid term override for",u.type,e),l.$_terms[e]=t.init,f[e]=t}u.terms=f,u.args||(u.args=d.args),u.prepare=a.prepare(u.prepare,d.prepare),u.coerce&&("function"==typeof u.coerce&&(u.coerce={method:u.coerce}),u.coerce.from&&!Array.isArray(u.coerce.from)&&(u.coerce={method:u.coerce.method,from:[].concat(u.coerce.from)})),u.coerce=a.coerce(u.coerce,d.coerce),u.validate=a.validate(u.validate,d.validate);const h=Object.assign({},d.rules);if(u.rules)for(const e in u.rules){const t=u.rules[e];r("object"==typeof t,"Invalid rule definition for",u.type,e);let n=t.method;if(void 0===n&&(n=function(){return this.$_addRule(e)}),n&&(r(!c[e],"Rule conflict in",u.type,e),c[e]=n),r(!h[e],"Rule conflict in",u.type,e),h[e]=t,t.alias){const e=[].concat(t.alias);for(const n of e)c[n]=t.method}t.args&&(t.argsByName=new Map,t.args=t.args.map((e=>("string"==typeof e&&(e={name:e}),r(!t.argsByName.has(e.name),"Duplicated argument name",e.name),i.isSchema(e.assert)&&(e.assert=e.assert.strict().label(e.name)),t.argsByName.set(e.name,e),e))))}u.rules=h;const b=Object.assign({},d.modifiers);if(u.modifiers)for(const e in u.modifiers){r(!c[e],"Rule conflict in",u.type,e);const t=u.modifiers[e];r("function"==typeof t,"Invalid modifier definition for",u.type,e);const n=function(t){return this.rule({[e]:t})};c[e]=n,b[e]=t}if(u.modifiers=b,u.overrides){c._super=n,l.$_super={};for(const e in u.overrides)r(n[e],"Cannot override missing",e),u.overrides[e][i.symbols.parent]=n[e],l.$_super[e]=n[e].bind(l);Object.assign(c,u.overrides)}u.cast=Object.assign({},d.cast,u.cast);const p=Object.assign({},d.manifest,u.manifest);return p.build=a.build(u.manifest&&u.manifest.build,d.manifest&&d.manifest.build),u.manifest=p,u.rebuild=a.rebuild(u.rebuild,d.rebuild),l},a.build=function(e,t){return e&&t?function(n,r){return t(e(n,r),r)}:e||t},a.coerce=function(e,t){return e&&t?{from:e.from&&t.from?[...new Set([...e.from,...t.from])]:null,method(n,r){let s;if((!t.from||t.from.includes(typeof n))&&(s=t.method(n,r),s)){if(s.errors||void 0===s.value)return s;n=s.value}if(!e.from||e.from.includes(typeof n)){const t=e.method(n,r);if(t)return t}return s}}:e||t},a.prepare=function(e,t){return e&&t?function(n,r){const s=e(n,r);if(s){if(s.errors||void 0===s.value)return s;n=s.value}return t(n,r)||s}:e||t},a.rebuild=function(e,t){return e&&t?function(n){t(n),e(n)}:e||t},a.validate=function(e,t){return e&&t?function(n,r){const s=t(n,r);if(s){if(s.errors&&(!Array.isArray(s.errors)||s.errors.length))return s;n=s.value}return e(n,r)||s}:e||t}},5107:(e,t,n)=>{const r=n(375),s=n(8571),i=n(8652),o=n(8160),a=n(3292),c=n(6354),l=n(8901),u=n(9708),d=n(6133),f=n(3328),h=n(1152);let b;const p={types:{alternatives:n(4946),any:n(8068),array:n(546),boolean:n(4937),date:n(7500),function:n(390),link:n(8785),number:n(3832),object:n(8966),string:n(7417),symbol:n(8826)},aliases:{alt:"alternatives",bool:"boolean",func:"function"},root:function(){const e={_types:new Set(Object.keys(p.types))};for(const t of e._types)e[t]=function(...e){return r(!e.length||["alternatives","link","object"].includes(t),"The",t,"type does not allow arguments"),p.generate(this,p.types[t],e)};for(const t of["allow","custom","disallow","equal","exist","forbidden","invalid","not","only","optional","options","prefs","preferences","required","strip","valid","when"])e[t]=function(...e){return this.any()[t](...e)};Object.assign(e,p.methods);for(const t in p.aliases){const n=p.aliases[t];e[t]=e[n]}return e.x=e.expression,h.setup&&h.setup(e),e}};p.methods={ValidationError:c.ValidationError,version:o.version,cache:i.provider,assert(e,t,...n){p.assert(e,t,!0,n)},attempt:(e,t,...n)=>p.assert(e,t,!1,n),build(e){return r("function"==typeof u.build,"Manifest functionality disabled"),u.build(this,e)},checkPreferences(e){o.checkPreferences(e)},compile(e,t){return a.compile(this,e,t)},defaults(e){r("function"==typeof e,"modifier must be a function");const t=Object.assign({},this);for(const n of t._types){const s=e(t[n]());r(o.isSchema(s),"modifier must return a valid schema object"),t[n]=function(...e){return p.generate(this,s,e)}}return t},expression:(...e)=>new f(...e),extend(...e){o.verifyFlat(e,"extend"),b=b||n(3378),r(e.length,"You need to provide at least one extension"),this.assert(e,b.extensions);const t=Object.assign({},this);t._types=new Set(t._types);for(let n of e){"function"==typeof n&&(n=n(t)),this.assert(n,b.extension);const e=p.expandExtension(n,t);for(const n of e){r(void 0===t[n.type]||t._types.has(n.type),"Cannot override name",n.type);const e=n.base||this.any(),s=l.type(e,n);t._types.add(n.type),t[n.type]=function(...e){return p.generate(this,s,e)}}}return t},isError:c.ValidationError.isError,isExpression:f.isTemplate,isRef:d.isRef,isSchema:o.isSchema,in:(...e)=>d.in(...e),override:o.symbols.override,ref:(...e)=>d.create(...e),types(){const e={};for(const t of this._types)e[t]=this[t]();for(const t in p.aliases)e[t]=this[t]();return e}},p.assert=function(e,t,n,r){const i=r[0]instanceof Error||"string"==typeof r[0]?r[0]:null,a=null!==i?r[1]:r[0],l=t.validate(e,o.preferences({errors:{stack:!0}},a||{}));let u=l.error;if(!u)return l.value;if(i instanceof Error)throw i;const d=n&&"function"==typeof u.annotate?u.annotate():u.message;throw u instanceof c.ValidationError==0&&(u=s(u)),u.message=i?`${i} ${d}`:d,u},p.generate=function(e,t,n){return r(e,"Must be invoked on a Joi instance."),t.$_root=e,t._definition.args&&n.length?t._definition.args(t,...n):t},p.expandExtension=function(e,t){if("string"==typeof e.type)return[e];const n=[];for(const r of t._types)if(e.type.test(r)){const s=Object.assign({},e);s.type=r,s.base=t[r](),n.push(s)}return n},e.exports=p.root()},6914:(e,t,n)=>{const r=n(375),s=n(8571),i=n(3328);t.compile=function(e,t){if("string"==typeof e)return r(!t,"Cannot set single message string"),new i(e);if(i.isTemplate(e))return r(!t,"Cannot set single message template"),e;r("object"==typeof e&&!Array.isArray(e),"Invalid message options"),t=t?s(t):{};for(let n in e){const s=e[n];if("root"===n||i.isTemplate(s)){t[n]=s;continue}if("string"==typeof s){t[n]=new i(s);continue}r("object"==typeof s&&!Array.isArray(s),"Invalid message for",n);const o=n;for(n in t[o]=t[o]||{},s){const e=s[n];"root"===n||i.isTemplate(e)?t[o][n]=e:(r("string"==typeof e,"Invalid message for",n,"in",o),t[o][n]=new i(e))}}return t},t.decompile=function(e){const t={};for(let n in e){const r=e[n];if("root"===n){t.root=r;continue}if(i.isTemplate(r)){t[n]=r.describe({compact:!0});continue}const s=n;for(n in t[s]={},r){const e=r[n];"root"!==n?t[s][n]=e.describe({compact:!0}):t[s].root=e}}return t},t.merge=function(e,n){if(!e)return t.compile(n);if(!n)return e;if("string"==typeof n)return new i(n);if(i.isTemplate(n))return n;const o=s(e);for(let e in n){const t=n[e];if("root"===e||i.isTemplate(t)){o[e]=t;continue}if("string"==typeof t){o[e]=new i(t);continue}r("object"==typeof t&&!Array.isArray(t),"Invalid message for",e);const s=e;for(e in o[s]=o[s]||{},t){const n=t[e];"root"===e||i.isTemplate(n)?o[s][e]=n:(r("string"==typeof n,"Invalid message for",e,"in",s),o[s][e]=new i(n))}}return o}},2294:(e,t,n)=>{const r=n(375),s=n(8160),i=n(6133),o={};t.Ids=o.Ids=class{constructor(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}clone(){const e=new o.Ids;return e._byId=new Map(this._byId),e._byKey=new Map(this._byKey),e._schemaChain=this._schemaChain,e}concat(e){e._schemaChain&&(this._schemaChain=!0);for(const[t,n]of e._byId.entries())r(!this._byKey.has(t),"Schema id conflicts with existing key:",t),this._byId.set(t,n);for(const[t,n]of e._byKey.entries())r(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,n)}fork(e,t,n){const i=this._collect(e);i.push({schema:n});const a=i.shift();let c={id:a.id,schema:t(a.schema)};r(s.isSchema(c.schema),"adjuster function failed to return a joi schema type");for(const e of i)c={id:e.id,schema:o.fork(e.schema,c.id,c.schema)};return c.schema}labels(e,t=[]){const n=e[0],r=this._get(n);if(!r)return[...t,...e].join(".");const s=e.slice(1);return t=[...t,r.schema._flags.label||n],s.length?r.schema._ids.labels(s,t):t.join(".")}reach(e,t=[]){const n=e[0],s=this._get(n);r(s,"Schema does not contain path",[...t,...e].join("."));const i=e.slice(1);return i.length?s.schema._ids.reach(i,[...t,n]):s.schema}register(e,{key:t}={}){if(!e||!s.isSchema(e))return;(e.$_property("schemaChain")||e._ids._schemaChain)&&(this._schemaChain=!0);const n=e._flags.id;if(n){const t=this._byId.get(n);r(!t||t.schema===e,"Cannot add different schemas with the same id:",n),r(!this._byKey.has(n),"Schema id conflicts with existing key:",n),this._byId.set(n,{schema:e,id:n})}t&&(r(!this._byKey.has(t),"Schema already contains key:",t),r(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,{schema:e,id:t}))}reset(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}_collect(e,t=[],n=[]){const s=e[0],i=this._get(s);r(i,"Schema does not contain path",[...t,...e].join(".")),n=[i,...n];const o=e.slice(1);return o.length?i.schema._ids._collect(o,[...t,s],n):n}_get(e){return this._byId.get(e)||this._byKey.get(e)}},o.fork=function(e,n,r){const s=t.schema(e,{each:(e,{key:t})=>{if(n===(e._flags.id||t))return r},ref:!1});return s?s.$_mutateRebuild():e},t.schema=function(e,t){let n;for(const r in e._flags){if("_"===r[0])continue;const s=o.scan(e._flags[r],{source:"flags",name:r},t);void 0!==s&&(n=n||e.clone(),n._flags[r]=s)}for(let r=0;r<e._rules.length;++r){const s=e._rules[r],i=o.scan(s.args,{source:"rules",name:s.name},t);if(void 0!==i){n=n||e.clone();const t=Object.assign({},s);t.args=i,n._rules[r]=t,n._singleRules.get(s.name)===s&&n._singleRules.set(s.name,t)}}for(const r in e.$_terms){if("_"===r[0])continue;const s=o.scan(e.$_terms[r],{source:"terms",name:r},t);void 0!==s&&(n=n||e.clone(),n.$_terms[r]=s)}return n},o.scan=function(e,t,n,r,a){const c=r||[];if(null===e||"object"!=typeof e)return;let l;if(Array.isArray(e)){for(let r=0;r<e.length;++r){const s="terms"===t.source&&"keys"===t.name&&e[r].key,i=o.scan(e[r],t,n,[r,...c],s);void 0!==i&&(l=l||e.slice(),l[r]=i)}return l}if(!1!==n.schema&&s.isSchema(e)||!1!==n.ref&&i.isRef(e)){const r=n.each(e,{...t,path:c,key:a});if(r===e)return;return r}for(const r in e){if("_"===r[0])continue;const s=o.scan(e[r],t,n,[r,...c],a);void 0!==s&&(l=l||Object.assign({},e),l[r]=s)}return l}},6133:(e,t,n)=>{const r=n(375),s=n(8571),i=n(9621),o=n(8160);let a;const c={symbol:Symbol("ref"),defaults:{adjust:null,in:!1,iterables:null,map:null,separator:".",type:"value"}};t.create=function(e,t={}){r("string"==typeof e,"Invalid reference key:",e),o.assertOptions(t,["adjust","ancestor","in","iterables","map","prefix","render","separator"]),r(!t.prefix||"object"==typeof t.prefix,"options.prefix must be of type object");const n=Object.assign({},c.defaults,t);delete n.prefix;const s=n.separator,i=c.context(e,s,t.prefix);if(n.type=i.type,e=i.key,"value"===n.type)if(i.root&&(r(!s||e[0]!==s,"Cannot specify relative path with root prefix"),n.ancestor="root",e||(e=null)),s&&s===e)e=null,n.ancestor=0;else if(void 0!==n.ancestor)r(!s||!e||e[0]!==s,"Cannot combine prefix with ancestor option");else{const[t,r]=c.ancestor(e,s);r&&""===(e=e.slice(r))&&(e=null),n.ancestor=t}return n.path=s?null===e?[]:e.split(s):[e],new c.Ref(n)},t.in=function(e,n={}){return t.create(e,{...n,in:!0})},t.isRef=function(e){return!!e&&!!e[o.symbols.ref]},c.Ref=class{constructor(e){r("object"==typeof e,"Invalid reference construction"),o.assertOptions(e,["adjust","ancestor","in","iterables","map","path","render","separator","type","depth","key","root","display"]),r([!1,void 0].includes(e.separator)||"string"==typeof e.separator&&1===e.separator.length,"Invalid separator"),r(!e.adjust||"function"==typeof e.adjust,"options.adjust must be a function"),r(!e.map||Array.isArray(e.map),"options.map must be an array"),r(!e.map||!e.adjust,"Cannot set both map and adjust options"),Object.assign(this,c.defaults,e),r("value"===this.type||void 0===this.ancestor,"Non-value references cannot reference ancestors"),Array.isArray(this.map)&&(this.map=new Map(this.map)),this.depth=this.path.length,this.key=this.path.length?this.path.join(this.separator):null,this.root=this.path[0],this.updateDisplay()}resolve(e,t,n,s,i={}){return r(!this.in||i.in,"Invalid in() reference usage"),"global"===this.type?this._resolve(n.context,t,i):"local"===this.type?this._resolve(s,t,i):this.ancestor?"root"===this.ancestor?this._resolve(t.ancestors[t.ancestors.length-1],t,i):(r(this.ancestor<=t.ancestors.length,"Invalid reference exceeds the schema root:",this.display),this._resolve(t.ancestors[this.ancestor-1],t,i)):this._resolve(e,t,i)}_resolve(e,t,n){let r;if("value"===this.type&&t.mainstay.shadow&&!1!==n.shadow&&(r=t.mainstay.shadow.get(this.absolute(t))),void 0===r&&(r=i(e,this.path,{iterables:this.iterables,functions:!0})),this.adjust&&(r=this.adjust(r)),this.map){const e=this.map.get(r);void 0!==e&&(r=e)}return t.mainstay&&t.mainstay.tracer.resolve(t,this,r),r}toString(){return this.display}absolute(e){return[...e.path.slice(0,-this.ancestor),...this.path]}clone(){return new c.Ref(this)}describe(){const e={path:this.path};"value"!==this.type&&(e.type=this.type),"."!==this.separator&&(e.separator=this.separator),"value"===this.type&&1!==this.ancestor&&(e.ancestor=this.ancestor),this.map&&(e.map=[...this.map]);for(const t of["adjust","iterables","render"])null!==this[t]&&void 0!==this[t]&&(e[t]=this[t]);return!1!==this.in&&(e.in=!0),{ref:e}}updateDisplay(){const e=null!==this.key?this.key:"";if("value"!==this.type)return void(this.display=`ref:${this.type}:${e}`);if(!this.separator)return void(this.display=`ref:${e}`);if(!this.ancestor)return void(this.display=`ref:${this.separator}${e}`);if("root"===this.ancestor)return void(this.display=`ref:root:${e}`);if(1===this.ancestor)return void(this.display=`ref:${e||".."}`);const t=new Array(this.ancestor+1).fill(this.separator).join("");this.display=`ref:${t}${e||""}`}},c.Ref.prototype[o.symbols.ref]=!0,t.build=function(e){return"value"===(e=Object.assign({},c.defaults,e)).type&&void 0===e.ancestor&&(e.ancestor=1),new c.Ref(e)},c.context=function(e,t,n={}){if(e=e.trim(),n){const r=void 0===n.global?"$":n.global;if(r!==t&&e.startsWith(r))return{key:e.slice(r.length),type:"global"};const s=void 0===n.local?"#":n.local;if(s!==t&&e.startsWith(s))return{key:e.slice(s.length),type:"local"};const i=void 0===n.root?"/":n.root;if(i!==t&&e.startsWith(i))return{key:e.slice(i.length),type:"value",root:!0}}return{key:e,type:"value"}},c.ancestor=function(e,t){if(!t)return[1,0];if(e[0]!==t)return[1,0];if(e[1]!==t)return[0,1];let n=2;for(;e[n]===t;)++n;return[n-1,n]},t.toSibling=0,t.toParent=1,t.Manager=class{constructor(){this.refs=[]}register(e,r){if(e)if(r=void 0===r?t.toParent:r,Array.isArray(e))for(const t of e)this.register(t,r);else if(o.isSchema(e))for(const t of e._refs.refs)t.ancestor-r>=0&&this.refs.push({ancestor:t.ancestor-r,root:t.root});else t.isRef(e)&&"value"===e.type&&e.ancestor-r>=0&&this.refs.push({ancestor:e.ancestor-r,root:e.root}),a=a||n(3328),a.isTemplate(e)&&this.register(e.refs(),r)}get length(){return this.refs.length}clone(){const e=new t.Manager;return e.refs=s(this.refs),e}reset(){this.refs=[]}roots(){return this.refs.filter((e=>!e.ancestor)).map((e=>e.root))}}},3378:(e,t,n)=>{const r=n(5107),s={};s.wrap=r.string().min(1).max(2).allow(!1),t.preferences=r.object({allowUnknown:r.boolean(),abortEarly:r.boolean(),artifacts:r.boolean(),cache:r.boolean(),context:r.object(),convert:r.boolean(),dateFormat:r.valid("date","iso","string","time","utc"),debug:r.boolean(),errors:{escapeHtml:r.boolean(),label:r.valid("path","key",!1),language:[r.string(),r.object().ref()],render:r.boolean(),stack:r.boolean(),wrap:{label:s.wrap,array:s.wrap,string:s.wrap}},externals:r.boolean(),messages:r.object(),noDefaults:r.boolean(),nonEnumerables:r.boolean(),presence:r.valid("required","optional","forbidden"),skipFunctions:r.boolean(),stripUnknown:r.object({arrays:r.boolean(),objects:r.boolean()}).or("arrays","objects").allow(!0,!1),warnings:r.boolean()}).strict(),s.nameRx=/^[a-zA-Z0-9]\w*$/,s.rule=r.object({alias:r.array().items(r.string().pattern(s.nameRx)).single(),args:r.array().items(r.string(),r.object({name:r.string().pattern(s.nameRx).required(),ref:r.boolean(),assert:r.alternatives([r.function(),r.object().schema()]).conditional("ref",{is:!0,then:r.required()}),normalize:r.function(),message:r.string().when("assert",{is:r.function(),then:r.required()})})),convert:r.boolean(),manifest:r.boolean(),method:r.function().allow(!1),multi:r.boolean(),validate:r.function()}),t.extension=r.object({type:r.alternatives([r.string(),r.object().regex()]).required(),args:r.function(),cast:r.object().pattern(s.nameRx,r.object({from:r.function().maxArity(1).required(),to:r.function().minArity(1).maxArity(2).required()})),base:r.object().schema().when("type",{is:r.object().regex(),then:r.forbidden()}),coerce:[r.function().maxArity(3),r.object({method:r.function().maxArity(3).required(),from:r.array().items(r.string()).single()})],flags:r.object().pattern(s.nameRx,r.object({setter:r.string(),default:r.any()})),manifest:{build:r.function().arity(2)},messages:[r.object(),r.string()],modifiers:r.object().pattern(s.nameRx,r.function().minArity(1).maxArity(2)),overrides:r.object().pattern(s.nameRx,r.function()),prepare:r.function().maxArity(3),rebuild:r.function().arity(1),rules:r.object().pattern(s.nameRx,s.rule),terms:r.object().pattern(s.nameRx,r.object({init:r.array().allow(null).required(),manifest:r.object().pattern(/.+/,[r.valid("schema","single"),r.object({mapped:r.object({from:r.string().required(),to:r.string().required()}).required()})])})),validate:r.function().maxArity(3)}).strict(),t.extensions=r.array().items(r.object(),r.function().arity(1)).strict(),s.desc={buffer:r.object({buffer:r.string()}),func:r.object({function:r.function().required(),options:{literal:!0}}),override:r.object({override:!0}),ref:r.object({ref:r.object({type:r.valid("value","global","local"),path:r.array().required(),separator:r.string().length(1).allow(!1),ancestor:r.number().min(0).integer().allow("root"),map:r.array().items(r.array().length(2)).min(1),adjust:r.function(),iterables:r.boolean(),in:r.boolean(),render:r.boolean()}).required()}),regex:r.object({regex:r.string().min(3)}),special:r.object({special:r.valid("deep").required()}),template:r.object({template:r.string().required(),options:r.object()}),value:r.object({value:r.alternatives([r.object(),r.array()]).required()})},s.desc.entity=r.alternatives([r.array().items(r.link("...")),r.boolean(),r.function(),r.number(),r.string(),s.desc.buffer,s.desc.func,s.desc.ref,s.desc.regex,s.desc.special,s.desc.template,s.desc.value,r.link("/")]),s.desc.values=r.array().items(null,r.boolean(),r.function(),r.number().allow(1/0,-1/0),r.string().allow(""),r.symbol(),s.desc.buffer,s.desc.func,s.desc.override,s.desc.ref,s.desc.regex,s.desc.template,s.desc.value),s.desc.messages=r.object().pattern(/.+/,[r.string(),s.desc.template,r.object().pattern(/.+/,[r.string(),s.desc.template])]),t.description=r.object({type:r.string().required(),flags:r.object({cast:r.string(),default:r.any(),description:r.string(),empty:r.link("/"),failover:s.desc.entity,id:r.string(),label:r.string(),only:!0,presence:["optional","required","forbidden"],result:["raw","strip"],strip:r.boolean(),unit:r.string()}).unknown(),preferences:{allowUnknown:r.boolean(),abortEarly:r.boolean(),artifacts:r.boolean(),cache:r.boolean(),convert:r.boolean(),dateFormat:["date","iso","string","time","utc"],errors:{escapeHtml:r.boolean(),label:["path","key"],language:[r.string(),s.desc.ref],wrap:{label:s.wrap,array:s.wrap}},externals:r.boolean(),messages:s.desc.messages,noDefaults:r.boolean(),nonEnumerables:r.boolean(),presence:["required","optional","forbidden"],skipFunctions:r.boolean(),stripUnknown:r.object({arrays:r.boolean(),objects:r.boolean()}).or("arrays","objects").allow(!0,!1),warnings:r.boolean()},allow:s.desc.values,invalid:s.desc.values,rules:r.array().min(1).items({name:r.string().required(),args:r.object().min(1),keep:r.boolean(),message:[r.string(),s.desc.messages],warn:r.boolean()}),keys:r.object().pattern(/.*/,r.link("/")),link:s.desc.ref}).pattern(/^[a-z]\w*$/,r.any())},493:(e,t,n)=>{const r=n(8571),s=n(9621),i=n(8160),o={value:Symbol("value")};e.exports=o.State=class{constructor(e,t,n){this.path=e,this.ancestors=t,this.mainstay=n.mainstay,this.schemas=n.schemas,this.debug=null}localize(e,t=null,n=null){const r=new o.State(e,t,this);return n&&r.schemas&&(r.schemas=[o.schemas(n),...r.schemas]),r}nest(e,t){const n=new o.State(this.path,this.ancestors,this);return n.schemas=n.schemas&&[o.schemas(e),...n.schemas],n.debug=t,n}shadow(e,t){this.mainstay.shadow=this.mainstay.shadow||new o.Shadow,this.mainstay.shadow.set(this.path,e,t)}snapshot(){this.mainstay.shadow&&(this._snapshot=r(this.mainstay.shadow.node(this.path))),this.mainstay.snapshot()}restore(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.restore()}commit(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.commit()}},o.schemas=function(e){return i.isSchema(e)?{schema:e}:e},o.Shadow=class{constructor(){this._values=null}set(e,t,n){if(!e.length)return;if("strip"===n&&"number"==typeof e[e.length-1])return;this._values=this._values||new Map;let r=this._values;for(let t=0;t<e.length;++t){const n=e[t];let s=r.get(n);s||(s=new Map,r.set(n,s)),r=s}r[o.value]=t}get(e){const t=this.node(e);if(t)return t[o.value]}node(e){if(this._values)return s(this._values,e,{iterables:!0})}override(e,t){if(!this._values)return;const n=e.slice(0,-1),r=e[e.length-1],i=s(this._values,n,{iterables:!0});t?i.set(r,t):i&&i.delete(r)}}},3328:(e,t,n)=>{const r=n(375),s=n(8571),i=n(5277),o=n(1447),a=n(8160),c=n(6354),l=n(6133),u={symbol:Symbol("template"),opens:new Array(1e3).join("\0"),closes:new Array(1e3).join(""),dateFormat:{date:Date.prototype.toDateString,iso:Date.prototype.toISOString,string:Date.prototype.toString,time:Date.prototype.toTimeString,utc:Date.prototype.toUTCString}};e.exports=u.Template=class{constructor(e,t){if(r("string"==typeof e,"Template source must be a string"),r(!e.includes("\0")&&!e.includes(""),"Template source cannot contain reserved control characters"),this.source=e,this.rendered=e,this._template=null,t){const{functions:e,...n}=t;this._settings=Object.keys(n).length?s(n):void 0,this._functions=e,this._functions&&(r(Object.keys(this._functions).every((e=>"string"==typeof e)),"Functions keys must be strings"),r(Object.values(this._functions).every((e=>"function"==typeof e)),"Functions values must be functions"))}else this._settings=void 0,this._functions=void 0;this._parse()}_parse(){if(!this.source.includes("{"))return;const e=u.encode(this.source),t=u.split(e);let n=!1;const r=[],s=t.shift();s&&r.push(s);for(const e of t){const t="{"!==e[0],s=t?"}":"}}",i=e.indexOf(s);if(-1===i||"{"===e[1]){r.push(`{${u.decode(e)}`);continue}let o=e.slice(t?0:1,i);const a=":"===o[0];a&&(o=o.slice(1));const c=this._ref(u.decode(o),{raw:t,wrapped:a});r.push(c),"string"!=typeof c&&(n=!0);const l=e.slice(i+s.length);l&&r.push(u.decode(l))}n?this._template=r:this.rendered=r.join("")}static date(e,t){return u.dateFormat[t.dateFormat].call(e)}describe(e={}){if(!this._settings&&e.compact)return this.source;const t={template:this.source};return this._settings&&(t.options=this._settings),this._functions&&(t.functions=this._functions),t}static build(e){return new u.Template(e.template,e.options||e.functions?{...e.options,functions:e.functions}:void 0)}isDynamic(){return!!this._template}static isTemplate(e){return!!e&&!!e[a.symbols.template]}refs(){if(!this._template)return;const e=[];for(const t of this._template)"string"!=typeof t&&e.push(...t.refs);return e}resolve(e,t,n,r){return this._template&&1===this._template.length?this._part(this._template[0],e,t,n,r,{}):this.render(e,t,n,r)}_part(e,...t){return e.ref?e.ref.resolve(...t):e.formula.evaluate(t)}render(e,t,n,r,s={}){if(!this.isDynamic())return this.rendered;const o=[];for(const a of this._template)if("string"==typeof a)o.push(a);else{const c=this._part(a,e,t,n,r,s),l=u.stringify(c,e,t,n,r,s);if(void 0!==l){const e=a.raw||!1===(s.errors&&s.errors.escapeHtml)?l:i(l);o.push(u.wrap(e,a.wrapped&&n.errors.wrap.label))}}return o.join("")}_ref(e,{raw:t,wrapped:n}){const r=[],s=e=>{const t=l.create(e,this._settings);return r.push(t),e=>{const n=t.resolve(...e);return void 0!==n?n:null}};try{const t=this._functions?{...u.functions,...this._functions}:u.functions;var i=new o.Parser(e,{reference:s,functions:t,constants:u.constants})}catch(t){throw t.message=`Invalid template variable "${e}" fails due to: ${t.message}`,t}if(i.single){if("reference"===i.single.type){const e=r[0];return{ref:e,raw:t,refs:r,wrapped:n||"local"===e.type&&"label"===e.key}}return u.stringify(i.single.value)}return{formula:i,raw:t,refs:r}}toString(){return this.source}},u.Template.prototype[a.symbols.template]=!0,u.Template.prototype.isImmutable=!0,u.encode=function(e){return e.replace(/\\(\{+)/g,((e,t)=>u.opens.slice(0,t.length))).replace(/\\(\}+)/g,((e,t)=>u.closes.slice(0,t.length)))},u.decode=function(e){return e.replace(/\u0000/g,"{").replace(/\u0001/g,"}")},u.split=function(e){const t=[];let n="";for(let r=0;r<e.length;++r){const s=e[r];if("{"===s){let s="";for(;r+1<e.length&&"{"===e[r+1];)s+="{",++r;t.push(n),n=s}else n+=s}return t.push(n),t},u.wrap=function(e,t){return t?1===t.length?`${t}${e}${t}`:`${t[0]}${e}${t[1]}`:e},u.stringify=function(e,t,n,r,s,i={}){const o=typeof e,a=r&&r.errors&&r.errors.wrap||{};let c=!1;if(l.isRef(e)&&e.render&&(c=e.in,e=e.resolve(t,n,r,s,{in:e.in,...i})),null===e)return"null";if("string"===o)return u.wrap(e,i.arrayItems&&a.string);if("number"===o||"function"===o||"symbol"===o)return e.toString();if("object"!==o)return JSON.stringify(e);if(e instanceof Date)return u.Template.date(e,r);if(e instanceof Map){const t=[];for(const[n,r]of e.entries())t.push(`${n.toString()} -> ${r.toString()}`);e=t}if(!Array.isArray(e))return e.toString();const d=[];for(const o of e)d.push(u.stringify(o,t,n,r,s,{arrayItems:!0,...i}));return u.wrap(d.join(", "),!c&&a.array)},u.constants={true:!0,false:!1,null:null,second:1e3,minute:6e4,hour:36e5,day:864e5},u.functions={if:(e,t,n)=>e?t:n,length:e=>"string"==typeof e?e.length:e&&"object"==typeof e?Array.isArray(e)?e.length:Object.keys(e).length:null,msg(e){const[t,n,r,s,i]=this,o=i.messages;if(!o)return"";const a=c.template(t,o[0],e,n,r)||c.template(t,o[1],e,n,r);return a?a.render(t,n,r,s,i):""},number:e=>"number"==typeof e?e:"string"==typeof e?parseFloat(e):"boolean"==typeof e?e?1:0:e instanceof Date?e.getTime():null}},4946:(e,t,n)=>{const r=n(375),s=n(1687),i=n(8068),o=n(8160),a=n(3292),c=n(6354),l=n(6133),u={};e.exports=i.extend({type:"alternatives",flags:{match:{default:"any"}},terms:{matches:{init:[],register:l.toSibling}},args:(e,...t)=>1===t.length&&Array.isArray(t[0])?e.try(...t[0]):e.try(...t),validate(e,t){const{schema:n,error:r,state:i,prefs:o}=t;if(n._flags.match){const t=[],a=[];for(let r=0;r<n.$_terms.matches.length;++r){const s=n.$_terms.matches[r],c=i.nest(s.schema,`match.${r}`);c.snapshot();const l=s.schema.$_validate(e,c,o);l.errors?(a.push(l.errors),c.restore()):(t.push(l.value),c.commit())}if(0===t.length)return{errors:r("alternatives.any",{details:a.map((e=>c.details(e,{override:!1})))})};if("one"===n._flags.match)return 1===t.length?{value:t[0]}:{errors:r("alternatives.one")};if(t.length!==n.$_terms.matches.length)return{errors:r("alternatives.all",{details:a.map((e=>c.details(e,{override:!1})))})};const l=e=>e.$_terms.matches.some((e=>"object"===e.schema.type||"alternatives"===e.schema.type&&l(e.schema)));return l(n)?{value:t.reduce(((e,t)=>s(e,t,{mergeArrays:!1})))}:{value:t[t.length-1]}}const a=[];for(let t=0;t<n.$_terms.matches.length;++t){const r=n.$_terms.matches[t];if(r.schema){const n=i.nest(r.schema,`match.${t}`);n.snapshot();const s=r.schema.$_validate(e,n,o);if(!s.errors)return n.commit(),s;n.restore(),a.push({schema:r.schema,reports:s.errors});continue}const s=r.ref?r.ref.resolve(e,i,o):e,c=r.is?[r]:r.switch;for(let n=0;n<c.length;++n){const a=c[n],{is:l,then:u,otherwise:d}=a,f=`match.${t}${r.switch?"."+n:""}`;if(l.$_match(s,i.nest(l,`${f}.is`),o)){if(u)return u.$_validate(e,i.nest(u,`${f}.then`),o)}else if(d)return d.$_validate(e,i.nest(d,`${f}.otherwise`),o)}}return u.errors(a,t)},rules:{conditional:{method(e,t){r(!this._flags._endedSwitch,"Unreachable condition"),r(!this._flags.match,"Cannot combine match mode",this._flags.match,"with conditional rule"),r(void 0===t.break,"Cannot use break option with alternatives conditional");const n=this.clone(),s=a.when(n,e,t),i=s.is?[s]:s.switch;for(const e of i)if(e.then&&e.otherwise){n.$_setFlag("_endedSwitch",!0,{clone:!1});break}return n.$_terms.matches.push(s),n.$_mutateRebuild()}},match:{method(e){if(r(["any","one","all"].includes(e),"Invalid alternatives match mode",e),"any"!==e)for(const t of this.$_terms.matches)r(t.schema,"Cannot combine match mode",e,"with conditional rules");return this.$_setFlag("match",e)}},try:{method(...e){r(e.length,"Missing alternative schemas"),o.verifyFlat(e,"try"),r(!this._flags._endedSwitch,"Unreachable condition");const t=this.clone();for(const n of e)t.$_terms.matches.push({schema:t.$_compile(n)});return t.$_mutateRebuild()}}},overrides:{label(e){return this.$_parent("label",e).$_modify({each:(t,n)=>"is"!==n.path[0]&&"string"!=typeof t._flags.label?t.label(e):void 0,ref:!1})}},rebuild(e){e.$_modify({each:t=>{o.isSchema(t)&&"array"===t.type&&e.$_setFlag("_arrayItems",!0,{clone:!1})}})},manifest:{build(e,t){if(t.matches)for(const n of t.matches){const{schema:t,ref:r,is:s,not:i,then:o,otherwise:a}=n;e=t?e.try(t):r?e.conditional(r,{is:s,then:o,not:i,otherwise:a,switch:n.switch}):e.conditional(s,{then:o,otherwise:a})}return e}},messages:{"alternatives.all":"{{#label}} does not match all of the required types","alternatives.any":"{{#label}} does not match any of the allowed types","alternatives.match":"{{#label}} does not match any of the allowed types","alternatives.one":"{{#label}} matches more than one allowed type","alternatives.types":"{{#label}} must be one of {{#types}}"}}),u.errors=function(e,{error:t,state:n}){if(!e.length)return{errors:t("alternatives.any")};if(1===e.length)return{errors:e[0].reports};const r=new Set,s=[];for(const{reports:i,schema:o}of e){if(i.length>1)return u.unmatched(e,t);const a=i[0];if(a instanceof c.Report==0)return u.unmatched(e,t);if(a.state.path.length!==n.path.length){s.push({type:o.type,report:a});continue}if("any.only"===a.code){for(const e of a.local.valids)r.add(e);continue}const[l,d]=a.code.split(".");"base"!==d?s.push({type:o.type,report:a}):"object.base"===a.code?r.add(a.local.type):r.add(l)}return s.length?1===s.length?{errors:s[0].report}:u.unmatched(e,t):{errors:t("alternatives.types",{types:[...r]})}},u.unmatched=function(e,t){const n=[];for(const t of e)n.push(...t.reports);return{errors:t("alternatives.match",c.details(n,{override:!1}))}}},8068:(e,t,n)=>{const r=n(375),s=n(7629),i=n(8160),o=n(6914);e.exports=s.extend({type:"any",flags:{only:{default:!1}},terms:{alterations:{init:null},examples:{init:null},externals:{init:null},metas:{init:[]},notes:{init:[]},shared:{init:null},tags:{init:[]},whens:{init:null}},rules:{custom:{method(e,t){return r("function"==typeof e,"Method must be a function"),r(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this.$_addRule({name:"custom",args:{method:e,description:t}})},validate(e,t,{method:n}){try{return n(e,t)}catch(e){return t.error("any.custom",{error:e})}},args:["method","description"],multi:!0},messages:{method(e){return this.prefs({messages:e})}},shared:{method(e){r(i.isSchema(e)&&e._flags.id,"Schema must be a schema with an id");const t=this.clone();return t.$_terms.shared=t.$_terms.shared||[],t.$_terms.shared.push(e),t.$_mutateRegister(e),t}},warning:{method(e,t){return r(e&&"string"==typeof e,"Invalid warning code"),this.$_addRule({name:"warning",args:{code:e,local:t},warn:!0})},validate:(e,t,{code:n,local:r})=>t.error(n,r),args:["code","local"],multi:!0}},modifiers:{keep(e,t=!0){e.keep=t},message(e,t){e.message=o.compile(t)},warn(e,t=!0){e.warn=t}},manifest:{build(e,t){for(const n in t){const r=t[n];if(["examples","externals","metas","notes","tags"].includes(n))for(const t of r)e=e[n.slice(0,-1)](t);else if("alterations"!==n)if("whens"!==n){if("shared"===n)for(const t of r)e=e.shared(t)}else for(const t of r){const{ref:n,is:r,not:s,then:i,otherwise:o,concat:a}=t;e=a?e.concat(a):n?e.when(n,{is:r,not:s,then:i,otherwise:o,switch:t.switch,break:t.break}):e.when(r,{then:i,otherwise:o,break:t.break})}else{const t={};for(const{target:e,adjuster:n}of r)t[e]=n;e=e.alter(t)}}return e}},messages:{"any.custom":"{{#label}} failed custom validation because {{#error.message}}","any.default":"{{#label}} threw an error when running default method","any.failover":"{{#label}} threw an error when running failover method","any.invalid":"{{#label}} contains an invalid value","any.only":'{{#label}} must be {if(#valids.length == 1, "", "one of ")}{{#valids}}',"any.ref":"{{#label}} {{#arg}} references {{:#ref}} which {{#reason}}","any.required":"{{#label}} is required","any.unknown":"{{#label}} is not allowed"}})},546:(e,t,n)=>{const r=n(375),s=n(9474),i=n(9621),o=n(8068),a=n(8160),c=n(3292),l={};e.exports=o.extend({type:"array",flags:{single:{default:!1},sparse:{default:!1}},terms:{items:{init:[],manifest:"schema"},ordered:{init:[],manifest:"schema"},_exclusions:{init:[]},_inclusions:{init:[]},_requireds:{init:[]}},coerce:{from:"object",method(e,{schema:t,state:n,prefs:r}){if(!Array.isArray(e))return;const s=t.$_getRule("sort");return s?l.sort(t,e,s.args.options,n,r):void 0}},validate(e,{schema:t,error:n}){if(!Array.isArray(e)){if(t._flags.single){const t=[e];return t[a.symbols.arraySingle]=!0,{value:t}}return{errors:n("array.base")}}if(t.$_getRule("items")||t.$_terms.externals)return{value:e.slice()}},rules:{has:{method(e){e=this.$_compile(e,{appendPath:!0});const t=this.$_addRule({name:"has",args:{schema:e}});return t.$_mutateRegister(e),t},validate(e,{state:t,prefs:n,error:r},{schema:s}){const i=[e,...t.ancestors];for(let r=0;r<e.length;++r){const o=t.localize([...t.path,r],i,s);if(s.$_match(e[r],o,n))return e}const o=s._flags.label;return o?r("array.hasKnown",{patternLabel:o}):r("array.hasUnknown",null)},multi:!0},items:{method(...e){a.verifyFlat(e,"items");const t=this.$_addRule("items");for(let n=0;n<e.length;++n){const r=a.tryWithPath((()=>this.$_compile(e[n])),n,{append:!0});t.$_terms.items.push(r)}return t.$_mutateRebuild()},validate(e,{schema:t,error:n,state:r,prefs:s,errorsArray:i}){const o=t.$_terms._requireds.slice(),c=t.$_terms.ordered.slice(),u=[...t.$_terms._inclusions,...o],d=!e[a.symbols.arraySingle];delete e[a.symbols.arraySingle];const f=i();let h=e.length;for(let i=0;i<h;++i){const a=e[i];let b=!1,p=!1;const m=d?i:new Number(i),y=[...r.path,m];if(!t._flags.sparse&&void 0===a){if(f.push(n("array.sparse",{key:m,path:y,pos:i,value:void 0},r.localize(y))),s.abortEarly)return f;c.shift();continue}const v=[e,...r.ancestors];for(const e of t.$_terms._exclusions)if(e.$_match(a,r.localize(y,v,e),s,{presence:"ignore"})){if(f.push(n("array.excludes",{pos:i,value:a},r.localize(y))),s.abortEarly)return f;b=!0,c.shift();break}if(b)continue;if(t.$_terms.ordered.length){if(c.length){const o=c.shift(),u=o.$_validate(a,r.localize(y,v,o),s);if(u.errors){if(f.push(...u.errors),s.abortEarly)return f}else if("strip"===o._flags.result)l.fastSplice(e,i),--i,--h;else{if(!t._flags.sparse&&void 0===u.value){if(f.push(n("array.sparse",{key:m,path:y,pos:i,value:void 0},r.localize(y))),s.abortEarly)return f;continue}e[i]=u.value}continue}if(!t.$_terms.items.length){if(f.push(n("array.orderedLength",{pos:i,limit:t.$_terms.ordered.length})),s.abortEarly)return f;break}}const g=[];let w=o.length;for(let c=0;c<w;++c){const u=r.localize(y,v,o[c]);u.snapshot();const d=o[c].$_validate(a,u,s);if(g[c]=d,!d.errors){if(u.commit(),e[i]=d.value,p=!0,l.fastSplice(o,c),--c,--w,!t._flags.sparse&&void 0===d.value&&(f.push(n("array.sparse",{key:m,path:y,pos:i,value:void 0},r.localize(y))),s.abortEarly))return f;break}u.restore()}if(p)continue;const k=s.stripUnknown&&!!s.stripUnknown.arrays||!1;w=u.length;for(const c of u){let u;const d=o.indexOf(c);if(-1!==d)u=g[d];else{const o=r.localize(y,v,c);if(o.snapshot(),u=c.$_validate(a,o,s),!u.errors){o.commit(),"strip"===c._flags.result?(l.fastSplice(e,i),--i,--h):t._flags.sparse||void 0!==u.value?e[i]=u.value:(f.push(n("array.sparse",{key:m,path:y,pos:i,value:void 0},r.localize(y))),b=!0),p=!0;break}o.restore()}if(1===w){if(k){l.fastSplice(e,i),--i,--h,p=!0;break}if(f.push(...u.errors),s.abortEarly)return f;b=!0;break}}if(!b&&(t.$_terms._inclusions.length||t.$_terms._requireds.length)&&!p){if(k){l.fastSplice(e,i),--i,--h;continue}if(f.push(n("array.includes",{pos:i,value:a},r.localize(y))),s.abortEarly)return f}}return o.length&&l.fillMissedErrors(t,f,o,e,r,s),c.length&&(l.fillOrderedErrors(t,f,c,e,r,s),f.length||l.fillDefault(c,e,r,s)),f.length?f:e},priority:!0,manifest:!1},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:n},{name:r,operator:s,args:i})=>a.compare(e.length,n,s)?e:t.error("array."+r,{limit:i.limit,value:e}),args:[{name:"limit",ref:!0,assert:a.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},ordered:{method(...e){a.verifyFlat(e,"ordered");const t=this.$_addRule("items");for(let n=0;n<e.length;++n){const r=a.tryWithPath((()=>this.$_compile(e[n])),n,{append:!0});l.validateSingle(r,t),t.$_mutateRegister(r),t.$_terms.ordered.push(r)}return t.$_mutateRebuild()}},single:{method(e){const t=void 0===e||!!e;return r(!t||!this._flags._arrayItems,"Cannot specify single rule when array has array items"),this.$_setFlag("single",t)}},sort:{method(e={}){a.assertOptions(e,["by","order"]);const t={order:e.order||"ascending"};return e.by&&(t.by=c.ref(e.by,{ancestor:0}),r(!t.by.ancestor,"Cannot sort by ancestor")),this.$_addRule({name:"sort",args:{options:t}})},validate(e,{error:t,state:n,prefs:r,schema:s},{options:i}){const{value:o,errors:a}=l.sort(s,e,i,n,r);if(a)return a;for(let n=0;n<e.length;++n)if(e[n]!==o[n])return t("array.sort",{order:i.order,by:i.by?i.by.key:"value"});return e},convert:!0},sparse:{method(e){const t=void 0===e||!!e;return this._flags.sparse===t?this:(t?this.clone():this.$_addRule("items")).$_setFlag("sparse",t,{clone:!1})}},unique:{method(e,t={}){r(!e||"function"==typeof e||"string"==typeof e,"comparator must be a function or a string"),a.assertOptions(t,["ignoreUndefined","separator"]);const n={name:"unique",args:{options:t,comparator:e}};if(e)if("string"==typeof e){const r=a.default(t.separator,".");n.path=r?e.split(r):[e]}else n.comparator=e;return this.$_addRule(n)},validate(e,{state:t,error:n,schema:o},{comparator:a,options:c},{comparator:l,path:u}){const d={string:Object.create(null),number:Object.create(null),undefined:Object.create(null),boolean:Object.create(null),bigint:Object.create(null),object:new Map,function:new Map,custom:new Map},f=l||s,h=c.ignoreUndefined;for(let s=0;s<e.length;++s){const o=u?i(e[s],u):e[s],c=l?d.custom:d[typeof o];if(r(c,"Failed to find unique map container for type",typeof o),c instanceof Map){const r=c.entries();let i;for(;!(i=r.next()).done;)if(f(i.value[0],o)){const r=t.localize([...t.path,s],[e,...t.ancestors]),o={pos:s,value:e[s],dupePos:i.value[1],dupeValue:e[i.value[1]]};return u&&(o.path=a),n("array.unique",o,r)}c.set(o,s)}else{if((!h||void 0!==o)&&void 0!==c[o]){const r={pos:s,value:e[s],dupePos:c[o],dupeValue:e[c[o]]};return u&&(r.path=a),n("array.unique",r,t.localize([...t.path,s],[e,...t.ancestors]))}c[o]=s}}return e},args:["comparator","options"],multi:!0}},cast:{set:{from:Array.isArray,to:(e,t)=>new Set(e)}},rebuild(e){e.$_terms._inclusions=[],e.$_terms._exclusions=[],e.$_terms._requireds=[];for(const t of e.$_terms.items)l.validateSingle(t,e),"required"===t._flags.presence?e.$_terms._requireds.push(t):"forbidden"===t._flags.presence?e.$_terms._exclusions.push(t):e.$_terms._inclusions.push(t);for(const t of e.$_terms.ordered)l.validateSingle(t,e)},manifest:{build:(e,t)=>(t.items&&(e=e.items(...t.items)),t.ordered&&(e=e.ordered(...t.ordered)),e)},messages:{"array.base":"{{#label}} must be an array","array.excludes":"{{#label}} contains an excluded value","array.hasKnown":"{{#label}} does not contain at least one required match for type {:#patternLabel}","array.hasUnknown":"{{#label}} does not contain at least one required match","array.includes":"{{#label}} does not match any of the allowed types","array.includesRequiredBoth":"{{#label}} does not contain {{#knownMisses}} and {{#unknownMisses}} other required value(s)","array.includesRequiredKnowns":"{{#label}} does not contain {{#knownMisses}}","array.includesRequiredUnknowns":"{{#label}} does not contain {{#unknownMisses}} required value(s)","array.length":"{{#label}} must contain {{#limit}} items","array.max":"{{#label}} must contain less than or equal to {{#limit}} items","array.min":"{{#label}} must contain at least {{#limit}} items","array.orderedLength":"{{#label}} must contain at most {{#limit}} items","array.sort":"{{#label}} must be sorted in {#order} order by {{#by}}","array.sort.mismatching":"{{#label}} cannot be sorted due to mismatching types","array.sort.unsupported":"{{#label}} cannot be sorted due to unsupported type {#type}","array.sparse":"{{#label}} must not be a sparse array item","array.unique":"{{#label}} contains a duplicate value"}}),l.fillMissedErrors=function(e,t,n,r,s,i){const o=[];let a=0;for(const e of n){const t=e._flags.label;t?o.push(t):++a}o.length?a?t.push(e.$_createError("array.includesRequiredBoth",r,{knownMisses:o,unknownMisses:a},s,i)):t.push(e.$_createError("array.includesRequiredKnowns",r,{knownMisses:o},s,i)):t.push(e.$_createError("array.includesRequiredUnknowns",r,{unknownMisses:a},s,i))},l.fillOrderedErrors=function(e,t,n,r,s,i){const o=[];for(const e of n)"required"===e._flags.presence&&o.push(e);o.length&&l.fillMissedErrors(e,t,o,r,s,i)},l.fillDefault=function(e,t,n,r){const s=[];let i=!0;for(let o=e.length-1;o>=0;--o){const a=e[o],c=[t,...n.ancestors],l=a.$_validate(void 0,n.localize(n.path,c,a),r).value;if(i){if(void 0===l)continue;i=!1}s.unshift(l)}s.length&&t.push(...s)},l.fastSplice=function(e,t){let n=t;for(;n<e.length;)e[n++]=e[n];--e.length},l.validateSingle=function(e,t){("array"===e.type||e._flags._arrayItems)&&(r(!t._flags.single,"Cannot specify array item with single rule enabled"),t.$_setFlag("_arrayItems",!0,{clone:!1}))},l.sort=function(e,t,n,r,s){const i="ascending"===n.order?1:-1,o=-1*i,a=i,c=(c,u)=>{let d=l.compare(c,u,o,a);if(null!==d)return d;if(n.by&&(c=n.by.resolve(c,r,s),u=n.by.resolve(u,r,s)),d=l.compare(c,u,o,a),null!==d)return d;const f=typeof c;if(f!==typeof u)throw e.$_createError("array.sort.mismatching",t,null,r,s);if("number"!==f&&"string"!==f)throw e.$_createError("array.sort.unsupported",t,{type:f},r,s);return"number"===f?(c-u)*i:c<u?o:a};try{return{value:t.slice().sort(c)}}catch(e){return{errors:e}}},l.compare=function(e,t,n,r){return e===t?0:void 0===e?1:void 0===t?-1:null===e?r:null===t?n:null}},4937:(e,t,n)=>{const r=n(375),s=n(8068),i=n(8160),o=n(2036),a={isBool:function(e){return"boolean"==typeof e}};e.exports=s.extend({type:"boolean",flags:{sensitive:{default:!1}},terms:{falsy:{init:null,manifest:"values"},truthy:{init:null,manifest:"values"}},coerce(e,{schema:t}){if("boolean"!=typeof e){if("string"==typeof e){const n=t._flags.sensitive?e:e.toLowerCase();e="true"===n||"false"!==n&&e}return"boolean"!=typeof e&&(e=t.$_terms.truthy&&t.$_terms.truthy.has(e,null,null,!t._flags.sensitive)||(!t.$_terms.falsy||!t.$_terms.falsy.has(e,null,null,!t._flags.sensitive))&&e),{value:e}}},validate(e,{error:t}){if("boolean"!=typeof e)return{value:e,errors:t("boolean.base")}},rules:{truthy:{method(...e){i.verifyFlat(e,"truthy");const t=this.clone();t.$_terms.truthy=t.$_terms.truthy||new o;for(let n=0;n<e.length;++n){const s=e[n];r(void 0!==s,"Cannot call truthy with undefined"),t.$_terms.truthy.add(s)}return t}},falsy:{method(...e){i.verifyFlat(e,"falsy");const t=this.clone();t.$_terms.falsy=t.$_terms.falsy||new o;for(let n=0;n<e.length;++n){const s=e[n];r(void 0!==s,"Cannot call falsy with undefined"),t.$_terms.falsy.add(s)}return t}},sensitive:{method(e=!0){return this.$_setFlag("sensitive",e)}}},cast:{number:{from:a.isBool,to:(e,t)=>e?1:0},string:{from:a.isBool,to:(e,t)=>e?"true":"false"}},manifest:{build:(e,t)=>(t.truthy&&(e=e.truthy(...t.truthy)),t.falsy&&(e=e.falsy(...t.falsy)),e)},messages:{"boolean.base":"{{#label}} must be a boolean"}})},7500:(e,t,n)=>{const r=n(375),s=n(8068),i=n(8160),o=n(3328),a={isDate:function(e){return e instanceof Date}};e.exports=s.extend({type:"date",coerce:{from:["number","string"],method:(e,{schema:t})=>({value:a.parse(e,t._flags.format)||e})},validate(e,{schema:t,error:n,prefs:r}){if(e instanceof Date&&!isNaN(e.getTime()))return;const s=t._flags.format;return r.convert&&s&&"string"==typeof e?{value:e,errors:n("date.format",{format:s})}:{value:e,errors:n("date.base")}},rules:{compare:{method:!1,validate(e,t,{date:n},{name:r,operator:s,args:o}){const a="now"===n?Date.now():n.getTime();return i.compare(e.getTime(),a,s)?e:t.error("date."+r,{limit:o.date,value:e})},args:[{name:"date",ref:!0,normalize:e=>"now"===e?e:a.parse(e),assert:e=>null!==e,message:"must have a valid date format"}]},format:{method(e){return r(["iso","javascript","unix"].includes(e),"Unknown date format",e),this.$_setFlag("format",e)}},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{date:e},operator:">"})}},iso:{method(){return this.format("iso")}},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{date:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{date:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{date:e},operator:">="})}},timestamp:{method(e="javascript"){return r(["javascript","unix"].includes(e),'"type" must be one of "javascript, unix"'),this.format(e)}}},cast:{number:{from:a.isDate,to:(e,t)=>e.getTime()},string:{from:a.isDate,to:(e,{prefs:t})=>o.date(e,t)}},messages:{"date.base":"{{#label}} must be a valid date","date.format":'{{#label}} must be in {msg("date.format." + #format) || #format} format',"date.greater":"{{#label}} must be greater than {{:#limit}}","date.less":"{{#label}} must be less than {{:#limit}}","date.max":"{{#label}} must be less than or equal to {{:#limit}}","date.min":"{{#label}} must be greater than or equal to {{:#limit}}","date.format.iso":"ISO 8601 date","date.format.javascript":"timestamp or number of milliseconds","date.format.unix":"timestamp or number of seconds"}}),a.parse=function(e,t){if(e instanceof Date)return e;if("string"!=typeof e&&(isNaN(e)||!isFinite(e)))return null;if(/^\s*$/.test(e))return null;if("iso"===t)return i.isIsoDate(e)?a.date(e.toString()):null;const n=e;if("string"==typeof e&&/^[+-]?\d+(\.\d+)?$/.test(e)&&(e=parseFloat(e)),t){if("javascript"===t)return a.date(1*e);if("unix"===t)return a.date(1e3*e);if("string"==typeof n)return null}return a.date(e)},a.date=function(e){const t=new Date(e);return isNaN(t.getTime())?null:t}},390:(e,t,n)=>{const r=n(375),s=n(7824);e.exports=s.extend({type:"function",properties:{typeof:"function"},rules:{arity:{method(e){return r(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"arity",args:{n:e}})},validate:(e,t,{n})=>e.length===n?e:t.error("function.arity",{n})},class:{method(){return this.$_addRule("class")},validate:(e,t)=>/^\s*class\s/.test(e.toString())?e:t.error("function.class",{value:e})},minArity:{method(e){return r(Number.isSafeInteger(e)&&e>0,"n must be a strict positive integer"),this.$_addRule({name:"minArity",args:{n:e}})},validate:(e,t,{n})=>e.length>=n?e:t.error("function.minArity",{n})},maxArity:{method(e){return r(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"maxArity",args:{n:e}})},validate:(e,t,{n})=>e.length<=n?e:t.error("function.maxArity",{n})}},messages:{"function.arity":"{{#label}} must have an arity of {{#n}}","function.class":"{{#label}} must be a class","function.maxArity":"{{#label}} must have an arity lesser or equal to {{#n}}","function.minArity":"{{#label}} must have an arity greater or equal to {{#n}}"}})},7824:(e,t,n)=>{const r=n(978),s=n(375),i=n(8571),o=n(3652),a=n(8068),c=n(8160),l=n(3292),u=n(6354),d=n(6133),f=n(3328),h={renameDefaults:{alias:!1,multiple:!1,override:!1}};e.exports=a.extend({type:"_keys",properties:{typeof:"object"},flags:{unknown:{default:void 0}},terms:{dependencies:{init:null},keys:{init:null,manifest:{mapped:{from:"schema",to:"key"}}},patterns:{init:null},renames:{init:null}},args:(e,t)=>e.keys(t),validate(e,{schema:t,error:n,state:r,prefs:s}){if(!e||typeof e!==t.$_property("typeof")||Array.isArray(e))return{value:e,errors:n("object.base",{type:t.$_property("typeof")})};if(!(t.$_terms.renames||t.$_terms.dependencies||t.$_terms.keys||t.$_terms.patterns||t.$_terms.externals))return;e=h.clone(e,s);const i=[];if(t.$_terms.renames&&!h.rename(t,e,r,s,i))return{value:e,errors:i};if(!t.$_terms.keys&&!t.$_terms.patterns&&!t.$_terms.dependencies)return{value:e,errors:i};const o=new Set(Object.keys(e));if(t.$_terms.keys){const n=[e,...r.ancestors];for(const a of t.$_terms.keys){const t=a.key,c=e[t];o.delete(t);const l=r.localize([...r.path,t],n,a),u=a.schema.$_validate(c,l,s);if(u.errors){if(s.abortEarly)return{value:e,errors:u.errors};void 0!==u.value&&(e[t]=u.value),i.push(...u.errors)}else"strip"===a.schema._flags.result||void 0===u.value&&void 0!==c?delete e[t]:void 0!==u.value&&(e[t]=u.value)}}if(o.size||t._flags._hasPatternMatch){const n=h.unknown(t,e,o,i,r,s);if(n)return n}if(t.$_terms.dependencies)for(const n of t.$_terms.dependencies){if(null!==n.key&&!1===h.isPresent(n.options)(n.key.resolve(e,r,s,null,{shadow:!1})))continue;const o=h.dependencies[n.rel](t,n,e,r,s);if(o){const n=t.$_createError(o.code,e,o.context,r,s);if(s.abortEarly)return{value:e,errors:n};i.push(n)}}return{value:e,errors:i}},rules:{and:{method(...e){return c.verifyFlat(e,"and"),h.dependency(this,"and",null,e)}},append:{method(e){return null==e||0===Object.keys(e).length?this:this.keys(e)}},assert:{method(e,t,n){f.isTemplate(e)||(e=l.ref(e)),s(void 0===n||"string"==typeof n,"Message must be a string"),t=this.$_compile(t,{appendPath:!0});const r=this.$_addRule({name:"assert",args:{subject:e,schema:t,message:n}});return r.$_mutateRegister(e),r.$_mutateRegister(t),r},validate(e,{error:t,prefs:n,state:r},{subject:s,schema:i,message:o}){const a=s.resolve(e,r,n),c=d.isRef(s)?s.absolute(r):[];return i.$_match(a,r.localize(c,[e,...r.ancestors],i),n)?e:t("object.assert",{subject:s,message:o})},args:["subject","schema","message"],multi:!0},instance:{method(e,t){return s("function"==typeof e,"constructor must be a function"),t=t||e.name,this.$_addRule({name:"instance",args:{constructor:e,name:t}})},validate:(e,t,{constructor:n,name:r})=>e instanceof n?e:t.error("object.instance",{type:r,value:e}),args:["constructor","name"]},keys:{method(e){s(void 0===e||"object"==typeof e,"Object schema must be a valid object"),s(!c.isSchema(e),"Object schema cannot be a joi schema");const t=this.clone();if(e)if(Object.keys(e).length){t.$_terms.keys=t.$_terms.keys?t.$_terms.keys.filter((t=>!e.hasOwnProperty(t.key))):new h.Keys;for(const n in e)c.tryWithPath((()=>t.$_terms.keys.push({key:n,schema:this.$_compile(e[n])})),n)}else t.$_terms.keys=new h.Keys;else t.$_terms.keys=null;return t.$_mutateRebuild()}},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:n},{name:r,operator:s,args:i})=>c.compare(Object.keys(e).length,n,s)?e:t.error("object."+r,{limit:i.limit,value:e}),args:[{name:"limit",ref:!0,assert:c.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},nand:{method(...e){return c.verifyFlat(e,"nand"),h.dependency(this,"nand",null,e)}},or:{method(...e){return c.verifyFlat(e,"or"),h.dependency(this,"or",null,e)}},oxor:{method(...e){return h.dependency(this,"oxor",null,e)}},pattern:{method(e,t,n={}){const r=e instanceof RegExp;r||(e=this.$_compile(e,{appendPath:!0})),s(void 0!==t,"Invalid rule"),c.assertOptions(n,["fallthrough","matches"]),r&&s(!e.flags.includes("g")&&!e.flags.includes("y"),"pattern should not use global or sticky mode"),t=this.$_compile(t,{appendPath:!0});const i=this.clone();i.$_terms.patterns=i.$_terms.patterns||[];const o={[r?"regex":"schema"]:e,rule:t};return n.matches&&(o.matches=this.$_compile(n.matches),"array"!==o.matches.type&&(o.matches=o.matches.$_root.array().items(o.matches)),i.$_mutateRegister(o.matches),i.$_setFlag("_hasPatternMatch",!0,{clone:!1})),n.fallthrough&&(o.fallthrough=!0),i.$_terms.patterns.push(o),i.$_mutateRegister(t),i}},ref:{method(){return this.$_addRule("ref")},validate:(e,t)=>d.isRef(e)?e:t.error("object.refType",{value:e})},regex:{method(){return this.$_addRule("regex")},validate:(e,t)=>e instanceof RegExp?e:t.error("object.regex",{value:e})},rename:{method(e,t,n={}){s("string"==typeof e||e instanceof RegExp,"Rename missing the from argument"),s("string"==typeof t||t instanceof f,"Invalid rename to argument"),s(t!==e,"Cannot rename key to same name:",e),c.assertOptions(n,["alias","ignoreUndefined","override","multiple"]);const i=this.clone();i.$_terms.renames=i.$_terms.renames||[];for(const t of i.$_terms.renames)s(t.from!==e,"Cannot rename the same key multiple times");return t instanceof f&&i.$_mutateRegister(t),i.$_terms.renames.push({from:e,to:t,options:r(h.renameDefaults,n)}),i}},schema:{method(e="any"){return this.$_addRule({name:"schema",args:{type:e}})},validate:(e,t,{type:n})=>!c.isSchema(e)||"any"!==n&&e.type!==n?t.error("object.schema",{type:n}):e},unknown:{method(e){return this.$_setFlag("unknown",!1!==e)}},with:{method(e,t,n={}){return h.dependency(this,"with",e,t,n)}},without:{method(e,t,n={}){return h.dependency(this,"without",e,t,n)}},xor:{method(...e){return c.verifyFlat(e,"xor"),h.dependency(this,"xor",null,e)}}},overrides:{default(e,t){return void 0===e&&(e=c.symbols.deepDefault),this.$_parent("default",e,t)}},rebuild(e){if(e.$_terms.keys){const t=new o.Sorter;for(const n of e.$_terms.keys)c.tryWithPath((()=>t.add(n,{after:n.schema.$_rootReferences(),group:n.key})),n.key);e.$_terms.keys=new h.Keys(...t.nodes)}},manifest:{build(e,t){if(t.keys&&(e=e.keys(t.keys)),t.dependencies)for(const{rel:n,key:r=null,peers:s,options:i}of t.dependencies)e=h.dependency(e,n,r,s,i);if(t.patterns)for(const{regex:n,schema:r,rule:s,fallthrough:i,matches:o}of t.patterns)e=e.pattern(n||r,s,{fallthrough:i,matches:o});if(t.renames)for(const{from:n,to:r,options:s}of t.renames)e=e.rename(n,r,s);return e}},messages:{"object.and":"{{#label}} contains {{#presentWithLabels}} without its required peers {{#missingWithLabels}}","object.assert":'{{#label}} is invalid because {if(#subject.key, `"` + #subject.key + `" failed to ` + (#message || "pass the assertion test"), #message || "the assertion failed")}',"object.base":"{{#label}} must be of type {{#type}}","object.instance":"{{#label}} must be an instance of {{:#type}}","object.length":'{{#label}} must have {{#limit}} key{if(#limit == 1, "", "s")}',"object.max":'{{#label}} must have less than or equal to {{#limit}} key{if(#limit == 1, "", "s")}',"object.min":'{{#label}} must have at least {{#limit}} key{if(#limit == 1, "", "s")}',"object.missing":"{{#label}} must contain at least one of {{#peersWithLabels}}","object.nand":"{{:#mainWithLabel}} must not exist simultaneously with {{#peersWithLabels}}","object.oxor":"{{#label}} contains a conflict between optional exclusive peers {{#peersWithLabels}}","object.pattern.match":"{{#label}} keys failed to match pattern requirements","object.refType":"{{#label}} must be a Joi reference","object.regex":"{{#label}} must be a RegExp object","object.rename.multiple":"{{#label}} cannot rename {{:#from}} because multiple renames are disabled and another key was already renamed to {{:#to}}","object.rename.override":"{{#label}} cannot rename {{:#from}} because override is disabled and target {{:#to}} exists","object.schema":"{{#label}} must be a Joi schema of {{#type}} type","object.unknown":"{{#label}} is not allowed","object.with":"{{:#mainWithLabel}} missing required peer {{:#peerWithLabel}}","object.without":"{{:#mainWithLabel}} conflict with forbidden peer {{:#peerWithLabel}}","object.xor":"{{#label}} contains a conflict between exclusive peers {{#peersWithLabels}}"}}),h.clone=function(e,t){if("object"==typeof e){if(t.nonEnumerables)return i(e,{shallow:!0});const n=Object.create(Object.getPrototypeOf(e));return Object.assign(n,e),n}const n=function(...t){return e.apply(this,t)};return n.prototype=i(e.prototype),Object.defineProperty(n,"name",{value:e.name,writable:!1}),Object.defineProperty(n,"length",{value:e.length,writable:!1}),Object.assign(n,e),n},h.dependency=function(e,t,n,r,i){s(null===n||"string"==typeof n,t,"key must be a strings"),i||(i=r.length>1&&"object"==typeof r[r.length-1]?r.pop():{}),c.assertOptions(i,["separator","isPresent"]),r=[].concat(r);const o=c.default(i.separator,"."),a=[];for(const e of r)s("string"==typeof e,t,"peers must be strings"),a.push(l.ref(e,{separator:o,ancestor:0,prefix:!1}));null!==n&&(n=l.ref(n,{separator:o,ancestor:0,prefix:!1}));const u=e.clone();return u.$_terms.dependencies=u.$_terms.dependencies||[],u.$_terms.dependencies.push(new h.Dependency(t,n,a,r,i)),u},h.dependencies={and(e,t,n,r,s){const i=[],o=[],a=t.peers.length,c=h.isPresent(t.options);for(const e of t.peers)!1===c(e.resolve(n,r,s,null,{shadow:!1}))?i.push(e.key):o.push(e.key);if(i.length!==a&&o.length!==a)return{code:"object.and",context:{present:o,presentWithLabels:h.keysToLabels(e,o),missing:i,missingWithLabels:h.keysToLabels(e,i)}}},nand(e,t,n,r,s){const i=[],o=h.isPresent(t.options);for(const e of t.peers)o(e.resolve(n,r,s,null,{shadow:!1}))&&i.push(e.key);if(i.length!==t.peers.length)return;const a=t.paths[0],c=t.paths.slice(1);return{code:"object.nand",context:{main:a,mainWithLabel:h.keysToLabels(e,a),peers:c,peersWithLabels:h.keysToLabels(e,c)}}},or(e,t,n,r,s){const i=h.isPresent(t.options);for(const e of t.peers)if(i(e.resolve(n,r,s,null,{shadow:!1})))return;return{code:"object.missing",context:{peers:t.paths,peersWithLabels:h.keysToLabels(e,t.paths)}}},oxor(e,t,n,r,s){const i=[],o=h.isPresent(t.options);for(const e of t.peers)o(e.resolve(n,r,s,null,{shadow:!1}))&&i.push(e.key);if(!i.length||1===i.length)return;const a={peers:t.paths,peersWithLabels:h.keysToLabels(e,t.paths)};return a.present=i,a.presentWithLabels=h.keysToLabels(e,i),{code:"object.oxor",context:a}},with(e,t,n,r,s){const i=h.isPresent(t.options);for(const o of t.peers)if(!1===i(o.resolve(n,r,s,null,{shadow:!1})))return{code:"object.with",context:{main:t.key.key,mainWithLabel:h.keysToLabels(e,t.key.key),peer:o.key,peerWithLabel:h.keysToLabels(e,o.key)}}},without(e,t,n,r,s){const i=h.isPresent(t.options);for(const o of t.peers)if(i(o.resolve(n,r,s,null,{shadow:!1})))return{code:"object.without",context:{main:t.key.key,mainWithLabel:h.keysToLabels(e,t.key.key),peer:o.key,peerWithLabel:h.keysToLabels(e,o.key)}}},xor(e,t,n,r,s){const i=[],o=h.isPresent(t.options);for(const e of t.peers)o(e.resolve(n,r,s,null,{shadow:!1}))&&i.push(e.key);if(1===i.length)return;const a={peers:t.paths,peersWithLabels:h.keysToLabels(e,t.paths)};return 0===i.length?{code:"object.missing",context:a}:(a.present=i,a.presentWithLabels=h.keysToLabels(e,i),{code:"object.xor",context:a})}},h.keysToLabels=function(e,t){return Array.isArray(t)?t.map((t=>e.$_mapLabels(t))):e.$_mapLabels(t)},h.isPresent=function(e){return"function"==typeof e.isPresent?e.isPresent:e=>void 0!==e},h.rename=function(e,t,n,r,s){const i={};for(const o of e.$_terms.renames){const a=[],c="string"!=typeof o.from;if(c)for(const e in t){if(void 0===t[e]&&o.options.ignoreUndefined)continue;if(e===o.to)continue;const n=o.from.exec(e);n&&a.push({from:e,to:o.to,match:n})}else!Object.prototype.hasOwnProperty.call(t,o.from)||void 0===t[o.from]&&o.options.ignoreUndefined||a.push(o);for(const l of a){const a=l.from;let u=l.to;if(u instanceof f&&(u=u.render(t,n,r,l.match)),a!==u){if(!o.options.multiple&&i[u]&&(s.push(e.$_createError("object.rename.multiple",t,{from:a,to:u,pattern:c},n,r)),r.abortEarly))return!1;if(Object.prototype.hasOwnProperty.call(t,u)&&!o.options.override&&!i[u]&&(s.push(e.$_createError("object.rename.override",t,{from:a,to:u,pattern:c},n,r)),r.abortEarly))return!1;void 0===t[a]?delete t[u]:t[u]=t[a],i[u]=!0,o.options.alias||delete t[a]}}}return!0},h.unknown=function(e,t,n,r,s,i){if(e.$_terms.patterns){let o=!1;const a=e.$_terms.patterns.map((e=>{if(e.matches)return o=!0,[]})),c=[t,...s.ancestors];for(const o of n){const l=t[o],u=[...s.path,o];for(let d=0;d<e.$_terms.patterns.length;++d){const f=e.$_terms.patterns[d];if(f.regex){const e=f.regex.test(o);if(s.mainstay.tracer.debug(s,"rule",`pattern.${d}`,e?"pass":"error"),!e)continue}else if(!f.schema.$_match(o,s.nest(f.schema,`pattern.${d}`),i))continue;n.delete(o);const h=s.localize(u,c,{schema:f.rule,key:o}),b=f.rule.$_validate(l,h,i);if(b.errors){if(i.abortEarly)return{value:t,errors:b.errors};r.push(...b.errors)}if(f.matches&&a[d].push(o),t[o]=b.value,!f.fallthrough)break}}if(o)for(let n=0;n<a.length;++n){const o=a[n];if(!o)continue;const l=e.$_terms.patterns[n].matches,d=s.localize(s.path,c,l),f=l.$_validate(o,d,i);if(f.errors){const n=u.details(f.errors,{override:!1});n.matches=o;const a=e.$_createError("object.pattern.match",t,n,s,i);if(i.abortEarly)return{value:t,errors:a};r.push(a)}}}if(n.size&&(e.$_terms.keys||e.$_terms.patterns)){if(i.stripUnknown&&void 0===e._flags.unknown||i.skipFunctions){const e=!(!i.stripUnknown||!0!==i.stripUnknown&&!i.stripUnknown.objects);for(const r of n)e?(delete t[r],n.delete(r)):"function"==typeof t[r]&&n.delete(r)}if(!c.default(e._flags.unknown,i.allowUnknown))for(const o of n){const n=s.localize([...s.path,o],[]),a=e.$_createError("object.unknown",t[o],{child:o},n,i,{flags:!1});if(i.abortEarly)return{value:t,errors:a};r.push(a)}}},h.Dependency=class{constructor(e,t,n,r,s){this.rel=e,this.key=t,this.peers=n,this.paths=r,this.options=s}describe(){const e={rel:this.rel,peers:this.paths};return null!==this.key&&(e.key=this.key.key),"."!==this.peers[0].separator&&(e.options={...e.options,separator:this.peers[0].separator}),this.options.isPresent&&(e.options={...e.options,isPresent:this.options.isPresent}),e}},h.Keys=class extends Array{concat(e){const t=this.slice(),n=new Map;for(let e=0;e<t.length;++e)n.set(t[e].key,e);for(const r of e){const e=r.key,s=n.get(e);void 0!==s?t[s]={key:e,schema:t[s].schema.concat(r.schema)}:t.push(r)}return t}}},8785:(e,t,n)=>{const r=n(375),s=n(8068),i=n(8160),o=n(3292),a=n(6354),c={};e.exports=s.extend({type:"link",properties:{schemaChain:!0},terms:{link:{init:null,manifest:"single",register:!1}},args:(e,t)=>e.ref(t),validate(e,{schema:t,state:n,prefs:s}){r(t.$_terms.link,"Uninitialized link schema");const i=c.generate(t,e,n,s),o=t.$_terms.link[0].ref;return i.$_validate(e,n.nest(i,`link:${o.display}:${i.type}`),s)},generate:(e,t,n,r)=>c.generate(e,t,n,r),rules:{ref:{method(e){r(!this.$_terms.link,"Cannot reinitialize schema"),e=o.ref(e),r("value"===e.type||"local"===e.type,"Invalid reference type:",e.type),r("local"===e.type||"root"===e.ancestor||e.ancestor>0,"Link cannot reference itself");const t=this.clone();return t.$_terms.link=[{ref:e}],t}},relative:{method(e=!0){return this.$_setFlag("relative",e)}}},overrides:{concat(e){r(this.$_terms.link,"Uninitialized link schema"),r(i.isSchema(e),"Invalid schema object"),r("link"!==e.type,"Cannot merge type link with another link");const t=this.clone();return t.$_terms.whens||(t.$_terms.whens=[]),t.$_terms.whens.push({concat:e}),t.$_mutateRebuild()}},manifest:{build:(e,t)=>(r(t.link,"Invalid link description missing link"),e.ref(t.link))}}),c.generate=function(e,t,n,r){let s=n.mainstay.links.get(e);if(s)return s._generate(t,n,r).schema;const i=e.$_terms.link[0].ref,{perspective:o,path:a}=c.perspective(i,n);c.assert(o,"which is outside of schema boundaries",i,e,n,r);try{s=a.length?o.$_reach(a):o}catch(t){c.assert(!1,"to non-existing schema",i,e,n,r)}return c.assert("link"!==s.type,"which is another link",i,e,n,r),e._flags.relative||n.mainstay.links.set(e,s),s._generate(t,n,r).schema},c.perspective=function(e,t){if("local"===e.type){for(const{schema:n,key:r}of t.schemas){if((n._flags.id||r)===e.path[0])return{perspective:n,path:e.path.slice(1)};if(n.$_terms.shared)for(const t of n.$_terms.shared)if(t._flags.id===e.path[0])return{perspective:t,path:e.path.slice(1)}}return{perspective:null,path:null}}return"root"===e.ancestor?{perspective:t.schemas[t.schemas.length-1].schema,path:e.path}:{perspective:t.schemas[e.ancestor]&&t.schemas[e.ancestor].schema,path:e.path}},c.assert=function(e,t,n,s,i,o){e||r(!1,`"${a.label(s._flags,i,o)}" contains link reference "${n.display}" ${t}`)}},3832:(e,t,n)=>{const r=n(375),s=n(8068),i=n(8160),o={numberRx:/^\s*[+-]?(?:(?:\d+(?:\.\d*)?)|(?:\.\d+))(?:e([+-]?\d+))?\s*$/i,precisionRx:/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/,exponentialPartRegex:/[eE][+-]?\d+$/,leadingSignAndZerosRegex:/^[+-]?(0*)?/,dotRegex:/\./,trailingZerosRegex:/0+$/,decimalPlaces(e){const t=e.toString(),n=t.indexOf("."),r=t.indexOf("e");return(n<0?0:(r<0?t.length:r)-n-1)+(r<0?0:Math.max(0,-parseInt(t.slice(r+1))))}};e.exports=s.extend({type:"number",flags:{unsafe:{default:!1}},coerce:{from:"string",method(e,{schema:t,error:n}){if(!e.match(o.numberRx))return;e=e.trim();const r={value:parseFloat(e)};if(0===r.value&&(r.value=0),!t._flags.unsafe)if(e.match(/e/i)){if(o.extractSignificantDigits(e)!==o.extractSignificantDigits(String(r.value)))return r.errors=n("number.unsafe"),r}else{const t=r.value.toString();if(t.match(/e/i))return r;if(t!==o.normalizeDecimal(e))return r.errors=n("number.unsafe"),r}return r}},validate(e,{schema:t,error:n,prefs:r}){if(e===1/0||e===-1/0)return{value:e,errors:n("number.infinity")};if(!i.isNumber(e))return{value:e,errors:n("number.base")};const s={value:e};if(r.convert){const e=t.$_getRule("precision");if(e){const t=Math.pow(10,e.args.limit);s.value=Math.round(s.value*t)/t}}return 0===s.value&&(s.value=0),!t._flags.unsafe&&(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&(s.errors=n("number.unsafe")),s},rules:{compare:{method:!1,validate:(e,t,{limit:n},{name:r,operator:s,args:o})=>i.compare(e,n,s)?e:t.error("number."+r,{limit:o.limit,value:e}),args:[{name:"limit",ref:!0,assert:i.isNumber,message:"must be a number"}]},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{limit:e},operator:">"})}},integer:{method(){return this.$_addRule("integer")},validate:(e,t)=>Math.trunc(e)-e==0?e:t.error("number.integer")},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{limit:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{limit:e},operator:">="})}},multiple:{method(e){const t="number"==typeof e?o.decimalPlaces(e):null,n=Math.pow(10,t);return this.$_addRule({name:"multiple",args:{base:e,baseDecimalPlace:t,pfactor:n}})},validate:(e,t,{base:n,baseDecimalPlace:r,pfactor:s},i)=>o.decimalPlaces(e)>r?t.error("number.multiple",{multiple:i.args.base,value:e}):Math.round(s*e)%Math.round(s*n)==0?e:t.error("number.multiple",{multiple:i.args.base,value:e}),args:[{name:"base",ref:!0,assert:e=>"number"==typeof e&&isFinite(e)&&e>0,message:"must be a positive number"},"baseDecimalPlace","pfactor"],multi:!0},negative:{method(){return this.sign("negative")}},port:{method(){return this.$_addRule("port")},validate:(e,t)=>Number.isSafeInteger(e)&&e>=0&&e<=65535?e:t.error("number.port")},positive:{method(){return this.sign("positive")}},precision:{method(e){return r(Number.isSafeInteger(e),"limit must be an integer"),this.$_addRule({name:"precision",args:{limit:e}})},validate(e,t,{limit:n}){const r=e.toString().match(o.precisionRx);return Math.max((r[1]?r[1].length:0)-(r[2]?parseInt(r[2],10):0),0)<=n?e:t.error("number.precision",{limit:n,value:e})},convert:!0},sign:{method(e){return r(["negative","positive"].includes(e),"Invalid sign",e),this.$_addRule({name:"sign",args:{sign:e}})},validate:(e,t,{sign:n})=>"negative"===n&&e<0||"positive"===n&&e>0?e:t.error(`number.${n}`)},unsafe:{method(e=!0){return r("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("unsafe",e)}}},cast:{string:{from:e=>"number"==typeof e,to:(e,t)=>e.toString()}},messages:{"number.base":"{{#label}} must be a number","number.greater":"{{#label}} must be greater than {{#limit}}","number.infinity":"{{#label}} cannot be infinity","number.integer":"{{#label}} must be an integer","number.less":"{{#label}} must be less than {{#limit}}","number.max":"{{#label}} must be less than or equal to {{#limit}}","number.min":"{{#label}} must be greater than or equal to {{#limit}}","number.multiple":"{{#label}} must be a multiple of {{#multiple}}","number.negative":"{{#label}} must be a negative number","number.port":"{{#label}} must be a valid port","number.positive":"{{#label}} must be a positive number","number.precision":"{{#label}} must have no more than {{#limit}} decimal places","number.unsafe":"{{#label}} must be a safe number"}}),o.extractSignificantDigits=function(e){return e.replace(o.exponentialPartRegex,"").replace(o.dotRegex,"").replace(o.trailingZerosRegex,"").replace(o.leadingSignAndZerosRegex,"")},o.normalizeDecimal=function(e){return(e=e.replace(/^\+/,"").replace(/\.0*$/,"").replace(/^(-?)\.([^\.]*)$/,"$10.$2").replace(/^(-?)0+([0-9])/,"$1$2")).includes(".")&&e.endsWith("0")&&(e=e.replace(/0+$/,"")),"-0"===e?"0":e}},8966:(e,t,n)=>{const r=n(7824);e.exports=r.extend({type:"object",cast:{map:{from:e=>e&&"object"==typeof e,to:(e,t)=>new Map(Object.entries(e))}}})},7417:(e,t,n)=>{const r=n(375),s=n(5380),i=n(1745),o=n(9959),a=n(6064),c=n(9926),l=n(5752),u=n(8068),d=n(8160),f={tlds:c instanceof Set&&{tlds:{allow:c,deny:null}},base64Regex:{true:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}==|[\w\-]{3}=)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/},false:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}(==)?|[\w\-]{3}=?)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}(==)?|[A-Za-z0-9+\/]{3}=?)?$/}},dataUriRegex:/^data:[\w+.-]+\/[\w+.-]+;((charset=[\w-]+|base64),)?(.*)$/,hexRegex:{withPrefix:/^0x[0-9a-f]+$/i,withOptionalPrefix:/^(?:0x)?[0-9a-f]+$/i,withoutPrefix:/^[0-9a-f]+$/i},ipRegex:o.regex({cidr:"forbidden"}).regex,isoDurationRegex:/^P(?!$)(\d+Y)?(\d+M)?(\d+W)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?$/,guidBrackets:{"{":"}","[":"]","(":")","":""},guidVersions:{uuidv1:"1",uuidv2:"2",uuidv3:"3",uuidv4:"4",uuidv5:"5",uuidv6:"6",uuidv7:"7",uuidv8:"8"},guidSeparators:new Set([void 0,!0,!1,"-",":"]),normalizationForms:["NFC","NFD","NFKC","NFKD"]};e.exports=u.extend({type:"string",flags:{insensitive:{default:!1},truncate:{default:!1}},terms:{replacements:{init:null}},coerce:{from:"string",method(e,{schema:t,state:n,prefs:r}){const s=t.$_getRule("normalize");s&&(e=e.normalize(s.args.form));const i=t.$_getRule("case");i&&(e="upper"===i.args.direction?e.toLocaleUpperCase():e.toLocaleLowerCase());const o=t.$_getRule("trim");if(o&&o.args.enabled&&(e=e.trim()),t.$_terms.replacements)for(const n of t.$_terms.replacements)e=e.replace(n.pattern,n.replacement);const a=t.$_getRule("hex");if(a&&a.args.options.byteAligned&&e.length%2!=0&&(e=`0${e}`),t.$_getRule("isoDate")){const t=f.isoDate(e);t&&(e=t)}if(t._flags.truncate){const s=t.$_getRule("max");if(s){let i=s.args.limit;if(d.isResolvable(i)&&(i=i.resolve(e,n,r),!d.limit(i)))return{value:e,errors:t.$_createError("any.ref",i,{ref:s.args.limit,arg:"limit",reason:"must be a positive integer"},n,r)};e=e.slice(0,i)}}return{value:e}}},validate(e,{schema:t,error:n}){if("string"!=typeof e)return{value:e,errors:n("string.base")};if(""===e){const r=t.$_getRule("min");if(r&&0===r.args.limit)return;return{value:e,errors:n("string.empty")}}},rules:{alphanum:{method(){return this.$_addRule("alphanum")},validate:(e,t)=>/^[a-zA-Z0-9]+$/.test(e)?e:t.error("string.alphanum")},base64:{method(e={}){return d.assertOptions(e,["paddingRequired","urlSafe"]),e={urlSafe:!1,paddingRequired:!0,...e},r("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),r("boolean"==typeof e.urlSafe,"urlSafe must be boolean"),this.$_addRule({name:"base64",args:{options:e}})},validate:(e,t,{options:n})=>f.base64Regex[n.paddingRequired][n.urlSafe].test(e)?e:t.error("string.base64")},case:{method(e){return r(["lower","upper"].includes(e),"Invalid case:",e),this.$_addRule({name:"case",args:{direction:e}})},validate:(e,t,{direction:n})=>"lower"===n&&e===e.toLocaleLowerCase()||"upper"===n&&e===e.toLocaleUpperCase()?e:t.error(`string.${n}case`),convert:!0},creditCard:{method(){return this.$_addRule("creditCard")},validate(e,t){let n=e.length,r=0,s=1;for(;n--;){const t=e.charAt(n)*s;r+=t-9*(t>9),s^=3}return r>0&&r%10==0?e:t.error("string.creditCard")}},dataUri:{method(e={}){return d.assertOptions(e,["paddingRequired"]),e={paddingRequired:!0,...e},r("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),this.$_addRule({name:"dataUri",args:{options:e}})},validate(e,t,{options:n}){const r=e.match(f.dataUriRegex);if(r){if(!r[2])return e;if("base64"!==r[2])return e;if(f.base64Regex[n.paddingRequired].false.test(r[3]))return e}return t.error("string.dataUri")}},domain:{method(e){e&&d.assertOptions(e,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const t=f.addressOptions(e);return this.$_addRule({name:"domain",args:{options:e},address:t})},validate:(e,t,n,{address:r})=>s.isValid(e,r)?e:t.error("string.domain")},email:{method(e={}){d.assertOptions(e,["allowFullyQualified","allowUnicode","ignoreLength","maxDomainSegments","minDomainSegments","multiple","separator","tlds"]),r(void 0===e.multiple||"boolean"==typeof e.multiple,"multiple option must be an boolean");const t=f.addressOptions(e),n=new RegExp(`\\s*[${e.separator?a(e.separator):","}]\\s*`);return this.$_addRule({name:"email",args:{options:e},regex:n,address:t})},validate(e,t,{options:n},{regex:r,address:s}){const o=n.multiple?e.split(r):[e],a=[];for(const e of o)i.isValid(e,s)||a.push(e);return a.length?t.error("string.email",{value:e,invalids:a}):e}},guid:{alias:"uuid",method(e={}){d.assertOptions(e,["version","separator"]);let t="";if(e.version){const n=[].concat(e.version);r(n.length>=1,"version must have at least 1 valid version specified");const s=new Set;for(let e=0;e<n.length;++e){const i=n[e];r("string"==typeof i,"version at position "+e+" must be a string");const o=f.guidVersions[i.toLowerCase()];r(o,"version at position "+e+" must be one of "+Object.keys(f.guidVersions).join(", ")),r(!s.has(o),"version at position "+e+" must not be a duplicate"),t+=o,s.add(o)}}r(f.guidSeparators.has(e.separator),'separator must be one of true, false, "-", or ":"');const n=void 0===e.separator?"[:-]?":!0===e.separator?"[:-]":!1===e.separator?"[]?":`\\${e.separator}`,s=new RegExp(`^([\\[{\\(]?)[0-9A-F]{8}(${n})[0-9A-F]{4}\\2?[${t||"0-9A-F"}][0-9A-F]{3}\\2?[${t?"89AB":"0-9A-F"}][0-9A-F]{3}\\2?[0-9A-F]{12}([\\]}\\)]?)$`,"i");return this.$_addRule({name:"guid",args:{options:e},regex:s})},validate(e,t,n,{regex:r}){const s=r.exec(e);return s?f.guidBrackets[s[1]]!==s[s.length-1]?t.error("string.guid"):e:t.error("string.guid")}},hex:{method(e={}){return d.assertOptions(e,["byteAligned","prefix"]),e={byteAligned:!1,prefix:!1,...e},r("boolean"==typeof e.byteAligned,"byteAligned must be boolean"),r("boolean"==typeof e.prefix||"optional"===e.prefix,'prefix must be boolean or "optional"'),this.$_addRule({name:"hex",args:{options:e}})},validate:(e,t,{options:n})=>("optional"===n.prefix?f.hexRegex.withOptionalPrefix:!0===n.prefix?f.hexRegex.withPrefix:f.hexRegex.withoutPrefix).test(e)?n.byteAligned&&e.length%2!=0?t.error("string.hexAlign"):e:t.error("string.hex")},hostname:{method(){return this.$_addRule("hostname")},validate:(e,t)=>s.isValid(e,{minDomainSegments:1})||f.ipRegex.test(e)?e:t.error("string.hostname")},insensitive:{method(){return this.$_setFlag("insensitive",!0)}},ip:{method(e={}){d.assertOptions(e,["cidr","version"]);const{cidr:t,versions:n,regex:r}=o.regex(e),s=e.version?n:void 0;return this.$_addRule({name:"ip",args:{options:{cidr:t,version:s}},regex:r})},validate:(e,t,{options:n},{regex:r})=>r.test(e)?e:n.version?t.error("string.ipVersion",{value:e,cidr:n.cidr,version:n.version}):t.error("string.ip",{value:e,cidr:n.cidr})},isoDate:{method(){return this.$_addRule("isoDate")},validate:(e,{error:t})=>f.isoDate(e)?e:t("string.isoDate")},isoDuration:{method(){return this.$_addRule("isoDuration")},validate:(e,t)=>f.isoDurationRegex.test(e)?e:t.error("string.isoDuration")},length:{method(e,t){return f.length(this,"length",e,"=",t)},validate(e,t,{limit:n,encoding:r},{name:s,operator:i,args:o}){const a=!r&&e.length;return d.compare(a,n,i)?e:t.error("string."+s,{limit:o.limit,value:e,encoding:r})},args:[{name:"limit",ref:!0,assert:d.limit,message:"must be a positive integer"},"encoding"]},lowercase:{method(){return this.case("lower")}},max:{method(e,t){return f.length(this,"max",e,"<=",t)},args:["limit","encoding"]},min:{method(e,t){return f.length(this,"min",e,">=",t)},args:["limit","encoding"]},normalize:{method(e="NFC"){return r(f.normalizationForms.includes(e),"normalization form must be one of "+f.normalizationForms.join(", ")),this.$_addRule({name:"normalize",args:{form:e}})},validate:(e,{error:t},{form:n})=>e===e.normalize(n)?e:t("string.normalize",{value:e,form:n}),convert:!0},pattern:{alias:"regex",method(e,t={}){r(e instanceof RegExp,"regex must be a RegExp"),r(!e.flags.includes("g")&&!e.flags.includes("y"),"regex should not use global or sticky mode"),"string"==typeof t&&(t={name:t}),d.assertOptions(t,["invert","name"]);const n=["string.pattern",t.invert?".invert":"",t.name?".name":".base"].join("");return this.$_addRule({name:"pattern",args:{regex:e,options:t},errorCode:n})},validate:(e,t,{regex:n,options:r},{errorCode:s})=>n.test(e)^r.invert?e:t.error(s,{name:r.name,regex:n,value:e}),args:["regex","options"],multi:!0},replace:{method(e,t){"string"==typeof e&&(e=new RegExp(a(e),"g")),r(e instanceof RegExp,"pattern must be a RegExp"),r("string"==typeof t,"replacement must be a String");const n=this.clone();return n.$_terms.replacements||(n.$_terms.replacements=[]),n.$_terms.replacements.push({pattern:e,replacement:t}),n}},token:{method(){return this.$_addRule("token")},validate:(e,t)=>/^\w+$/.test(e)?e:t.error("string.token")},trim:{method(e=!0){return r("boolean"==typeof e,"enabled must be a boolean"),this.$_addRule({name:"trim",args:{enabled:e}})},validate:(e,t,{enabled:n})=>n&&e!==e.trim()?t.error("string.trim"):e,convert:!0},truncate:{method(e=!0){return r("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("truncate",e)}},uppercase:{method(){return this.case("upper")}},uri:{method(e={}){d.assertOptions(e,["allowRelative","allowQuerySquareBrackets","domain","relativeOnly","scheme","encodeUri"]),e.domain&&d.assertOptions(e.domain,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const{regex:t,scheme:n}=l.regex(e),r=e.domain?f.addressOptions(e.domain):null;return this.$_addRule({name:"uri",args:{options:e},regex:t,domain:r,scheme:n})},validate(e,t,{options:n},{regex:r,domain:i,scheme:o}){if(["http:/","https:/"].includes(e))return t.error("string.uri");let a=r.exec(e);if(!a&&t.prefs.convert&&n.encodeUri){const t=encodeURI(e);a=r.exec(t),a&&(e=t)}if(a){const r=a[1]||a[2];return!i||n.allowRelative&&!r||s.isValid(r,i)?e:t.error("string.domain",{value:r})}return n.relativeOnly?t.error("string.uriRelativeOnly"):n.scheme?t.error("string.uriCustomScheme",{scheme:o,value:e}):t.error("string.uri")}}},manifest:{build(e,t){if(t.replacements)for(const{pattern:n,replacement:r}of t.replacements)e=e.replace(n,r);return e}},messages:{"string.alphanum":"{{#label}} must only contain alpha-numeric characters","string.base":"{{#label}} must be a string","string.base64":"{{#label}} must be a valid base64 string","string.creditCard":"{{#label}} must be a credit card","string.dataUri":"{{#label}} must be a valid dataUri string","string.domain":"{{#label}} must contain a valid domain name","string.email":"{{#label}} must be a valid email","string.empty":"{{#label}} is not allowed to be empty","string.guid":"{{#label}} must be a valid GUID","string.hex":"{{#label}} must only contain hexadecimal characters","string.hexAlign":"{{#label}} hex decoded representation must be byte aligned","string.hostname":"{{#label}} must be a valid hostname","string.ip":"{{#label}} must be a valid ip address with a {{#cidr}} CIDR","string.ipVersion":"{{#label}} must be a valid ip address of one of the following versions {{#version}} with a {{#cidr}} CIDR","string.isoDate":"{{#label}} must be in iso format","string.isoDuration":"{{#label}} must be a valid ISO 8601 duration","string.length":"{{#label}} length must be {{#limit}} characters long","string.lowercase":"{{#label}} must only contain lowercase characters","string.max":"{{#label}} length must be less than or equal to {{#limit}} characters long","string.min":"{{#label}} length must be at least {{#limit}} characters long","string.normalize":"{{#label}} must be unicode normalized in the {{#form}} form","string.token":"{{#label}} must only contain alpha-numeric and underscore characters","string.pattern.base":"{{#label}} with value {:[.]} fails to match the required pattern: {{#regex}}","string.pattern.name":"{{#label}} with value {:[.]} fails to match the {{#name}} pattern","string.pattern.invert.base":"{{#label}} with value {:[.]} matches the inverted pattern: {{#regex}}","string.pattern.invert.name":"{{#label}} with value {:[.]} matches the inverted {{#name}} pattern","string.trim":"{{#label}} must not have leading or trailing whitespace","string.uri":"{{#label}} must be a valid uri","string.uriCustomScheme":"{{#label}} must be a valid uri with a scheme matching the {{#scheme}} pattern","string.uriRelativeOnly":"{{#label}} must be a valid relative uri","string.uppercase":"{{#label}} must only contain uppercase characters"}}),f.addressOptions=function(e){if(!e)return f.tlds||e;if(r(void 0===e.minDomainSegments||Number.isSafeInteger(e.minDomainSegments)&&e.minDomainSegments>0,"minDomainSegments must be a positive integer"),r(void 0===e.maxDomainSegments||Number.isSafeInteger(e.maxDomainSegments)&&e.maxDomainSegments>0,"maxDomainSegments must be a positive integer"),!1===e.tlds)return e;if(!0===e.tlds||void 0===e.tlds)return r(f.tlds,"Built-in TLD list disabled"),Object.assign({},e,f.tlds);r("object"==typeof e.tlds,"tlds must be true, false, or an object");const t=e.tlds.deny;if(t)return Array.isArray(t)&&(e=Object.assign({},e,{tlds:{deny:new Set(t)}})),r(e.tlds.deny instanceof Set,"tlds.deny must be an array, Set, or boolean"),r(!e.tlds.allow,"Cannot specify both tlds.allow and tlds.deny lists"),f.validateTlds(e.tlds.deny,"tlds.deny"),e;const n=e.tlds.allow;return n?!0===n?(r(f.tlds,"Built-in TLD list disabled"),Object.assign({},e,f.tlds)):(Array.isArray(n)&&(e=Object.assign({},e,{tlds:{allow:new Set(n)}})),r(e.tlds.allow instanceof Set,"tlds.allow must be an array, Set, or boolean"),f.validateTlds(e.tlds.allow,"tlds.allow"),e):e},f.validateTlds=function(e,t){for(const n of e)r(s.isValid(n,{minDomainSegments:1,maxDomainSegments:1}),`${t} must contain valid top level domain names`)},f.isoDate=function(e){if(!d.isIsoDate(e))return null;/.*T.*[+-]\d\d$/.test(e)&&(e+="00");const t=new Date(e);return isNaN(t.getTime())?null:t.toISOString()},f.length=function(e,t,n,s,i){return r(!i||!1,"Invalid encoding:",i),e.$_addRule({name:t,method:"length",args:{limit:n,encoding:i},operator:s})}},8826:(e,t,n)=>{const r=n(375),s=n(8068),i={};i.Map=class extends Map{slice(){return new i.Map(this)}},e.exports=s.extend({type:"symbol",terms:{map:{init:new i.Map}},coerce:{method(e,{schema:t,error:n}){const r=t.$_terms.map.get(e);return r&&(e=r),t._flags.only&&"symbol"!=typeof e?{value:e,errors:n("symbol.map",{map:t.$_terms.map})}:{value:e}}},validate(e,{error:t}){if("symbol"!=typeof e)return{value:e,errors:t("symbol.base")}},rules:{map:{method(e){e&&!e[Symbol.iterator]&&"object"==typeof e&&(e=Object.entries(e)),r(e&&e[Symbol.iterator],"Iterable must be an iterable or object");const t=this.clone(),n=[];for(const s of e){r(s&&s[Symbol.iterator],"Entry must be an iterable");const[e,i]=s;r("object"!=typeof e&&"function"!=typeof e&&"symbol"!=typeof e,"Key must not be of type object, function, or Symbol"),r("symbol"==typeof i,"Value must be a Symbol"),t.$_terms.map.set(e,i),n.push(i)}return t.valid(...n)}}},manifest:{build:(e,t)=>(t.map&&(e=e.map(t.map)),e)},messages:{"symbol.base":"{{#label}} must be a symbol","symbol.map":"{{#label}} must be one of {{#map}}"}})},8863:(e,t,n)=>{const r=n(375),s=n(8571),i=n(738),o=n(9621),a=n(8160),c=n(6354),l=n(493),u={result:Symbol("result")};t.entry=function(e,t,n){let s=a.defaults;n&&(r(void 0===n.warnings,"Cannot override warnings preference in synchronous validation"),r(void 0===n.artifacts,"Cannot override artifacts preference in synchronous validation"),s=a.preferences(a.defaults,n));const i=u.entry(e,t,s);r(!i.mainstay.externals.length,"Schema with external rules must use validateAsync()");const o={value:i.value};return i.error&&(o.error=i.error),i.mainstay.warnings.length&&(o.warning=c.details(i.mainstay.warnings)),i.mainstay.debug&&(o.debug=i.mainstay.debug),i.mainstay.artifacts&&(o.artifacts=i.mainstay.artifacts),o},t.entryAsync=async function(e,t,n){let r=a.defaults;n&&(r=a.preferences(a.defaults,n));const s=u.entry(e,t,r),i=s.mainstay;if(s.error)throw i.debug&&(s.error.debug=i.debug),s.error;if(i.externals.length){let t=s.value;const l=[];for(const s of i.externals){const d=s.state.path,f="link"===s.schema.type?i.links.get(s.schema):null;let h,b,p=t;const m=d.length?[t]:[],y=d.length?o(e,d):e;if(d.length){h=d[d.length-1];let e=t;for(const t of d.slice(0,-1))e=e[t],m.unshift(e);b=m[0],p=b[h]}try{const e=(e,t)=>(f||s.schema).$_createError(e,p,t,s.state,r),o=await s.method(p,{schema:s.schema,linked:f,state:s.state,prefs:n,original:y,error:e,errorsArray:u.errorsArray,warn:(e,t)=>i.warnings.push((f||s.schema).$_createError(e,p,t,s.state,r)),message:(e,t)=>(f||s.schema).$_createError("external",p,t,s.state,r,{messages:e})});if(void 0===o||o===p)continue;if(o instanceof c.Report){if(i.tracer.log(s.schema,s.state,"rule","external","error"),l.push(o),r.abortEarly)break;continue}if(Array.isArray(o)&&o[a.symbols.errors]){if(i.tracer.log(s.schema,s.state,"rule","external","error"),l.push(...o),r.abortEarly)break;continue}b?(i.tracer.value(s.state,"rule",p,o,"external"),b[h]=o):(i.tracer.value(s.state,"rule",t,o,"external"),t=o)}catch(e){throw r.errors.label&&(e.message+=` (${s.label})`),e}}if(s.value=t,l.length)throw s.error=c.process(l,e,r),i.debug&&(s.error.debug=i.debug),s.error}if(!r.warnings&&!r.debug&&!r.artifacts)return s.value;const l={value:s.value};return i.warnings.length&&(l.warning=c.details(i.warnings)),i.debug&&(l.debug=i.debug),i.artifacts&&(l.artifacts=i.artifacts),l},u.Mainstay=class{constructor(e,t,n){this.externals=[],this.warnings=[],this.tracer=e,this.debug=t,this.links=n,this.shadow=null,this.artifacts=null,this._snapshots=[]}snapshot(){this._snapshots.push({externals:this.externals.slice(),warnings:this.warnings.slice()})}restore(){const e=this._snapshots.pop();this.externals=e.externals,this.warnings=e.warnings}commit(){this._snapshots.pop()}},u.entry=function(e,n,r){const{tracer:s,cleanup:i}=u.tracer(n,r),o=r.debug?[]:null,a=n._ids._schemaChain?new Map:null,d=new u.Mainstay(s,o,a),f=n._ids._schemaChain?[{schema:n}]:null,h=new l([],[],{mainstay:d,schemas:f}),b=t.validate(e,n,h,r);i&&n.$_root.untrace();const p=c.process(b.errors,e,r);return{value:b.value,error:p,mainstay:d}},u.tracer=function(e,t){return e.$_root._tracer?{tracer:e.$_root._tracer._register(e)}:t.debug?(r(e.$_root.trace,"Debug mode not supported"),{tracer:e.$_root.trace()._register(e),cleanup:!0}):{tracer:u.ignore}},t.validate=function(e,t,n,r,s={}){if(t.$_terms.whens&&(t=t._generate(e,n,r).schema),t._preferences&&(r=u.prefs(t,r)),t._cache&&r.cache){const r=t._cache.get(e);if(n.mainstay.tracer.debug(n,"validate","cached",!!r),r)return r}const i=(s,i,o)=>t.$_createError(s,e,i,o||n,r),o={original:e,prefs:r,schema:t,state:n,error:i,errorsArray:u.errorsArray,warn:(e,t,r)=>n.mainstay.warnings.push(i(e,t,r)),message:(s,i)=>t.$_createError("custom",e,i,n,r,{messages:s})};n.mainstay.tracer.entry(t,n);const c=t._definition;if(c.prepare&&void 0!==e&&r.convert){const t=c.prepare(e,o);if(t){if(n.mainstay.tracer.value(n,"prepare",e,t.value),t.errors)return u.finalize(t.value,[].concat(t.errors),o);e=t.value}}if(c.coerce&&void 0!==e&&r.convert&&(!c.coerce.from||c.coerce.from.includes(typeof e))){const t=c.coerce.method(e,o);if(t){if(n.mainstay.tracer.value(n,"coerced",e,t.value),t.errors)return u.finalize(t.value,[].concat(t.errors),o);e=t.value}}const l=t._flags.empty;l&&l.$_match(u.trim(e,t),n.nest(l),a.defaults)&&(n.mainstay.tracer.value(n,"empty",e,void 0),e=void 0);const d=s.presence||t._flags.presence||(t._flags._endedSwitch?null:r.presence);if(void 0===e){if("forbidden"===d)return u.finalize(e,null,o);if("required"===d)return u.finalize(e,[t.$_createError("any.required",e,null,n,r)],o);if("optional"===d){if(t._flags.default!==a.symbols.deepDefault)return u.finalize(e,null,o);n.mainstay.tracer.value(n,"default",e,{}),e={}}}else if("forbidden"===d)return u.finalize(e,[t.$_createError("any.unknown",e,null,n,r)],o);const f=[];if(t._valids){const s=t._valids.get(e,n,r,t._flags.insensitive);if(s)return r.convert&&(n.mainstay.tracer.value(n,"valids",e,s.value),e=s.value),n.mainstay.tracer.filter(t,n,"valid",s),u.finalize(e,null,o);if(t._flags.only){const s=t.$_createError("any.only",e,{valids:t._valids.values({display:!0})},n,r);if(r.abortEarly)return u.finalize(e,[s],o);f.push(s)}}if(t._invalids){const s=t._invalids.get(e,n,r,t._flags.insensitive);if(s){n.mainstay.tracer.filter(t,n,"invalid",s);const i=t.$_createError("any.invalid",e,{invalids:t._invalids.values({display:!0})},n,r);if(r.abortEarly)return u.finalize(e,[i],o);f.push(i)}}if(c.validate){const t=c.validate(e,o);if(t&&(n.mainstay.tracer.value(n,"base",e,t.value),e=t.value,t.errors)){if(!Array.isArray(t.errors))return f.push(t.errors),u.finalize(e,f,o);if(t.errors.length)return f.push(...t.errors),u.finalize(e,f,o)}}return t._rules.length?u.rules(e,f,o):u.finalize(e,f,o)},u.rules=function(e,t,n){const{schema:r,state:s,prefs:i}=n;for(const o of r._rules){const c=r._definition.rules[o.method];if(c.convert&&i.convert){s.mainstay.tracer.log(r,s,"rule",o.name,"full");continue}let l,d=o.args;if(o._resolve.length){d=Object.assign({},d);for(const t of o._resolve){const n=c.argsByName.get(t),o=d[t].resolve(e,s,i),u=n.normalize?n.normalize(o):o,f=a.validateArg(u,null,n);if(f){l=r.$_createError("any.ref",o,{arg:t,ref:d[t],reason:f},s,i);break}d[t]=u}}l=l||c.validate(e,n,d,o);const f=u.rule(l,o);if(f.errors){if(s.mainstay.tracer.log(r,s,"rule",o.name,"error"),o.warn){s.mainstay.warnings.push(...f.errors);continue}if(i.abortEarly)return u.finalize(e,f.errors,n);t.push(...f.errors)}else s.mainstay.tracer.log(r,s,"rule",o.name,"pass"),s.mainstay.tracer.value(s,"rule",e,f.value,o.name),e=f.value}return u.finalize(e,t,n)},u.rule=function(e,t){return e instanceof c.Report?(u.error(e,t),{errors:[e],value:null}):Array.isArray(e)&&e[a.symbols.errors]?(e.forEach((e=>u.error(e,t))),{errors:e,value:null}):{errors:null,value:e}},u.error=function(e,t){return t.message&&e._setTemplate(t.message),e},u.finalize=function(e,t,n){t=t||[];const{schema:s,state:i,prefs:o}=n;if(t.length){const r=u.default("failover",void 0,t,n);void 0!==r&&(i.mainstay.tracer.value(i,"failover",e,r),e=r,t=[])}if(t.length&&s._flags.error)if("function"==typeof s._flags.error){t=s._flags.error(t),Array.isArray(t)||(t=[t]);for(const e of t)r(e instanceof Error||e instanceof c.Report,"error() must return an Error object")}else t=[s._flags.error];if(void 0===e){const r=u.default("default",e,t,n);i.mainstay.tracer.value(i,"default",e,r),e=r}if(s._flags.cast&&void 0!==e){const t=s._definition.cast[s._flags.cast];if(t.from(e)){const r=t.to(e,n);i.mainstay.tracer.value(i,"cast",e,r,s._flags.cast),e=r}}if(s.$_terms.externals&&o.externals&&!1!==o._externals)for(const{method:e}of s.$_terms.externals)i.mainstay.externals.push({method:e,schema:s,state:i,label:c.label(s._flags,i,o)});const a={value:e,errors:t.length?t:null};return s._flags.result&&(a.value="strip"===s._flags.result?void 0:n.original,i.mainstay.tracer.value(i,s._flags.result,e,a.value),i.shadow(e,s._flags.result)),s._cache&&!1!==o.cache&&!s._refs.length&&s._cache.set(n.original,a),void 0===e||a.errors||void 0===s._flags.artifact||(i.mainstay.artifacts=i.mainstay.artifacts||new Map,i.mainstay.artifacts.has(s._flags.artifact)||i.mainstay.artifacts.set(s._flags.artifact,[]),i.mainstay.artifacts.get(s._flags.artifact).push(i.path)),a},u.prefs=function(e,t){const n=t===a.defaults;return n&&e._preferences[a.symbols.prefs]?e._preferences[a.symbols.prefs]:(t=a.preferences(t,e._preferences),n&&(e._preferences[a.symbols.prefs]=t),t)},u.default=function(e,t,n,r){const{schema:i,state:o,prefs:c}=r,l=i._flags[e];if(c.noDefaults||void 0===l)return t;if(o.mainstay.tracer.log(i,o,"rule",e,"full"),!l)return l;if("function"==typeof l){const a=l.length?[s(o.ancestors[0]),r]:[];try{return l(...a)}catch(t){return void n.push(i.$_createError(`any.${e}`,null,{error:t},o,c))}}return"object"!=typeof l?l:l[a.symbols.literal]?l.literal:a.isResolvable(l)?l.resolve(t,o,c):s(l)},u.trim=function(e,t){if("string"!=typeof e)return e;const n=t.$_getRule("trim");return n&&n.args.enabled?e.trim():e},u.ignore={active:!1,debug:i,entry:i,filter:i,log:i,resolve:i,value:i},u.errorsArray=function(){const e=[];return e[a.symbols.errors]=!0,e}},2036:(e,t,n)=>{const r=n(375),s=n(9474),i=n(8160),o={};e.exports=o.Values=class{constructor(e,t){this._values=new Set(e),this._refs=new Set(t),this._lowercase=o.lowercases(e),this._override=!1}get length(){return this._values.size+this._refs.size}add(e,t){i.isResolvable(e)?this._refs.has(e)||(this._refs.add(e),t&&t.register(e)):this.has(e,null,null,!1)||(this._values.add(e),"string"==typeof e&&this._lowercase.set(e.toLowerCase(),e))}static merge(e,t,n){if(e=e||new o.Values,t){if(t._override)return t.clone();for(const n of[...t._values,...t._refs])e.add(n)}if(n)for(const t of[...n._values,...n._refs])e.remove(t);return e.length?e:null}remove(e){i.isResolvable(e)?this._refs.delete(e):(this._values.delete(e),"string"==typeof e&&this._lowercase.delete(e.toLowerCase()))}has(e,t,n,r){return!!this.get(e,t,n,r)}get(e,t,n,r){if(!this.length)return!1;if(this._values.has(e))return{value:e};if("string"==typeof e&&e&&r){const t=this._lowercase.get(e.toLowerCase());if(t)return{value:t}}if(!this._refs.size&&"object"!=typeof e)return!1;if("object"==typeof e)for(const t of this._values)if(s(t,e))return{value:t};if(t)for(const i of this._refs){const o=i.resolve(e,t,n,null,{in:!0});if(void 0===o)continue;const a=i.in&&"object"==typeof o?Array.isArray(o)?o:Object.keys(o):[o];for(const t of a)if(typeof t==typeof e)if(r&&e&&"string"==typeof e){if(t.toLowerCase()===e.toLowerCase())return{value:t,ref:i}}else if(s(t,e))return{value:t,ref:i}}return!1}override(){this._override=!0}values(e){if(e&&e.display){const e=[];for(const t of[...this._values,...this._refs])void 0!==t&&e.push(t);return e}return Array.from([...this._values,...this._refs])}clone(){const e=new o.Values(this._values,this._refs);return e._override=this._override,e}concat(e){r(!e._override,"Cannot concat override set of values");const t=new o.Values([...this._values,...e._values],[...this._refs,...e._refs]);return t._override=this._override,t}describe(){const e=[];this._override&&e.push({override:!0});for(const t of this._values.values())e.push(t&&"object"==typeof t?{value:t}:t);for(const t of this._refs.values())e.push(t.describe());return e}},o.Values.prototype[i.symbols.values]=!0,o.Values.prototype.slice=o.Values.prototype.clone,o.lowercases=function(e){const t=new Map;if(e)for(const n of e)"string"==typeof n&&t.set(n.toLowerCase(),n);return t}},978:(e,t,n)=>{const r=n(375),s=n(8571),i=n(1687),o=n(9621),a={};e.exports=function(e,t,n={}){if(r(e&&"object"==typeof e,"Invalid defaults value: must be an object"),r(!t||!0===t||"object"==typeof t,"Invalid source value: must be true, falsy or an object"),r("object"==typeof n,"Invalid options: must be an object"),!t)return null;if(n.shallow)return a.applyToDefaultsWithShallow(e,t,n);const o=s(e);if(!0===t)return o;const c=void 0!==n.nullOverride&&n.nullOverride;return i(o,t,{nullOverride:c,mergeArrays:!1})},a.applyToDefaultsWithShallow=function(e,t,n){const c=n.shallow;r(Array.isArray(c),"Invalid keys");const l=new Map,u=!0===t?null:new Set;for(let n of c){n=Array.isArray(n)?n:n.split(".");const r=o(e,n);r&&"object"==typeof r?l.set(r,u&&o(t,n)||r):u&&u.add(n)}const d=s(e,{},l);if(!u)return d;for(const e of u)a.reachCopy(d,t,e);const f=void 0!==n.nullOverride&&n.nullOverride;return i(d,t,{nullOverride:f,mergeArrays:!1})},a.reachCopy=function(e,t,n){for(const e of n){if(!(e in t))return;const n=t[e];if("object"!=typeof n||null===n)return;t=n}const r=t;let s=e;for(let e=0;e<n.length-1;++e){const t=n[e];"object"!=typeof s[t]&&(s[t]={}),s=s[t]}s[n[n.length-1]]=r}},375:(e,t,n)=>{const r=n(7916);e.exports=function(e,...t){if(!e){if(1===t.length&&t[0]instanceof Error)throw t[0];throw new r(t)}}},8571:(e,t,n)=>{const r=n(9621),s=n(4277),i=n(7043),o={needsProtoHack:new Set([s.set,s.map,s.weakSet,s.weakMap])};e.exports=o.clone=function(e,t={},n=null){if("object"!=typeof e||null===e)return e;let r=o.clone,a=n;if(t.shallow){if(!0!==t.shallow)return o.cloneWithShallow(e,t);r=e=>e}else if(a){const t=a.get(e);if(t)return t}else a=new Map;const c=s.getInternalProto(e);if(c===s.buffer)return!1;if(c===s.date)return new Date(e.getTime());if(c===s.regex)return new RegExp(e);const l=o.base(e,c,t);if(l===e)return e;if(a&&a.set(e,l),c===s.set)for(const n of e)l.add(r(n,t,a));else if(c===s.map)for(const[n,s]of e)l.set(n,r(s,t,a));const u=i.keys(e,t);for(const n of u){if("__proto__"===n)continue;if(c===s.array&&"length"===n){l.length=e.length;continue}const i=Object.getOwnPropertyDescriptor(e,n);i?i.get||i.set?Object.defineProperty(l,n,i):i.enumerable?l[n]=r(e[n],t,a):Object.defineProperty(l,n,{enumerable:!1,writable:!0,configurable:!0,value:r(e[n],t,a)}):Object.defineProperty(l,n,{enumerable:!0,writable:!0,configurable:!0,value:r(e[n],t,a)})}return l},o.cloneWithShallow=function(e,t){const n=t.shallow;(t=Object.assign({},t)).shallow=!1;const s=new Map;for(const t of n){const n=r(e,t);"object"!=typeof n&&"function"!=typeof n||s.set(n,n)}return o.clone(e,t,s)},o.base=function(e,t,n){if(!1===n.prototype)return o.needsProtoHack.has(t)?new t.constructor:t===s.array?[]:{};const r=Object.getPrototypeOf(e);if(r&&r.isImmutable)return e;if(t===s.array){const e=[];return r!==t&&Object.setPrototypeOf(e,r),e}if(o.needsProtoHack.has(t)){const e=new r.constructor;return r!==t&&Object.setPrototypeOf(e,r),e}return Object.create(r)}},9474:(e,t,n)=>{const r=n(4277),s={mismatched:null};e.exports=function(e,t,n){return n=Object.assign({prototype:!0},n),!!s.isDeepEqual(e,t,n,[])},s.isDeepEqual=function(e,t,n,i){if(e===t)return 0!==e||1/e==1/t;const o=typeof e;if(o!==typeof t)return!1;if(null===e||null===t)return!1;if("function"===o){if(!n.deepFunction||e.toString()!==t.toString())return!1}else if("object"!==o)return e!=e&&t!=t;const a=s.getSharedType(e,t,!!n.prototype);switch(a){case r.buffer:return!1;case r.promise:return e===t;case r.regex:return e.toString()===t.toString();case s.mismatched:return!1}for(let n=i.length-1;n>=0;--n)if(i[n].isSame(e,t))return!0;i.push(new s.SeenEntry(e,t));try{return!!s.isDeepEqualObj(a,e,t,n,i)}finally{i.pop()}},s.getSharedType=function(e,t,n){if(n)return Object.getPrototypeOf(e)!==Object.getPrototypeOf(t)?s.mismatched:r.getInternalProto(e);const i=r.getInternalProto(e);return i!==r.getInternalProto(t)?s.mismatched:i},s.valueOf=function(e){const t=e.valueOf;if(void 0===t)return e;try{return t.call(e)}catch(e){return e}},s.hasOwnEnumerableProperty=function(e,t){return Object.prototype.propertyIsEnumerable.call(e,t)},s.isSetSimpleEqual=function(e,t){for(const n of Set.prototype.values.call(e))if(!Set.prototype.has.call(t,n))return!1;return!0},s.isDeepEqualObj=function(e,t,n,i,o){const{isDeepEqual:a,valueOf:c,hasOwnEnumerableProperty:l}=s,{keys:u,getOwnPropertySymbols:d}=Object;if(e===r.array){if(!i.part){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!a(t[e],n[e],i,o))return!1;return!0}for(const e of t)for(const t of n)if(a(e,t,i,o))return!0}else if(e===r.set){if(t.size!==n.size)return!1;if(!s.isSetSimpleEqual(t,n)){const e=new Set(Set.prototype.values.call(n));for(const n of Set.prototype.values.call(t)){if(e.delete(n))continue;let t=!1;for(const r of e)if(a(n,r,i,o)){e.delete(r),t=!0;break}if(!t)return!1}}}else if(e===r.map){if(t.size!==n.size)return!1;for(const[e,r]of Map.prototype.entries.call(t)){if(void 0===r&&!Map.prototype.has.call(n,e))return!1;if(!a(r,Map.prototype.get.call(n,e),i,o))return!1}}else if(e===r.error&&(t.name!==n.name||t.message!==n.message))return!1;const f=c(t),h=c(n);if((t!==f||n!==h)&&!a(f,h,i,o))return!1;const b=u(t);if(!i.part&&b.length!==u(n).length&&!i.skip)return!1;let p=0;for(const e of b)if(i.skip&&i.skip.includes(e))void 0===n[e]&&++p;else{if(!l(n,e))return!1;if(!a(t[e],n[e],i,o))return!1}if(!i.part&&b.length-p!==u(n).length)return!1;if(!1!==i.symbols){const e=d(t),r=new Set(d(n));for(const s of e){if(!i.skip||!i.skip.includes(s))if(l(t,s)){if(!l(n,s))return!1;if(!a(t[s],n[s],i,o))return!1}else if(l(n,s))return!1;r.delete(s)}for(const e of r)if(l(n,e))return!1}return!0},s.SeenEntry=class{constructor(e,t){this.obj=e,this.ref=t}isSame(e,t){return this.obj===e&&this.ref===t}}},7916:(e,t,n)=>{const r=n(8761);e.exports=class extends Error{constructor(e){super(e.filter((e=>""!==e)).map((e=>"string"==typeof e?e:e instanceof Error?e.message:r(e))).join(" ")||"Unknown error"),"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t.assert)}}},5277:e=>{const t={};e.exports=function(e){if(!e)return"";let n="";for(let r=0;r<e.length;++r){const s=e.charCodeAt(r);t.isSafe(s)?n+=e[r]:n+=t.escapeHtmlChar(s)}return n},t.escapeHtmlChar=function(e){return t.namedHtml.get(e)||(e>=256?"&#"+e+";":`&#x${e.toString(16).padStart(2,"0")};`)},t.isSafe=function(e){return t.safeCharCodes.has(e)},t.namedHtml=new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[34,"&quot;"],[160,"&nbsp;"],[162,"&cent;"],[163,"&pound;"],[164,"&curren;"],[169,"&copy;"],[174,"&reg;"]]),t.safeCharCodes=function(){const e=new Set;for(let t=32;t<123;++t)(t>=97||t>=65&&t<=90||t>=48&&t<=57||32===t||46===t||44===t||45===t||58===t||95===t)&&e.add(t);return e}()},6064:e=>{e.exports=function(e){return e.replace(/[\^\$\.\*\+\-\?\=\!\:\|\\\/\(\)\[\]\{\}\,]/g,"\\$&")}},738:e=>{e.exports=function(){}},1687:(e,t,n)=>{const r=n(375),s=n(8571),i=n(7043),o={};e.exports=o.merge=function(e,t,n){if(r(e&&"object"==typeof e,"Invalid target value: must be an object"),r(null==t||"object"==typeof t,"Invalid source value: must be null, undefined, or an object"),!t)return e;if(n=Object.assign({nullOverride:!0,mergeArrays:!0},n),Array.isArray(t)){r(Array.isArray(e),"Cannot merge array onto an object"),n.mergeArrays||(e.length=0);for(let r=0;r<t.length;++r)e.push(s(t[r],{symbols:n.symbols}));return e}const a=i.keys(t,n);for(let r=0;r<a.length;++r){const i=a[r];if("__proto__"===i||!Object.prototype.propertyIsEnumerable.call(t,i))continue;const c=t[i];if(c&&"object"==typeof c){if(e[i]===c)continue;!e[i]||"object"!=typeof e[i]||Array.isArray(e[i])!==Array.isArray(c)||c instanceof Date||c instanceof RegExp?e[i]=s(c,{symbols:n.symbols}):o.merge(e[i],c,n)}else(null!=c||n.nullOverride)&&(e[i]=c)}return e}},9621:(e,t,n)=>{const r=n(375),s={};e.exports=function(e,t,n){if(!1===t||null==t)return e;"string"==typeof(n=n||{})&&(n={separator:n});const i=Array.isArray(t);r(!i||!n.separator,"Separator option is not valid for array-based chain");const o=i?t:t.split(n.separator||".");let a=e;for(let e=0;e<o.length;++e){let i=o[e];const c=n.iterables&&s.iterables(a);if(Array.isArray(a)||"set"===c){const e=Number(i);Number.isInteger(e)&&(i=e<0?a.length+e:e)}if(!a||"function"==typeof a&&!1===n.functions||!c&&void 0===a[i]){r(!n.strict||e+1===o.length,"Missing segment",i,"in reach path ",t),r("object"==typeof a||!0===n.functions||"function"!=typeof a,"Invalid segment",i,"in reach path ",t),a=n.default;break}a=c?"set"===c?[...a][i]:a.get(i):a[i]}return a},s.iterables=function(e){return e instanceof Set?"set":e instanceof Map?"map":void 0}},8761:e=>{e.exports=function(...e){try{return JSON.stringify(...e)}catch(e){return"[Cannot display object: "+e.message+"]"}}},4277:(e,t)=>{const n={};t=e.exports={array:Array.prototype,buffer:!1,date:Date.prototype,error:Error.prototype,generic:Object.prototype,map:Map.prototype,promise:Promise.prototype,regex:RegExp.prototype,set:Set.prototype,weakMap:WeakMap.prototype,weakSet:WeakSet.prototype},n.typeMap=new Map([["[object Error]",t.error],["[object Map]",t.map],["[object Promise]",t.promise],["[object Set]",t.set],["[object WeakMap]",t.weakMap],["[object WeakSet]",t.weakSet]]),t.getInternalProto=function(e){if(Array.isArray(e))return t.array;if(e instanceof Date)return t.date;if(e instanceof RegExp)return t.regex;if(e instanceof Error)return t.error;const r=Object.prototype.toString.call(e);return n.typeMap.get(r)||t.generic}},7043:(e,t)=>{t.keys=function(e,t={}){return!1!==t.symbols?Reflect.ownKeys(e):Object.getOwnPropertyNames(e)}},3652:(e,t,n)=>{const r=n(375),s={};t.Sorter=class{constructor(){this._items=[],this.nodes=[]}add(e,t){const n=[].concat((t=t||{}).before||[]),s=[].concat(t.after||[]),i=t.group||"?",o=t.sort||0;r(!n.includes(i),`Item cannot come before itself: ${i}`),r(!n.includes("?"),"Item cannot come before unassociated items"),r(!s.includes(i),`Item cannot come after itself: ${i}`),r(!s.includes("?"),"Item cannot come after unassociated items"),Array.isArray(e)||(e=[e]);for(const t of e){const e={seq:this._items.length,sort:o,before:n,after:s,group:i,node:t};this._items.push(e)}if(!t.manual){const e=this._sort();r(e,"item","?"!==i?`added into group ${i}`:"","created a dependencies error")}return this.nodes}merge(e){Array.isArray(e)||(e=[e]);for(const t of e)if(t)for(const e of t._items)this._items.push(Object.assign({},e));this._items.sort(s.mergeSort);for(let e=0;e<this._items.length;++e)this._items[e].seq=e;const t=this._sort();return r(t,"merge created a dependencies error"),this.nodes}sort(){const e=this._sort();return r(e,"sort created a dependencies error"),this.nodes}_sort(){const e={},t=Object.create(null),n=Object.create(null);for(const r of this._items){const s=r.seq,i=r.group;n[i]=n[i]||[],n[i].push(s),e[s]=r.before;for(const e of r.after)t[e]=t[e]||[],t[e].push(s)}for(const t in e){const r=[];for(const s in e[t]){const i=e[t][s];n[i]=n[i]||[],r.push(...n[i])}e[t]=r}for(const r in t)if(n[r])for(const s of n[r])e[s].push(...t[r]);const r={};for(const t in e){const n=e[t];for(const e of n)r[e]=r[e]||[],r[e].push(t)}const s={},i=[];for(let e=0;e<this._items.length;++e){let t=e;if(r[e]){t=null;for(let e=0;e<this._items.length;++e){if(!0===s[e])continue;r[e]||(r[e]=[]);const n=r[e].length;let i=0;for(let t=0;t<n;++t)s[r[e][t]]&&++i;if(i===n){t=e;break}}}null!==t&&(s[t]=!0,i.push(t))}if(i.length!==this._items.length)return!1;const o={};for(const e of this._items)o[e.seq]=e;this._items=[],this.nodes=[];for(const e of i){const t=o[e];this.nodes.push(t.node),this._items.push(t)}return!0}},s.mergeSort=(e,t)=>e.sort===t.sort?0:e.sort<t.sort?-1:1},5380:(e,t,n)=>{const r=n(443),s=n(2178),i={minDomainSegments:2,nonAsciiRx:/[^\x00-\x7f]/,domainControlRx:/[\x00-\x20@\:\/\\#!\$&\'\(\)\*\+,;=\?]/,tldSegmentRx:/^[a-zA-Z](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,domainSegmentRx:/^[a-zA-Z0-9](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,URL:r.URL||URL};t.analyze=function(e,t={}){if(!e)return s.code("DOMAIN_NON_EMPTY_STRING");if("string"!=typeof e)throw new Error("Invalid input: domain must be a string");if(e.length>256)return s.code("DOMAIN_TOO_LONG");if(i.nonAsciiRx.test(e)){if(!1===t.allowUnicode)return s.code("DOMAIN_INVALID_UNICODE_CHARS");e=e.normalize("NFC")}if(i.domainControlRx.test(e))return s.code("DOMAIN_INVALID_CHARS");e=i.punycode(e),t.allowFullyQualified&&"."===e[e.length-1]&&(e=e.slice(0,-1));const n=t.minDomainSegments||i.minDomainSegments,r=e.split(".");if(r.length<n)return s.code("DOMAIN_SEGMENTS_COUNT");if(t.maxDomainSegments&&r.length>t.maxDomainSegments)return s.code("DOMAIN_SEGMENTS_COUNT_MAX");const o=t.tlds;if(o){const e=r[r.length-1].toLowerCase();if(o.deny&&o.deny.has(e)||o.allow&&!o.allow.has(e))return s.code("DOMAIN_FORBIDDEN_TLDS")}for(let e=0;e<r.length;++e){const t=r[e];if(!t.length)return s.code("DOMAIN_EMPTY_SEGMENT");if(t.length>63)return s.code("DOMAIN_LONG_SEGMENT");if(e<r.length-1){if(!i.domainSegmentRx.test(t))return s.code("DOMAIN_INVALID_CHARS")}else if(!i.tldSegmentRx.test(t))return s.code("DOMAIN_INVALID_TLDS_CHARS")}return null},t.isValid=function(e,n){return!t.analyze(e,n)},i.punycode=function(e){e.includes("%")&&(e=e.replace(/%/g,"%25"));try{return new i.URL(`http://${e}`).host}catch(t){return e}}},1745:(e,t,n)=>{const r=n(9848),s=n(5380),i=n(2178),o={nonAsciiRx:/[^\x00-\x7f]/,encoder:new(r.TextEncoder||TextEncoder)};t.analyze=function(e,t){return o.email(e,t)},t.isValid=function(e,t){return!o.email(e,t)},o.email=function(e,t={}){if("string"!=typeof e)throw new Error("Invalid input: email must be a string");if(!e)return i.code("EMPTY_STRING");const n=!o.nonAsciiRx.test(e);if(!n){if(!1===t.allowUnicode)return i.code("FORBIDDEN_UNICODE");e=e.normalize("NFC")}const r=e.split("@");if(2!==r.length)return r.length>2?i.code("MULTIPLE_AT_CHAR"):i.code("MISSING_AT_CHAR");const[a,c]=r;if(!a)return i.code("EMPTY_LOCAL");if(!t.ignoreLength){if(e.length>254)return i.code("ADDRESS_TOO_LONG");if(o.encoder.encode(a).length>64)return i.code("LOCAL_TOO_LONG")}return o.local(a,n)||s.analyze(c,t)},o.local=function(e,t){const n=e.split(".");for(const e of n){if(!e.length)return i.code("EMPTY_LOCAL_SEGMENT");if(t){if(!o.atextRx.test(e))return i.code("INVALID_LOCAL_CHARS")}else for(const t of e){if(o.atextRx.test(t))continue;const e=o.binary(t);if(!o.atomRx.test(e))return i.code("INVALID_LOCAL_CHARS")}}},o.binary=function(e){return Array.from(o.encoder.encode(e)).map((e=>String.fromCharCode(e))).join("")},o.atextRx=/^[\w!#\$%&'\*\+\-/=\?\^`\{\|\}~]+$/,o.atomRx=new RegExp(["(?:[\\xc2-\\xdf][\\x80-\\xbf])","(?:\\xe0[\\xa0-\\xbf][\\x80-\\xbf])|(?:[\\xe1-\\xec][\\x80-\\xbf]{2})|(?:\\xed[\\x80-\\x9f][\\x80-\\xbf])|(?:[\\xee-\\xef][\\x80-\\xbf]{2})","(?:\\xf0[\\x90-\\xbf][\\x80-\\xbf]{2})|(?:[\\xf1-\\xf3][\\x80-\\xbf]{3})|(?:\\xf4[\\x80-\\x8f][\\x80-\\xbf]{2})"].join("|"))},2178:(e,t)=>{t.codes={EMPTY_STRING:"Address must be a non-empty string",FORBIDDEN_UNICODE:"Address contains forbidden Unicode characters",MULTIPLE_AT_CHAR:"Address cannot contain more than one @ character",MISSING_AT_CHAR:"Address must contain one @ character",EMPTY_LOCAL:"Address local part cannot be empty",ADDRESS_TOO_LONG:"Address too long",LOCAL_TOO_LONG:"Address local part too long",EMPTY_LOCAL_SEGMENT:"Address local part contains empty dot-separated segment",INVALID_LOCAL_CHARS:"Address local part contains invalid character",DOMAIN_NON_EMPTY_STRING:"Domain must be a non-empty string",DOMAIN_TOO_LONG:"Domain too long",DOMAIN_INVALID_UNICODE_CHARS:"Domain contains forbidden Unicode characters",DOMAIN_INVALID_CHARS:"Domain contains invalid character",DOMAIN_INVALID_TLDS_CHARS:"Domain contains invalid tld character",DOMAIN_SEGMENTS_COUNT:"Domain lacks the minimum required number of segments",DOMAIN_SEGMENTS_COUNT_MAX:"Domain contains too many segments",DOMAIN_FORBIDDEN_TLDS:"Domain uses forbidden TLD",DOMAIN_EMPTY_SEGMENT:"Domain contains empty dot-separated segment",DOMAIN_LONG_SEGMENT:"Domain contains dot-separated segment that is too long"},t.code=function(e){return{code:e,error:t.codes[e]}}},9959:(e,t,n)=>{const r=n(375),s=n(5752);t.regex=function(e={}){r(void 0===e.cidr||"string"==typeof e.cidr,"options.cidr must be a string");const t=e.cidr?e.cidr.toLowerCase():"optional";r(["required","optional","forbidden"].includes(t),"options.cidr must be one of required, optional, forbidden"),r(void 0===e.version||"string"==typeof e.version||Array.isArray(e.version),"options.version must be a string or an array of string");let n=e.version||["ipv4","ipv6","ipvfuture"];Array.isArray(n)||(n=[n]),r(n.length>=1,"options.version must have at least 1 version specified");for(let e=0;e<n.length;++e)r("string"==typeof n[e],"options.version must only contain strings"),n[e]=n[e].toLowerCase(),r(["ipv4","ipv6","ipvfuture"].includes(n[e]),"options.version contains unknown version "+n[e]+" - must be one of ipv4, ipv6, ipvfuture");n=Array.from(new Set(n));const i=`(?:${n.map((e=>{if("forbidden"===t)return s.ip[e];const n=`\\/${"ipv4"===e?s.ip.v4Cidr:s.ip.v6Cidr}`;return"required"===t?`${s.ip[e]}${n}`:`${s.ip[e]}(?:${n})?`})).join("|")})`,o=new RegExp(`^${i}$`);return{cidr:t,versions:n,regex:o,raw:i}}},5752:(e,t,n)=>{const r=n(375),s=n(6064),i={generate:function(){const e={},t="\\dA-Fa-f",n="["+t+"]",r="\\w-\\.~",s="!\\$&'\\(\\)\\*\\+,;=",i="%"+t,o=r+i+s+":@",a="["+o+"]",c="(?:0{0,2}\\d|0?[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])";e.ipv4address="(?:"+c+"\\.){3}"+c;const l=n+"{1,4}",u="(?:"+l+":"+l+"|"+e.ipv4address+")",d="(?:"+l+":){6}"+u,f="::(?:"+l+":){5}"+u,h="(?:"+l+")?::(?:"+l+":){4}"+u,b="(?:(?:"+l+":){0,1}"+l+")?::(?:"+l+":){3}"+u,p="(?:(?:"+l+":){0,2}"+l+")?::(?:"+l+":){2}"+u,m="(?:(?:"+l+":){0,3}"+l+")?::"+l+":"+u,y="(?:(?:"+l+":){0,4}"+l+")?::"+u,v="(?:(?:"+l+":){0,5}"+l+")?::"+l,g="(?:(?:"+l+":){0,6}"+l+")?::";e.ipv4Cidr="(?:\\d|[1-2]\\d|3[0-2])",e.ipv6Cidr="(?:0{0,2}\\d|0?[1-9]\\d|1[01]\\d|12[0-8])",e.ipv6address="(?:"+d+"|"+f+"|"+h+"|"+b+"|"+p+"|"+m+"|"+y+"|"+v+"|"+g+")",e.ipvFuture="v"+n+"+\\.["+r+s+":]+",e.scheme="[a-zA-Z][a-zA-Z\\d+-\\.]*",e.schemeRegex=new RegExp(e.scheme);const w="["+r+i+s+":]*",k="["+r+i+s+"]{1,255}",x="(?:\\[(?:"+e.ipv6address+"|"+e.ipvFuture+")\\]|"+e.ipv4address+"|"+k+")",_="(?:"+w+"@)?"+x+"(?::\\d*)?",j="(?:"+w+"@)?("+x+")(?::\\d*)?",S=a+"*",$=a+"+",C="(?:\\/"+S+")*",A="\\/(?:"+$+C+")?",O=$+C,E="["+r+i+s+"@]+"+C,M="(?:\\/\\/\\/"+S+C+")";return e.hierPart="(?:(?:\\/\\/"+_+C+")|"+A+"|"+O+"|"+M+")",e.hierPartCapture="(?:(?:\\/\\/"+j+C+")|"+A+"|"+O+")",e.relativeRef="(?:(?:\\/\\/"+_+C+")|"+A+"|"+E+"|)",e.relativeRefCapture="(?:(?:\\/\\/"+j+C+")|"+A+"|"+E+"|)",e.query="["+o+"\\/\\?]*(?=#|$)",e.queryWithSquareBrackets="["+o+"\\[\\]\\/\\?]*(?=#|$)",e.fragment="["+o+"\\/\\?]*",e}};i.rfc3986=i.generate(),t.ip={v4Cidr:i.rfc3986.ipv4Cidr,v6Cidr:i.rfc3986.ipv6Cidr,ipv4:i.rfc3986.ipv4address,ipv6:i.rfc3986.ipv6address,ipvfuture:i.rfc3986.ipvFuture},i.createRegex=function(e){const t=i.rfc3986,n="(?:\\?"+(e.allowQuerySquareBrackets?t.queryWithSquareBrackets:t.query)+")?(?:#"+t.fragment+")?",o=e.domain?t.relativeRefCapture:t.relativeRef;if(e.relativeOnly)return i.wrap(o+n);let a="";if(e.scheme){r(e.scheme instanceof RegExp||"string"==typeof e.scheme||Array.isArray(e.scheme),"scheme must be a RegExp, String, or Array");const n=[].concat(e.scheme);r(n.length>=1,"scheme must have at least 1 scheme specified");const i=[];for(let e=0;e<n.length;++e){const o=n[e];r(o instanceof RegExp||"string"==typeof o,"scheme at position "+e+" must be a RegExp or String"),o instanceof RegExp?i.push(o.source.toString()):(r(t.schemeRegex.test(o),"scheme at position "+e+" must be a valid scheme"),i.push(s(o)))}a=i.join("|")}const c="(?:"+(a?"(?:"+a+")":t.scheme)+":"+(e.domain?t.hierPartCapture:t.hierPart)+")",l=e.allowRelative?"(?:"+c+"|"+o+")":c;return i.wrap(l+n,a)},i.wrap=function(e,t){return{raw:e=`(?=.)(?!https?:/(?:$|[^/]))(?!https?:///)(?!https?:[^/])${e}`,regex:new RegExp(`^${e}$`),scheme:t}},i.uriRegex=i.createRegex({}),t.regex=function(e={}){return e.scheme||e.allowRelative||e.relativeOnly||e.allowQuerySquareBrackets||e.domain?i.createRegex(e):i.uriRegex}},1447:(e,t)=>{const n={operators:["!","^","*","/","%","+","-","<","<=",">",">=","==","!=","&&","||","??"],operatorCharacters:["!","^","*","/","%","+","-","<","=",">","&","|","?"],operatorsOrder:[["^"],["*","/","%"],["+","-"],["<","<=",">",">="],["==","!="],["&&"],["||","??"]],operatorsPrefix:["!","n"],literals:{'"':'"',"`":"`","'":"'","[":"]"},numberRx:/^(?:[0-9]*(\.[0-9]*)?){1}$/,tokenRx:/^[\w\$\#\.\@\:\{\}]+$/,symbol:Symbol("formula"),settings:Symbol("settings")};t.Parser=class{constructor(e,t={}){if(!t[n.settings]&&t.constants)for(const e in t.constants){const n=t.constants[e];if(null!==n&&!["boolean","number","string"].includes(typeof n))throw new Error(`Formula constant ${e} contains invalid ${typeof n} value type`)}this.settings=t[n.settings]?t:Object.assign({[n.settings]:!0,constants:{},functions:{}},t),this.single=null,this._parts=null,this._parse(e)}_parse(e){let r=[],s="",i=0,o=!1;const a=e=>{if(i)throw new Error("Formula missing closing parenthesis");const a=r.length?r[r.length-1]:null;if(o||s||e){if(a&&"reference"===a.type&&")"===e)return a.type="function",a.value=this._subFormula(s,a.value),void(s="");if(")"===e){const e=new t.Parser(s,this.settings);r.push({type:"segment",value:e})}else if(o){if("]"===o)return r.push({type:"reference",value:s}),void(s="");r.push({type:"literal",value:s})}else if(n.operatorCharacters.includes(s))a&&"operator"===a.type&&n.operators.includes(a.value+s)?a.value+=s:r.push({type:"operator",value:s});else if(s.match(n.numberRx))r.push({type:"constant",value:parseFloat(s)});else if(void 0!==this.settings.constants[s])r.push({type:"constant",value:this.settings.constants[s]});else{if(!s.match(n.tokenRx))throw new Error(`Formula contains invalid token: ${s}`);r.push({type:"reference",value:s})}s=""}};for(const t of e)o?t===o?(a(),o=!1):s+=t:i?"("===t?(s+=t,++i):")"===t?(--i,i?s+=t:a(t)):s+=t:t in n.literals?o=n.literals[t]:"("===t?(a(),++i):n.operatorCharacters.includes(t)?(a(),s=t,a()):" "!==t?s+=t:a();a(),r=r.map(((e,t)=>"operator"!==e.type||"-"!==e.value||t&&"operator"!==r[t-1].type?e:{type:"operator",value:"n"}));let c=!1;for(const e of r){if("operator"===e.type){if(n.operatorsPrefix.includes(e.value))continue;if(!c)throw new Error("Formula contains an operator in invalid position");if(!n.operators.includes(e.value))throw new Error(`Formula contains an unknown operator ${e.value}`)}else if(c)throw new Error("Formula missing expected operator");c=!c}if(!c)throw new Error("Formula contains invalid trailing operator");1===r.length&&["reference","literal","constant"].includes(r[0].type)&&(this.single={type:"reference"===r[0].type?"reference":"value",value:r[0].value}),this._parts=r.map((e=>{if("operator"===e.type)return n.operatorsPrefix.includes(e.value)?e:e.value;if("reference"!==e.type)return e.value;if(this.settings.tokenRx&&!this.settings.tokenRx.test(e.value))throw new Error(`Formula contains invalid reference ${e.value}`);return this.settings.reference?this.settings.reference(e.value):n.reference(e.value)}))}_subFormula(e,r){const s=this.settings.functions[r];if("function"!=typeof s)throw new Error(`Formula contains unknown function ${r}`);let i=[];if(e){let t="",s=0,o=!1;const a=()=>{if(!t)throw new Error(`Formula contains function ${r} with invalid arguments ${e}`);i.push(t),t=""};for(let r=0;r<e.length;++r){const i=e[r];o?(t+=i,i===o&&(o=!1)):i in n.literals&&!s?(t+=i,o=n.literals[i]):","!==i||s?(t+=i,"("===i?++s:")"===i&&--s):a()}a()}return i=i.map((e=>new t.Parser(e,this.settings))),function(e){const t=[];for(const n of i)t.push(n.evaluate(e));return s.call(e,...t)}}evaluate(e){const t=this._parts.slice();for(let r=t.length-2;r>=0;--r){const s=t[r];if(s&&"operator"===s.type){const i=t[r+1];t.splice(r+1,1);const o=n.evaluate(i,e);t[r]=n.single(s.value,o)}}return n.operatorsOrder.forEach((r=>{for(let s=1;s<t.length-1;)if(r.includes(t[s])){const r=t[s],i=n.evaluate(t[s-1],e),o=n.evaluate(t[s+1],e);t.splice(s,2);const a=n.calculate(r,i,o);t[s-1]=0===a?0:a}else s+=2})),n.evaluate(t[0],e)}},t.Parser.prototype[n.symbol]=!0,n.reference=function(e){return function(t){return t&&void 0!==t[e]?t[e]:null}},n.evaluate=function(e,t){return null===e?null:"function"==typeof e?e(t):e[n.symbol]?e.evaluate(t):e},n.single=function(e,t){if("!"===e)return!t;const n=-t;return 0===n?0:n},n.calculate=function(e,t,r){if("??"===e)return n.exists(t)?t:r;if("string"==typeof t||"string"==typeof r){if("+"===e)return(t=n.exists(t)?t:"")+(n.exists(r)?r:"")}else switch(e){case"^":return Math.pow(t,r);case"*":return t*r;case"/":return t/r;case"%":return t%r;case"+":return t+r;case"-":return t-r}switch(e){case"<":return t<r;case"<=":return t<=r;case">":return t>r;case">=":return t>=r;case"==":return t===r;case"!=":return t!==r;case"&&":return t&&r;case"||":return t||r}return null},n.exists=function(e){return null!=e}},9926:()=>{},5688:()=>{},9708:()=>{},1152:()=>{},443:()=>{},9848:()=>{},5934:e=>{e.exports=JSON.parse('{"version":"17.13.3"}')}},t={},function n(r){var s=t[r];if(void 0!==s)return s.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}(5107);var e,t}))})(x);const _=x.exports;const j=_.object({email:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required(),emailVerificationCode:_.string().trim().length(4).required()});const S=e=>{let{error:t}=j.validate(e);if(t){return{isValid:false,validationMessage:t.details[0].message}}else{return{isValid:true,validationMessage:""}}};const $=e=>{v.accountName=e.name;v.accountEmail=e.email;v.isEmailVerified=e.isEmailVerified;v.isSessionActive=e.isSessionActive};const C=async()=>{let e=`${g.api.url}${g.api.endpoint.account.details}`;let t={method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"};let n;await fetch(e,t).then((e=>e.json())).then((e=>{n=e})).catch((e=>{console.log(e)}));return{success:n.success,message:n.message,payload:n.payload}};const A=async()=>{let e=`${g.api.url}${g.api.endpoint.account.auth.logout}`;let t={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include"};let n;await fetch(e,t).then((e=>e.json())).then((e=>{n=e})).catch((e=>{console.log(e)}));return{success:n.success,message:n.message,payload:n.payload}};const O=async e=>{let t="";if(e.variant==="emailVerificationCode"){t=`${g.api.url}${g.api.endpoint.mail.code.emailVerification}`}else if(e.variant==="passwordResetCode"){t=`${g.api.url}${g.api.endpoint.mail.code.passwordReset}`}let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const E=async e=>{let t="";if(e.entity==="account"){t=`${g.api.url}${g.api.endpoint.account.details}`}let n={method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const M=(e,t)=>{let n={email:e.trim().toLowerCase(),variant:t};return n};const D=(e,t,n)=>{let r={entity:e,attribute:t,value:n};return r};const I=_.object({email:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required(),variant:_.string().trim().required()});const L=e=>{let{error:t}=I.validate(e);if(t){return{isValid:false,validationMessage:`❌ ${t.details[0].message}`}}else{return{isValid:true,validationMessage:""}}};const R=_.object({entity:_.string().trim().required(),attribute:_.string().valid("name","email","password").required(),value:_.when("attribute",{switch:[{is:"name",then:_.string().required()},{is:"email",then:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required()},{is:"password",then:_.string().min(8).max(1024).required()}]})});const N=e=>{let{error:t}=R.validate(e);if(t){return{isValid:false,validationMessage:`❌ ${t.details[0].message}`}}else{return{isValid:true,validationMessage:""}}};const P=".hide-on-mobile{display:block}.show-on-mobile{display:none}#init-loader{width:100vw;height:100vh;background:var(--color__bg);position:fixed;top:0;left:0;z-index:9;display:flex;justify-content:space-around;align-items:center}@media only screen and (max-width: 768px){.hide-on-mobile{display:none}.show-on-mobile{display:block}}";const T=P;const z=class{constructor(e){n(this,e);this.emailVerificationCode="";this.EmailVerificationBanner=()=>r("c-banner",{position:"bottom",theme:"danger"},r("div",{class:"hide-on-mobile"},r("l-row",{justifyContent:"space-between"},r("l-row",null,r("e-text",null,"We have sent an email verification code to"," ",r("u",null,r("strong",null,v.accountEmail))),r("l-spacer",{variant:"horizontal",value:.25}),r("e-input",{type:"text",name:"emailVerificationCode",placeholder:"Enter verification code"})),this.isMailingEmailVerificationCode?r("e-spinner",{theme:"blue"}):r("e-button",{variant:"link",action:"mailEmailVerificationCode"},"Re-send code"))),r("div",{class:"show-on-mobile"},r("e-text",null,"We have sent an email verification code to"," ",r("strong",null,r("u",null,v.accountEmail))),r("l-spacer",{value:1}),r("l-row",null,r("e-input",{type:"text",name:"emailVerificationCode",placeholder:"Enter verification code"}),this.isMailingEmailVerificationCode?r("e-spinner",{theme:"blue"}):r("e-button",{variant:"link",action:"mailEmailVerificationCode"},"Re-send code"))));this.isMailingEmailVerificationCode=false;this.isSessionChecked=false;this.isVerifyingEmail=false}handleAuthSuccessfulEvent(){this.initSession()}async handleButtonClickEvent(e){if(e.detail.action==="mailEmailVerificationCode"){this.mailEmailVerificationCode()}else if(e.detail.action==="logout"){this.logout()}else if(e.detail.action==="proceedToLogin"){o.push("/login")}else if(e.detail.action==="routeTo"){o.push(e.detail.value)}}handleInputEvent(e){if(e.detail.name==="emailVerificationCode"){this.emailVerificationCode=e.detail.value;if(this.emailVerificationCode.length===4){this.verifyEmail()}}}handleLogout(){this.logout()}handleRouteToEvent(e){o.push(e.detail.route)}handleAuthSuccessEvent(e){$(e.detail.payload)}componentDidLoad(){this.initSession()}async initSession(){let{success:e,payload:t}=await C();if(e){$(t)}this.isSessionChecked=true}async logout(){let{success:e,message:t,payload:n}=await A();if(!e){return alert(t)}v.isSessionActive=n.isSessionActive;v.accountName="";v.accountEmail="";v.isEmailVerified=true;o.push("/")}async mailEmailVerificationCode(){let e=M(v.accountEmail,"emailVerificationCode");let{isValid:t,validationMessage:n}=L(e);if(!t){return alert(n)}this.isMailingEmailVerificationCode=true;let{message:r}=await O(e);this.isMailingEmailVerificationCode=false;alert(r)}async verifyEmail(){let e=k(v.accountEmail,this.emailVerificationCode);let{isValid:t,validationMessage:n}=S(e);if(!t){return alert(n)}this.isVerifyingEmail=true;let{success:r,message:s}=await w(e);alert(s);if(!r){return}v.isEmailVerified=true}render(){return r(s,{key:"7d482279de02ba0edf5d303e6c1a5ebdf0dc0f93"},v.isSessionActive&&r("p-topbar",{key:"09403378daed6f0d1d93bf396b6bad1efab1d6d4"}),!this.isSessionChecked&&r("div",{key:"1f2325266695b4f254266d938be5e6f5408763c6",id:"init-loader"},r("e-spinner",{key:"b1bebbab74b3229adb465b7036ef5f54f9731b22",theme:"dark"})),r(o.Switch,{key:"f57bf9d8b9a25e0e11e88bb4775b717a807ce19d"},v.isSessionActive?[r(a,{path:/^\/$/,to:"/surveys"}),r(a,{path:/^\/login/,to:"/surveys"}),r(a,{path:/^\/signup/,to:"/surveys"}),r(a,{path:/^\/post-oauth$/,to:"/surveys"}),r(a,{path:/^\/password-reset/,to:"/surveys"})]:[r(a,{path:/^\/$/,to:"/login"}),r(a,{path:/^\/surveys/,to:"/login"}),r(a,{path:/^\/billing/,to:"/login"}),r(a,{path:/^\/account/,to:"/login"}),r(a,{path:/^\/delete-account/,to:"/login"}),r(a,{path:/^\/checkout$/,to:"/login"}),r(a,{path:/^\/payment-success$/,to:"/login"}),r(a,{path:/^\/payment-failed/,to:"/login"})],r(a,{key:"d3ef9b41370c8f34e77280b5dac2d07d5ab2acca",path:"/login",render:()=>r("v-login",null)}),r(a,{key:"4722eee10714e954e3467be91fbf422e62307080",path:"/signup",render:()=>r("v-signup",null)}),r(a,{key:"1ca4081a748d96bf9038dc3476b5cc610b0fbcfb",path:c("/post-oauth/:service"),render:({service:e})=>r("v-post-oauth",{service:e})}),r(a,{key:"903c20435a001a9dd0d524e4cfc52e1913f71a05",path:"/password-reset",render:()=>r("v-password-reset",null)}),r(a,{key:"f142c3ef5b57f19621f3aedd35e6507bcb054168",path:"/surveys",render:()=>r("v-surveys",null)}),r(a,{key:"24a9661af6cc39b4f26a27ad55e5a26d095fc625",path:"/websites",render:()=>r("v-websites",null)}),r(a,{key:"612e6317ae91dca1b53262cda17d27c7fcae5288",path:"/billing",render:()=>r("v-billing",null)}),r(a,{key:"6ce2ba9e03156cc72542572c5634cd9cb7cb58e4",path:"/account",render:()=>r("v-account",null)}),r(a,{key:"8ef707edadb090f20bc47c2c35168b6527f87308",path:"/delete-account",render:()=>r("v-delete-account",null)}),r(a,{key:"0b4e5470275cfc21f81ffb8168d7407014ffdf84",path:"/payment-failed",render:()=>r("v-payment-failed",null)}),r(a,{key:"144a316bd48203b3ccec04b332ac59fe71d786c2",path:c("/checkout/:orderId"),render:({orderId:e})=>r("v-checkout",{orderId:e})})," ",r(a,{key:"1352d20a4d695668a964f8687b12ee134c244cc1",path:c("/payment-success/:sessionId"),render:({sessionId:e})=>r("v-payment-success",{sessionId:e})})," ",r(a,{key:"acd8bf08d02541d1cca74bf1be91f700625e4095",path:/.*/,render:()=>r("v-catch-all",null)})),v.isSessionActive&&!v.isEmailVerified&&r(this.EmailVerificationBanner,{key:"cbee86bfccc7c256e322e8f7085d34c9cb0b7a8d"}))}};z.style=T;const F=":host{display:block}:host(.banner){display:block;padding:var(--padding);border-radius:var(--border-radius);box-sizing:border-box}:host(.banner__position--bottom){width:100%;left:0;bottom:0;position:fixed;-webkit-box-shadow:0px -2px 33px 3px rgba(0, 0, 0, 0.15);-moz-box-shadow:0px -2px 33px 3px rgba(0, 0, 0, 0.15);box-shadow:0px -2px 33px 3px rgba(0, 0, 0, 0.15)}:host(.banner__theme--danger){background:var(--color__red--50);color:var(--color__red--900);border:1px solid var(--color__red--100)}:host(.banner__theme--warning){background:var(--color__orange--50);color:var(--color__orange--900);border:1px solid var(--color__orange--200)}:host(.banner__theme--success){background:var(--color__green--50);color:var(--color__green--800);border:1px solid var(--color__green--200)}";const W=F;const U=class{constructor(e){n(this,e);this.classList="banner";this.theme="default";this.position="inline"}componentWillLoad(){this.generateClassList()}generateClassList(){this.classList=`banner banner__theme--${this.theme} banner__position--${this.position}`}render(){return r(s,{key:"e4d1681a09903efea0987cffd8c6b33f2db34256",class:this.classList},r("slot",{key:"5d262bcec051b3a9b807b5491bffa38ea908fa3e"}))}};U.style=W;const q=".card-container{display:block;border:var(--border);border-radius:var(--border-radius);padding:calc(var(--padding) * 1.5);background:white}.card-container--interactive{transition:all 0.25s}.card-container--interactive:hover{cursor:pointer;border:1px solid var(--color__gray--800)}";const V=q;const B=class{constructor(e){n(this,e);this.clickable=false}render(){return r("div",{key:"64ff95f34a79745336b377c95cd804c2bd3473fd",class:`card-container ${this.clickable&&"card-container--interactive"}`},r("slot",{key:"d1821898bb84a9f65e331b93fe37768fd4cd3b64"}))}};B.style=V;const G=":host{display:flex;justify-content:space-around;margin:0 auto;overflow-y:auto;box-sizing:border-box;padding-top:125px}@media only screen and (max-width: 768px){:host{width:100%;display:block;position:static;margin:0 auto;padding:2em 2em 12em 2em}}";const H=G;const Z=class{constructor(e){n(this,e);this.classList="";this.variant="default"}componentWillLoad(){this.generateClassList()}generateClassList(){this.classList=this.variant}render(){return r(s,{key:"f16c911f2ad861f9824fe20047302ef8098c213c",class:this.classList},r("slot",{key:"16ed970297475f11f7a9f994549844fec3a01aef"}))}};Z.style=H;const Y="button{display:flex;justify-content:space-around;align-items:center;background:none;border:0;font-size:1em;border-radius:var(--border-radius);transition:all 0.25s;padding:calc(var(--padding) / 1.5) calc(var(--padding) / 1.5);height:50px}button:hover{cursor:pointer}button:disabled{opacity:0.3;pointer-events:none}.button__primary--default{font-weight:600;border:1px solid var(--color__indigo--500);background:var(--color__indigo--500);color:var(--color__bg)}.button__primary--default:hover{background:var(--color__indigo--700)}.button__primary--default:active{background:var(--color__indigo--400)}.button__primary--danger{background:var(--color__red--700);color:var(--color__bg)}.button__primary--danger:hover{background:var(--color__red--900)}.button__primary--danger:active{background:var(--color__red--light)}.button__ghost--default{border:1px solid var(--color__indigo--500);color:var(--color__indigo--500)}.button__ghost--default:hover{background:var(--color__indigo--50)}.button__ghost--default:active{color:var(--color__indigo--200)}.button__link--default{padding:0;border:0;color:var(--color__indigo--500);height:auto}.button__link--default:hover{color:var(--color__indigo--700)}.button__link--default:active{color:var(--color__indigo--400)}.button__light--default{color:var(--color__indigo--500)}.button__light--default:hover{background:var(--color__grey--50);color:var(--color__indigo--700)}.button__light--default:active{color:var(--color__indigo--200)}.button__size--wide{display:block;width:100%}.button__size--small{padding:calc(var(--padding) / 2) calc(var(--padding) / 1.5)}.button__status--active{padding:calc(var(--padding) / 2) calc(var(--padding) / 1.5)}";const J=Y;const Q=class{constructor(e){n(this,e);this.buttonClickEventEmitter=i(this,"buttonClickEvent",7);this.action=undefined;this.value=undefined;this.variant="primary";this.size="default";this.disabled=false;this.active=false;this.theme="default";this.isActive=false;this.classList=""}activePropWatcher(e,t){if(e!=t){this.isActive=e;this.generateClassList()}}handleButtonClick(e){e.preventDefault();this.buttonClickEventEmitter.emit({action:this.action,value:this.value})}componentWillLoad(){this.isActive=this.active;this.generateClassList()}generateClassList(){this.classList=`button__${this.variant}--${this.theme} button__size--${this.size}`;if(this.isActive){this.classList=`${this.classList} button__status--active`}}render(){return r("button",{key:"f49b65620d11a9cef39bef4e54bd14780666420f",class:this.classList,onClick:e=>this.handleButtonClick(e),disabled:this.disabled||this.isActive,ref:e=>this.buttonEl=e},this.isActive?r("e-spinner",null):r("slot",null))}static get watchers(){return{active:["activePropWatcher"]}}};Q.style=J;const K=":host{display:block}#button__oauth--google{transform:scale(1.28);width:200px;margin-left:28px}";const X=K;const ee=class{constructor(e){n(this,e);this.routeToEvent=i(this,"routeToEvent",7);this.GoogleOauthButton=()=>r("div",{id:"button__oauth--google",ref:e=>this.googleOauthButtonEl=e});this.variant="google"}componentDidLoad(){this.window=window;if(this.variant==="google"){this.initGoogleOauth()}}loadScript(e){return new Promise((t=>{const n=document.createElement("script");n.src=e;n.onload=()=>{t(true)};n.onerror=()=>{t(false)};document.body.appendChild(n)}))}async initGoogleOauth(){const e=await this.loadScript("https://accounts.google.com/gsi/client");if(!e){console.log("Failed to load GSI client");return}await this.window.google.accounts.id.initialize({client_id:g.keys.oauth.google.clientId,callback:e=>{v.oauthToken=e.credential;this.routeToEvent.emit({route:"/post-oauth/google"})}});await this.window.google.accounts.id.renderButton(this.googleOauthButtonEl,{type:"standard",theme:"outline",size:"large",width:250})}render(){if(this.variant==="google"){return r(this.GoogleOauthButton,{key:"eed8ea2c9020ebfeef555c238bf2c7e925de3488"})}}};ee.style=X;const te=":host{display:contents}img{display:inline-block}";const ne=te;const re=class{constructor(e){n(this,e);this.classMap={};this.variant="default";this.src="";this.width="100%"}componentWillLoad(){this.generateClassMap()}generateClassMap(){this.classMap.width=this.width}render(){return r(s,{key:"a7f1d6e2185e6494c1a6bae3299c56e293606304"},r("img",{key:"d317b920b2a1da949e04e715b4d335d0e7e5c3f8",style:this.classMap,src:this.src}))}};re.style=ne;const se='input[type="email"],input[type="number"],input[type="password"],input[type="text"]{width:100%;padding:calc(var(--padding) / 1.5) var(--padding);border:var(--border__input);border-radius:var(--border-radius);font-size:1em;box-sizing:border-box;height:50px}input[type="email"]:focus,input[type="number"]:focus,input[type="password"]:focus,input[type="text"]:focus{outline:none;border-color:var(--color__grey--800)}.radio-label{display:flex;align-items:center;gap:0.5em;padding:calc(var(--padding) * 0.5) var(--padding);border:var(--border__input);border-radius:var(--border-radius);cursor:pointer;transition:all 0.3s ease-in-out}.radio-label:hover{cursor:pointer;color:var(--color__grey--800);border:1px solid var(--color__grey--800)}.radio-input{display:none}.radio-input:checked+.radio-label{color:var(--color__grey--800);border:1px solid var(--color__grey--800);background:var(--color__grey--100)}::-webkit-input-placeholder{color:var(--color__grey--400)}:-moz-placeholder{color:var(--color__grey--400);opacity:1}::-moz-placeholder{color:var(--color__grey--400);opacity:1}:-ms-input-placeholder{color:var(--color__grey--400)}::-ms-input-placeholder{color:var(--color__grey--400)}::placeholder{color:var(--color__grey--400)}.checkbox-container{display:flex;gap:1em;font-family:sans-serif}.input-label__container{position:relative}.checkbox-input{position:absolute;opacity:0;cursor:pointer}.checkbox-label{display:flex;align-items:center;padding:0.5rem 1rem;padding-left:2.75rem;border:1px solid var(--color__grey--400);border-radius:var(--border-radius);cursor:pointer;transition:all 0.25s;position:relative}.checkbox-label:hover{color:var(--color__grey--800);border-color:var(--color__grey--800)}.checkbox-label:before{content:"";position:absolute;left:1rem;top:50%;transform:translateY(-50%);width:0.8em;height:0.8em;border:1px solid var(--color__grey--400);border-radius:2px;transition:all 0.25s}.checkbox-label:hover:before{border-color:var(--color__grey--800)}.checkbox-input:checked+.checkbox-label{border-color:var(--color__grey--800);background-color:var(--color__grey--100);color:var(--color__grey--800)}.checkbox-input:checked+.checkbox-label:before{background-color:var(--color__grey--800);border-color:var(--color__grey--800)}.checkbox-input:checked+.checkbox-label:after{content:"";position:absolute;left:calc(1rem + 0.27em);top:50%;transform:translateY(-50%) rotate(45deg);width:0.25em;height:0.4em;border-right:2px solid white;border-bottom:2px solid white;margin-top:-0.1em;}';const ie=se;const oe=class{constructor(e){n(this,e);this.inputEvent=i(this,"inputEvent",7);this.label=undefined;this.type=undefined;this.name=undefined;this.placeholder="Your text";this.value=undefined;this.checked=false;this.isRadioChecked=false}componentWillLoad(){if(this.name==="surveyEmbed"){this.isRadioChecked=this.checked}}checkedPropWatcher(e,t){if(e!=t){this.isRadioChecked=e}}handleInputEvent(e){if(this.type==="email"||this.type==="number"||this.type==="password"||this.type==="text"){this.inputEvent.emit({name:this.name,value:e.target.value.trim()})}else if(this.type==="radio"||this.type==="checkbox"){this.inputEvent.emit({name:this.name,value:e.target.value.trim(),isChecked:e.target.checked})}}render(){if(this.type==="email"||this.type==="number"||this.type==="password"||this.type==="text"){return r("input",{type:this.type,placeholder:this.placeholder,onInput:e=>this.handleInputEvent(e),value:this.value})}else if(this.type==="radio"){return r("div",{class:"input-label__container"},r("input",{id:`${this.name}-${this.value}`,type:this.type,class:`${this.type}-input`,name:this.name,value:this.value,checked:this.isRadioChecked,onInput:e=>this.handleInputEvent(e)}),r("label",{class:`${this.type}-label`,htmlFor:`${this.name}-${this.value}`},r("slot",null)))}else if(this.type==="checkbox"){return r("div",{class:"input-label__container"},r("input",{id:`${this.name}-${this.value}`,type:this.type,class:`${this.type}-input`,name:this.name,value:this.value,onInput:e=>this.handleInputEvent(e)}),r("label",{class:`${this.type}-label`,htmlFor:`${this.name}-${this.value}`},r("slot",null)))}}static get watchers(){return{checked:["checkedPropWatcher"]}}};oe.style=ie;const ae="a{text-decoration:none;color:var(--color__indigo--500);transition:all 0.25s}a:hover{color:var(--color__indigo--700);cursor:pointer}a:active{color:var(--color__indigo--400)}.link__nav--default{display:block;box-sizing:border-box;padding:0.25em 1em}.link__nav--default-active{display:block;box-sizing:border-box;color:var(--color__indigo--700);background:var(--color__indigo--50);padding:0.25em 1em;border-radius:var(--border-radius)}.link__card--default{width:100%;border-radius:var(--border-radius);padding:calc(var(--padding) / 2) var(--padding);border:var(--border)}.link__card--default a:hover{background:var(--color__indigo--50)}.link__tab--default{margin-right:3em}.link__tab--default-active{margin-right:3em;border-bottom:2px solid var(--color__indigo--600)}.link__default--danger{color:var(--color__red--700)}.link__default--danger:hover{color:var(--color__red--900)}.link__default--danger:active{color:var(--color__red--light)}";const ce=ae;const le=class{constructor(e){n(this,e);this.classList="";this.variant="default";this.theme="default";this.url="";this.active=false;this.isActive=false}activePropWatcher(e,t){if(e!=t){this.generateClassList()}}componentWillLoad(){this.generateClassList()}generateClassList(){if(this.active){this.classList=`link__${this.variant}--${this.theme}-active`}else{this.classList=`link__${this.variant}--${this.theme}`}}render(){if(this.variant==="email"){return r("a",{class:this.classList,href:this.url,target:"_self"},r("slot",null)," ")}else if(this.variant==="externalLink"){return r("l-row",{justifyContent:"flex-start"},r("a",{class:this.classList,href:this.url,target:"_blank"},r("slot",null)," ")," ",r("e-image",{src:"../../../assets/icon/light/open-in-new.svg",width:"1em"}))}else{return r("a",{class:this.classList,onClick:()=>o.push(this.url)},r("slot",null))}}static get watchers(){return{active:["activePropWatcher"]}}};le.style=ce;const ue="ul{margin:0;padding:0;list-style-type:none}";const de=ue;const fe=class{constructor(e){n(this,e)}render(){return r("ul",{key:"43f063adfd8dd261bf4efc4ad3bb41f0cfb7c3fb"},r("slot",{key:"8acd985eccf6db4eb1c6aec2ad5cf8ebd96229bd"}))}};fe.style=de;const he="li{margin:0}";const be=he;const pe=class{constructor(e){n(this,e)}render(){return r("li",{key:"2c6d0cec36b828158775faa26abaa82cb5af7e26"},r("slot",{key:"66f267d482a692bfc7ba19dc80e60bfe37440490"}))}};pe.style=be;const me=".spinner{display:inline-block;position:relative;width:20px;height:20px;padding-right:0.1em}.spinner div{display:block;position:absolute;width:14px;height:14px;margin:2px;border:2px solid #fff;border-radius:50%;animation:spinner-animation 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;border-color:#fff transparent transparent transparent}.spinner div:nth-child(1){animation-delay:-0.45s}.spinner div:nth-child(2){animation-delay:-0.3s}.spinner div:nth-child(3){animation-delay:-0.15s}@keyframes spinner-animation{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.spinner--light div{border:2px solid #fff;border-color:#fff transparent transparent transparent}.spinner--blue div{border:2px solid var(--color__indigo--200);border-color:var(--color__indigo--200) transparent transparent transparent}.spinner--dark div{border:2px solid rgba(0, 0, 0, 0.6);border-color:rgba(0, 0, 0, 0.6) transparent transparent transparent}";const ye=me;const ve=class{constructor(e){n(this,e);this.theme="light"}render(){return r("div",{key:"c1f2fd149cca0b944ffa2db778e3b38d369195f2",class:`spinner spinner--${this.theme}`},r("div",{key:"8c472cc798203711bf7bb6ca9f01be8b71433934"}),r("div",{key:"6d52db3213ebb56b413d671070cee8bbf7ea4754"}),r("div",{key:"48b2dd9d2d42ffa239da4c0a4e1eea0fe0e8bfdd"}),r("div",{key:"e6e79b1718b503cb6d400f2b8d5e93a2dd92a198"}))}};ve.style=ye;const ge="h1,h2,h3,p{margin:0;padding:0;font-weight:var(--font-weight__body)}h1{line-height:1}h2{color:var(--color__grey--500);line-height:1.2}.display{font-family:var(--font-family__serif)}.footnote{font-size:0.8em;color:var(--color__grey--600)}.success{color:var(--color__green--800)}.danger{color:var(--color__red--700)}.light-grey{color:rgba(0, 0, 0, 0.3)}.light{color:rgba(255, 255, 255, 0.6)}.regular{font-weight:400}.bold{font-weight:700}";const we=ge;const ke=class{constructor(e){n(this,e);this.variant="body";this.theme="default";this.weight="regular"}componentWillLoad(){this.generateClassList()}generateClassList(){this.classList=`${this.variant} ${this.theme} ${this.weight}`}render(){if(this.variant==="display"){return r("h1",{class:this.classList},r("slot",null))}else if(this.variant==="heading"){return r("h2",{class:this.classList},r("slot",null))}else if(this.variant==="subHeading"){return r("h3",{class:this.classList},r("slot",null))}else{return r("p",{class:this.classList},r("slot",null))}}};ke.style=we;const xe=":host{display:block}.edit-container{transition:all 0.25s;border:var(--border);border-radius:var(--border-radius);padding:calc(var(--padding) * 1.5);background:white}.edit-container--on{border:1px solid var(--color__grey--800);background:var(--color__grey--50)}.edit-container--off:hover{border:1px solid var(--color__grey--800);cursor:pointer}";const _e=xe;const je=class{constructor(e){n(this,e);this.EditModeForPassword=()=>[r("e-input",{type:"password",name:"newValue",placeholder:"New password"}),r("l-spacer",{value:.5}),r("e-input",{type:"password",name:"newValueRepeat",placeholder:"Re-type new password"})];this.EditModeOn=()=>r("div",{class:"edit-container edit-container--on"},r("e-text",{variant:"footnote"},this.label.toUpperCase()),r("l-spacer",{value:.5}),this.attribute==="password"?r(this.EditModeForPassword,null):r("e-input",{type:"text",name:"newValue",value:this.value}),r("l-spacer",{value:1}),r("l-row",{direction:"row-reverse"},r("div",null),r("l-row",null,r("e-button",{variant:"light",action:"cancelEditMode"},"Cancel"),"  ",r("e-button",{disabled:this.isSaveButtonDisabled,action:"saveEdit",active:this.isSaveButtonActive},"Save"))));this.EditModeOff=()=>r("div",{class:"edit-container edit-container--off",onClick:()=>this.isEditModeOn=true},r("e-text",{variant:"footnote"},this.label.toUpperCase()),r("l-row",{justifyContent:"space-between"},this.type==="text"&&r("e-text",null,this.value),this.type==="password"&&r("e-text",null,this.value),this.type==="link"&&r("e-link",{variant:"email",url:`mailto:${this.value}`},this.value)));this.type=undefined;this.entity=undefined;this.attribute=undefined;this.value=undefined;this.label=undefined;this.active=false;this.isEditModeOn=false;this.isSaveButtonDisabled=true;this.isSaveButtonActive=false}async handleButtonClickEvent(e){if(e.detail.action==="startEditMode"){this.isEditModeOn=true}else if(e.detail.action==="cancelEditMode"){this.isEditModeOn=false;this.isSaveButtonDisabled=true;this.isSaveButtonActive=false}else if(e.detail.action==="saveEdit"){this.handleSaveEdit()}}handleInputEvent(e){if(e.detail.name==="newValue"){this.newValue=e.detail.value}else if(e.detail.name==="newValueRepeat"){this.newValueRepeat=e.detail.value}this.setSaveButtonState()}setSaveButtonState(){if(!this.newValue){this.isSaveButtonDisabled=true;return}if(this.attribute==="password"){if(this.newValue!=this.newValueRepeat){this.isSaveButtonDisabled=true}else{this.isSaveButtonDisabled=false}}else{if(this.newValue!=this.value){this.isSaveButtonDisabled=false}else{this.isSaveButtonDisabled=true}}}async handleSaveEdit(){this.isSaveButtonActive=true;let e=D(this.entity,this.attribute,this.newValue);let{isValid:t,validationMessage:n}=N(e);if(!t){this.isSaveButtonActive=false;this.isSaveButtonDisabled=false;return alert(n)}let{success:r,message:s}=await E(e);this.isSaveButtonActive=false;this.isEditModeOn=false;if(!r){return alert(s)}if(this.attribute==="name"){v.accountName=this.newValue}else if(this.attribute==="email"){v.accountEmail=this.newValue;v.isEmailVerified=false}return alert(s)}render(){if(this.isEditModeOn){return r(this.EditModeOn,null)}else{return r(this.EditModeOff,null)}}};je.style=_e;const Se="";const $e=Se;const Ce=class{constructor(e){n(this,e);this.classMap={};this.variant="";this.justifyContent="space-between";this.align="center";this.direction="row"}componentWillLoad(){this.generateClassMap()}generateClassMap(){this.classMap.display="flex";this.classMap.alignItems=this.align;this.classMap.justifyContent=this.justifyContent;this.classMap.flexDirection=this.direction}render(){return r(s,{key:"d2c9b793b71338544e58cb795335e76b7f729b0f",style:this.classMap},r("slot",{key:"c7950ae34ae14b3316539641c2196a4b72220617"}))}};Ce.style=$e;const Ae=".separator--default{display:block;width:100%;height:1px;background:var(--color__grey--200)}#separator__oauth{display:flex;width:50%;justify-content:space-between;align-items:center;margin:0 auto}#separator__oauth c-text{font-size:1em;margin:0;color:var(--color__grey--400)}.separator__oauth--lines{width:35%;border-top:var(--border)}";const Oe=Ae;const Ee=class{constructor(e){n(this,e);this.variant="default"}render(){if(this.variant==="default"){return r("div",{class:"separator--default"})}else if(this.variant==="oauth"){return r("div",{id:"separator__oauth"},r("div",{class:"separator__oauth--lines"}),r("c-text",null,"OR"),r("div",{class:"separator__oauth--lines"}))}}};Ee.style=Oe;const Me=":host{display:block}";const De=Me;const Ie=class{constructor(e){n(this,e);this.classMap={};this.value=1;this.variant="vertical"}componentWillLoad(){if(this.variant==="vertical"){this.classMap.marginTop=`${this.value}em`;this.classMap.marginBottom=`${this.value}em`}else if(this.variant==="horizontal"){this.classMap.marginLeft=`${this.value}em`;this.classMap.marginRight=`${this.value}em`}}render(){return r(s,{key:"e0477dd2a957de90c7c7ab4c5e04af46376c552b",style:this.classMap})}};Ie.style=De;const Le=":host{width:1024px;position:fixed;background:var(--color__bg);display:block;padding:1em 0 0.75em 0;z-index:9;box-sizing:border-box;border-bottom:var(--border)}.hide-on-mobile{display:block}.show-on-mobile{display:none}@media only screen and (max-width: 768px){.hide-on-mobile{display:none}.show-on-mobile{display:block}#mobile-menu{position:fixed;left:0;top:0;width:100vw;height:100vh;background:var(--color__bg);box-sizing:border-box;padding:0.5em 1em 2em 2em}#mobile-menu__inner e-image{margin-top:5px}e-list{font-size:1.2em}}";const Re=Le;const Ne=class{constructor(e){n(this,e);this.isMobileMenuOpen=false}async handleButtonClickEvent(e){if(e.detail.action==="openMobileMenu"){this.isMobileMenuOpen=true}else if(e.detail.action==="closeMobileMenu"){this.isMobileMenuOpen=false}}render(){return r(s,{key:"da01f3519451d743ca2f439fbaaa2c28c36d95ef"},r("l-row",{key:"c63149f7fa35b0f71389f09b66127527b357fc19",justifyContent:"space-between",align:"anchor-center"},r("e-link",{key:"e4770e14042e3fc81098fc48cd22283057f925e9",url:"/"},r("e-image",{key:"72dfe31d0816cd975730b6ca7f43e3b2c25bffc6",src:g.app.branding.logo.logomark.withoutBg,width:"24px"})),r("e-text",{key:"8a6773a238ad9ac1fb7e60dbd6e40c44439feb99"},"Trial Period . ",r("e-link",{key:"27290859268e7ca73ce028160d0f557f66e87805",url:"/billing"},"Upgrade Now")),r("div",{key:"d7af9f3b54144c961ba249672b86c18525d7d994",class:"hide-on-mobile"},r("p-user-control",{key:"c45d226a23d8c774697f15b6671b11dd1fd2f60f"})),r("div",{key:"557774c049d4736cda46ba3459aca7ec10d2a4e9",class:"show-on-mobile"},r("e-button",{key:"4aa68a0f6bdad9729512805174b574121e228737",action:"openMobileMenu",variant:"light"},r("e-image",{key:"af47357abc410c888f463e3270466d5468a542b6",src:"../../../assets/icon/light/hamburger-menu.svg",width:"1.5em"})))),this.isMobileMenuOpen&&r("div",{key:"12f150a6bd348cb4a2cd0280715bb1bd7a8e3b98",id:"mobile-menu",class:"show-on-mobile"},r("div",{key:"35a20dacfca7ff92762aec5aeb994f5e8546e054",id:"mobile-menu__inner"},r("l-row",{key:"3df907e4f5c04d1ae67129c4c88bccbb13cd39f6",justifyContent:"space-between",align:"center"},r("div",{key:"b5c7807f86d8a309b2435f1559ef86ee6cc582ee"}),r("e-button",{key:"3561f329cf8bc2c131e2a5407a537ed992f65ab4",action:"closeMobileMenu",variant:"light"},r("e-image",{key:"e36071653f385c113116a3be25c7076ce42dc725",src:"../../../assets/icon/light/close.svg",width:"1.25em"}))),r("l-spacer",{key:"11f65c78eb2424a81f9d120dc99583944c239f23",value:2}),r("e-list",{key:"c1ddd1fc08714e2e6aa3f8edea4e001709e0f838"},r("e-list-item",{key:"0aaaa186edbad47958391e5cd66cd9bfba0bf853"},r("e-link",{key:"d73d2051de7338553693661789988be9fcfe3ff8",url:"/"},r("e-text",{key:"d6274b9e26e8fd325cbc8df0ca9ec3088c0ebbb1"},"Home"))),r("l-spacer",{key:"eafdedca55a9f044cef9f1dbe578ec0e0008f35e",value:1}),r("l-separator",{key:"3c2aba1e45b626b4acb852ae9c4a6b02bf6a60b7"}),r("l-spacer",{key:"d95bd513403d96659d1a1bbc2071acd25e444334",value:1}),r("e-list-item",{key:"dac013faf3adf1ab12ca4269f3fb0f1e9347c75f"},r("e-link",{key:"071c1fe2db28ba725b922af867970d1b0554cef2",url:"/account"},r("e-text",{key:"54a9af0670ec6e072e8f52785c964e3f632d7fbc"},"Account"))),r("l-spacer",{key:"a13fe4c3737656f3f3496a151e22d4378aa1452d",value:.5}),r("e-list-item",{key:"40156a4210df08056dd8288c5c4b29565cee6d94"},r("e-link",{key:"d4bc015054c1b17656412af539f6a9ab05282f8d",url:"/billing"},r("e-text",{key:"f0c361d615329f8bb87831600487d83d1a031af5"},"Billing"))),r("l-spacer",{key:"b53617baefcef8f0e576e5373649bbec60456c58",value:.5}),r("e-list-item",{key:"663eb45383d67fc618f01a2c0d77d710a13d33bb"},r("e-link",{key:"5c4ae3ab5c6bb5ff7bbaff1856620c218e3ef276",variant:"externalLink",url:g.app.contact.url},"Support")),r("l-spacer",{key:"858a363a07b4ce438e73208cb79dff3362b474f5",value:1}),r("l-separator",{key:"13b8da889c480e5f09db703e59d36a6690d293f7"}),r("l-spacer",{key:"804bef5b8c1a77e7c6820ebb64856d3c9b4b5be4",value:1}),r("e-list-item",{key:"5d6f84c4ceef8a7ec882379b3ef9a19bebcc023d"},r("e-button",{key:"bc49daca0bab55a5c4f1fcdf926ea12fff04d786",variant:"link",action:"logout"},r("e-text",{key:"d61b63cca4225ee0661a478e41e115e5b3cf3bff"},"Logout")))))))}};Ne.style=Re;const Pe=":host{display:block}c-card{position:fixed;-webkit-box-shadow:0px -2px 33px 1px rgba(0, 0, 0, 0.15);-moz-box-shadow:0px -2px 33px 1px rgba(0, 0, 0, 0.15);box-shadow:0px -2px 33px 1px rgba(0, 0, 0, 0.15);right:1.5em;border-radius:0}e-image{margin-top:3px;margin-left:0.25em}";const Te=Pe;const ze=class{constructor(e){n(this,e);this.isExpanded=false}async handleButtonClickEvent(e){if(e.detail.action==="toggleUserControl"){this.isExpanded=!this.isExpanded}}render(){return r(s,{key:"4358df4dda9fab5df0e1324bdf0a66cfd87145cc"},r("e-button",{key:"922af9a92d9b0c677ed22a7a5c450a20be95fea3",variant:"link",action:"toggleUserControl"},r("l-row",{key:"e971ba89032ec8acc6fb8510d77c3f5866ca12c5",align:"center"},r("e-text",{key:"68cbf9189bbdd8d0123071dc9f42b54858f54c3f",weight:"700"},v.accountName.split(" ")[0])," ",this.isExpanded?r("e-image",{src:"../../../assets/icon/light/caret-up.svg",width:"1em"}):r("e-image",{src:"../../../assets/icon/light/caret-down.svg",width:"1em"}))),this.isExpanded&&r("c-card",{key:"751eb86da653217dd0557379478e260bfc0aaa5a"},r("e-list",{key:"0f455cf3a50a6435128d515da06a2d25e338d01d"},r("e-list-item",{key:"1c0b75b02c582c6f796956786f286d1914071da4"},r("e-link",{key:"12fb9d527479f5bb761d3927b6911abf4f124cdb",url:"/"},r("e-text",{key:"75c742aee09dd7a308f79f8fc41837ef51651891"},"Home"))),r("l-spacer",{key:"e365148668041b42375a91066af35a199ec8c129",value:.5}),r("e-list-item",{key:"56f160778ce6f7cf019076a7f3bb16bab6bbede3"},r("e-link",{key:"755bff9f89e07db5b02105f1c20b7f4f9b48a56e",url:"/account"},r("e-text",{key:"9d8dec7f049c0846fca5df2fac26b473b273f845"},"Account"))),r("l-spacer",{key:"1c79c7dff155c855157d47aa5550bbd93c71fd7d",value:.5}),r("e-list-item",{key:"9ef3d5cfb4794e10010a2fd64bc60e99861d2683"},r("e-link",{key:"b60b2add5bfe9888d84f065ffeb07f618ccc4554",url:"/billing"},r("e-text",{key:"f3969f58a9af931c72a9311780bcfa44b01c590c"},"Billing"))),r("l-spacer",{key:"5173dd10f6e3660e891983e69d467fc9e99f42e2",value:.5}),r("e-list-item",{key:"64391ff0fd4ecf84ef605628b7e2fa7ecd7ad65c"},r("e-link",{key:"d6d5e753951d57b6cf9fb112a483de5b147255c0",variant:"externalLink",url:g.app.contact.url},"Support")),r("l-spacer",{key:"f043eeb34a47fc0d0eea4b20f7230fc4911e8688",value:.75}),r("l-separator",{key:"c819a47550e5d0a84fbccd803e17cdf0221da4cb"}),r("l-spacer",{key:"c357b1666d0718c09c9c7d25b8ff73580c369f3d",value:.75}),r("e-list-item",{key:"44a8bfd07f49d71377f9b413312873634fc7d10a"},r("e-button",{key:"f07c074f8a75e83f5d1fb641ec85fbd1553dd4aa",variant:"link",action:"logout"},r("e-text",{key:"59ac82c81e95d00958dece5cd3fa32467dcd560c"},"Logout"))))))}};ze.style=Te;const Fe=async e=>{let t=`${g.api.url}${g.api.endpoint.survey.create}`;let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const We=(e,t,n,r)=>{let s={type:e.trim(),title:t.trim(),websiteUrl:n.trim(),respondentAttributes:r};return s};const Ue=_.object({type:_.string().valid("sensePrice","senseFeature","senseLike","senseEmotion","senseQuery").required(),title:_.string().required(),websiteUrl:_.string().uri({scheme:["https"]}).allow(""),respondentAttributes:_.array().items(_.string()).min(0).max(1024).required()});const qe=e=>{let{error:t}=Ue.validate(e);if(t){return{isValid:false,validationMessage:`❌ ${t.details[0].message}`}}else{return{isValid:true,validationMessage:""}}};const Ve=":host{display:block;width:100vw;height:100vh;background:white;position:fixed;top:0;left:0;z-index:99999}main{width:90%;max-width:400px;margin:0 auto;margin-top:10vh}.row{display:flex;justify-content:space-between}.row--reverse{flex-direction:row-reverse}.survey-radio{display:block;border-radius:var(--border-radius);border:1px solid white;padding:var(--padding);transition:all 0.25s}.survey-radio:hover{cursor:pointer;color:var(--color__grey--800);border:1px solid var(--color__grey--800)}.survey-radio--active{color:var(--color__grey--800);border:1px solid var(--color__grey--800);background:var(--color__grey--100)}.gallery{margin-top:1.5em;display:flex;flex-wrap:wrap;gap:1em}.mandatory{color:var(--color__red--400)}.respondent-attributes{margin:0;padding-left:0em;list-style-position:inside}.radio-container{display:flex;gap:1em;font-family:sans-serif}";const Be=Ve;const Ge=class{constructor(e){n(this,e);this.wizardCompletionEventEmitter=i(this,"wizardCompletionEvent",7);this.respondentAttributes=[];this.surveyTitlePlaceholder={sensePrice:"e.g. Berrito Fizz Price Survey",senseFeature:"e.g. EVCharge Map Feature Survey",senseLike:"e.g. Blog Post 1 Survey",senseEmotion:"e.g. Donna Pizza Checkout Survey",senseQuery:"e.g. Landing Page Faq Survey"};this.surveyEmbedPlaceholder={sensePrice:"e.g. https://example.com/pricing",senseFeature:"e.g. https://example.com/landing-page",senseLike:"e.g. https://example.com/blog/post1",senseEmotion:"e.g. https://example.com/checkout-feedback",senseQuery:"e.g. https://example.com/faq"};this.SurveyRadio=({label:e,description:t,value:n})=>r("div",{class:`survey-radio ${this.surveyType===n&&"survey-radio--active"}`,onClick:()=>this.handleSurveyChoice(n)},r("e-text",null,r("strong",null,e)),r("e-text",null,t));this.SurveyTypeStep=()=>r("article",null,r(this.SurveyRadio,{label:"SensePrice",description:"Discover the price that maximizes revenue",value:"sensePrice"}),r(this.SurveyRadio,{label:"SenseFeature",description:"Prioritize features that make an impact",value:"senseFeature"}),r(this.SurveyRadio,{label:"SenseLikes",description:"Measure what people like and dislike",value:"senseLike"}),r(this.SurveyRadio,{label:"SenseEmotion",description:"Learn how people are reacting",value:"senseEmotion"}),r(this.SurveyRadio,{label:"SenseQuery",description:"Uncover the unanswered questions",value:"senseQuery"}),r("l-spacer",{value:3}),r("div",{class:"row"},r("div",null),r("e-button",{disabled:this.surveyType.length>0?false:true,action:"nextStep"},"Next")));this.SurveyDetailsStep=()=>r("article",null,r("e-text",null,this.generateProperSurveyName(this.surveyType)," Survey Title"," ",r("span",{class:"mandatory"},"*")),r("l-spacer",{value:.5}),r("e-input",{type:"text",name:"surveyTitle",placeholder:this.surveyTitlePlaceholder[this.surveyType],value:this.surveyTitle}),r("l-spacer",{value:.5}),r("e-text",{variant:"footnote"},r("e-link",{variant:"externalLink",url:"https://sensefolks.com"},"Read survey naming tips")),r("l-spacer",{value:2}),r("e-text",null,"Will you embed this survey on a webpage?"," ",r("span",{class:"mandatory"},"*")),r("l-spacer",{value:.5}),r("div",{class:"radio-container"},r("e-input",{type:"radio",name:"surveyEmbed",value:"yes",checked:this.surveyEmbed},"Yes"),r("e-input",{type:"radio",name:"surveyEmbed",value:"no",checked:!this.surveyEmbed},"No")),this.surveyEmbed&&r("div",null,r("l-spacer",{value:2}),r("e-text",null,"Enter webpage url ",r("span",{class:"mandatory"},"*")),r("l-spacer",{value:.5}),r("e-input",{type:"text",name:"websiteUrl",placeholder:this.surveyEmbedPlaceholder[this.surveyType],value:this.websiteUrl})),r("l-spacer",{value:3}),r("div",{class:"row"},r("e-button",{variant:"light",action:"prevStep"},"Back"),r("e-button",{disabled:this.surveyTitle.length>0?false:true,action:"nextStep"},"Next")));this.SurveyRespondentDetailsStep=()=>r("article",null,r("e-text",null,"What would you like to know about the survey respondents? (Optional)"),r("div",{class:"gallery"},r("e-input",{type:"checkbox",value:"name",name:"respondentAttributes"},"Name"),r("e-input",{type:"checkbox",value:"email",name:"respondentAttributes"},"Email"),r("e-input",{type:"checkbox",value:"age",name:"respondentAttributes"},"Age"),r("e-input",{type:"checkbox",value:"gender",name:"respondentAttributes"},"Gender"),r("e-input",{type:"checkbox",value:"country",name:"respondentAttributes"},"Country"),r("e-input",{type:"checkbox",value:"state",name:"respondentAttributes"},"State"),r("e-input",{type:"checkbox",value:"industry",name:"respondentAttributes"},"Industry"),r("e-input",{type:"checkbox",value:"designation",name:"respondentAttributes"},"Designation"),r("e-input",{type:"checkbox",value:"companySize",name:"respondentAttributes"},"Company Size")),r("l-spacer",{value:3}),r("div",{class:"row"},r("e-button",{variant:"light",action:"prevStep"},"Back"),r("e-button",{disabled:this.surveyTitle.length>0?false:true,action:"nextStep"},"Confirm")));this.SurveyConfirmationStep=()=>r("article",null,r("e-text",null,"Survey Type"),r("e-text",null,r("strong",null,this.generateProperSurveyName(this.surveyType))),r("l-spacer",{value:2}),r("e-text",null,"Survey Title"),r("e-text",null,r("strong",null,this.surveyTitle)),this.websiteUrl.length>0&&r("div",null,r("l-spacer",{value:2}),r("e-text",null,"Website"),r("e-text",null,r("strong",null,this.websiteUrl))),this.respondentAttributes.length>0&&r("div",null,r("l-spacer",{value:2}),r("e-text",null,"Respondent Attributes"),r("ul",{class:"respondent-attributes"},this.respondentAttributes.map((e=>r("li",null,r("strong",null,e)))))),r("l-spacer",{value:3}),r("div",{class:"row"},r("e-button",{variant:"light",action:"prevStep"},"Back"),r("e-button",{disabled:this.surveyTitle.length>0?false:true,action:"createSurvey",active:this.isCreatingSurvey},"Create Survey")));this.wizardTitle="";this.currentStageIndex=0;this.surveyType="";this.surveyTitle="";this.websiteUrl="";this.surveyEmbed=false;this.isCreatingSurvey=false;this.stages=[{label:"Choose Survey Type"},{label:"Enter Survey Details"},{label:"Collect Respondent Details"},{label:"Confirm Survey Details"}]}async handleButtonClickEvent(e){if(e.detail.action==="closeWizard"){v.isWizardActive=false}else if(e.detail.action==="nextStep"){if(this.currentStageIndex+1>=this.stages.length){return}this.currentStageIndex=this.currentStageIndex+1}else if(e.detail.action==="prevStep"){if(this.currentStageIndex<=0){return}this.currentStageIndex=this.currentStageIndex-1;if(this.currentStageIndex===0){this.surveyTitle="";this.respondentAttributes=[];this.respondentAttributes=[...this.respondentAttributes]}}else if(e.detail.action==="createSurvey"){this.createSurvey()}}handleInputEvent(e){if(e.detail.name==="surveyTitle"){this.surveyTitle=e.detail.value}else if(e.detail.name==="websiteUrl"){this.websiteUrl=e.detail.value}else if(e.detail.name==="respondentAttributes"){if(e.detail.isChecked){this.respondentAttributes.push(e.detail.value)}else{let t=[];for(let n=0;n<this.respondentAttributes.length;n++){if(this.respondentAttributes[n]!==e.detail.value){t.push(this.respondentAttributes[n])}}this.respondentAttributes=t}}else if(e.detail.name==="surveyEmbed"){if(e.detail.value==="yes"){this.surveyEmbed=true}else if(e.detail.value==="no"){this.surveyEmbed=false}}}componentWillLoad(){if(v.wizardType==="createSurvey"){this.wizardTitle="Create Survey"}else{this.wizardTitle="Wizard Title"}}handleSurveyChoice(e){this.surveyType=e}generateProperSurveyName(e){if(e==="sensePrice"){return"SensePrice"}else if(e==="senseFeature"){return"SenseFeature"}else if(e==="senseLike"){return"SenseLike"}else if(e==="senseEmotion"){return"SenseEmotion"}else if(e==="senseQuery"){return"SenseQuery"}}async createSurvey(){let e=We(this.surveyType,this.surveyTitle,this.websiteUrl,this.respondentAttributes);let{isValid:t,validationMessage:n}=qe(e);if(!t){return alert(n)}this.isCreatingSurvey=true;let{success:r,message:s}=await Fe(e);this.isCreatingSurvey=false;if(!r){return alert(s)}v.isWizardActive=false;this.wizardCompletionEventEmitter.emit()}handleRadioInput(e,t){if(e==="surveyEmbed"){this.surveyEmbed=t}}render(){return r(s,{key:"805b0abcf8b141229133c7e34e20c40f2298a9e2"},r("main",{key:"ad623fbbea4b1a216d03783878d6b2a4baab2ca4"},r("l-row",{key:"804d7bc28003512c8957d72d1fbda8a91332b3d6"},r("e-text",{key:"b1014a6e7509734130e0d0f674f217a794af599e",variant:"display"},this.wizardTitle),r("e-button",{key:"6e9a4f61ad46d05f07bfd510d0ec8efd0ddcbd70",action:"closeWizard",variant:"light"},r("e-image",{key:"ecd0619edbfa04bd0b97fd6edb8a8ab4756d803f",src:"../../../assets/icon/light/close.svg",width:"1.25em"}))),r("e-text",{key:"5a801967f83dc5cdfbd966d9bac0831eb9a0d203",variant:"footnote"},"STEP ",this.currentStageIndex+1," OF ",this.stages.length," -"," ",this.stages[this.currentStageIndex].label.toUpperCase()),r("l-spacer",{key:"b55e1b2906ec8d542b8ccdf9f62c6760ce235664",value:1.5}),r("l-separator",{key:"29ca8b75f4005f29a4055551947207412e331798"}),r("l-spacer",{key:"2dd2d656c0008ea5ef12874c89f6c94863b06a26",value:3}),this.currentStageIndex===0&&r(this.SurveyTypeStep,{key:"238459345500df27b4f7b51fa17d9f600a30c0eb"}),this.currentStageIndex===1&&r(this.SurveyDetailsStep,{key:"10f4868edbce6a5a00287051d97c21485d420e2b"}),this.currentStageIndex===2&&r(this.SurveyRespondentDetailsStep,{key:"b0d4fd9701f64281fdaee4c5544dcebf5a796318"}),this.currentStageIndex===3&&r(this.SurveyConfirmationStep,{key:"14dc2d89f38a62dbb4e5dace683ac89b1f8c0cdb"})))}};Ge.style=Be;const He=".container{width:90%;max-width:640px}";const Ze=He;const Ye=class{constructor(e){n(this,e)}componentWillLoad(){v.activeView="account";document.title="Account | Sensefolks"}render(){return r("c-main",{key:"e8d89a5bf8c82762fadee98b526a59ff215f3a75"},r("div",{key:"0996b6d51ff9951585f2f400af57f60129387960",class:"container"},r("e-text",{key:"ff9bd0d434901be83f1e1465e325ed210452cad6",variant:"heading"},"Account Details"),r("l-spacer",{key:"3bd5279d2f46afe54f7004df782c38b8719ffab8",value:2}),r("e-text-editable",{key:"34f78d4a082b610853d4b313133a00de612bc6fa",label:"name",type:"text",value:v.accountName,entity:"account",attribute:"name"}),r("l-spacer",{key:"2e1561faad20cb732caa91a34fa11fd605c513f5",value:2}),r("e-text-editable",{key:"223b168317fb6114200148f3301bc831924f3d16",label:"email",type:"link",value:`${v.accountEmail}`,entity:"account",attribute:"email"}),r("l-spacer",{key:"5021962e6d8b6398b46c607b713297b1754607a6",value:2}),r("e-text-editable",{key:"4e21887d11a49165d52e3bb06b73fd6bfeae6465",label:"password",type:"password",value:"********",entity:"account",attribute:"password"}),r("l-spacer",{key:"073b70e69415897cab647d1b4dcb7abc52978ee1",value:2}),r("l-row",{key:"0066b20e3b3e1a8eef9c36b3e305d71ee1e834ba"},r("e-link",{key:"5c781159171d6ed4cb371a752bf08b0570f23b1e",url:"/delete-account",theme:"danger"},r("e-text",{key:"a8419b5a83b084d02783eeee88a85c4a9cb6d852"}," Delete account and data")))))}};Ye.style=Ze;const Je=".container{width:90%;max-width:640px}";const Qe=Je;const Ke=class{constructor(e){n(this,e)}componentWillLoad(){v.activeView="billing";document.title="Billing | Sensefolks"}render(){return r("c-main",{key:"dd4d89e8202c31d980d29966d8c744e4580d7113"},r("div",{key:"0b334e905cdfa17dc0e692d37b444cfe74cc83f5",class:"container"},r("e-text",{key:"70c55ba858a68ee178bf2eb821912ea76076a8a8",variant:"heading"},"Billing"),r("l-spacer",{key:"2dd3198cd81b45fa11e9f9fc585b24451bab8633",value:2}),r("e-link",{key:"0190f95c549894f72222c593df9df56d938bfaf8",url:"/checkout"},"Checkout"),r("br",{key:"db42baf0d861f3fb5c67131bb7e4788bc6dcdf93"}),r("e-link",{key:"bf8435cb4250ee2c0209daca9f97bf1ec273789d",url:"/checkout/bmvbmw"},"Checkout (with value)")))}};Ke.style=Qe;const Xe=":host{display:block}";const et=Xe;const tt=class{constructor(e){n(this,e)}componentDidLoad(){if(!v.isSessionActive){o.push("/login")}else{o.push("/surveys")}}render(){return r(s,{key:"76bc1bc233b3652fe59a60f8afb9172551ef3916"},r("slot",{key:"6196afb9070c28928c3af80c961e3fe668ce2286"}))}};tt.style=et;const nt='c-card{width:90%;max-width:400px}input[type="radio"]:hover{cursor:pointer}table{border-collapse:collapse;width:100%;border-radius:0.5em}table,td{border:1px solid var(--color__grey--200);text-align:left}th,td{padding:0.5em 0.5em}.skel__line{height:10px;border-radius:0.25em;background:#ccc;background-image:linear-gradient(\n    90deg,\n    #f4f4f4 0px,\n    rgba(229, 229, 229, 0.8) 40%,\n    #f4f4f4 100%\n  );background-size:200%;animation:skel-shine 1s infinite ease-out}@keyframes skel-shine{0%{background-position:-100px}40%,100%{background-position:140px}}';const rt=nt;const st=class{constructor(e){n(this,e);this.routeToEvent=i(this,"routeToEvent",7);this.productName="";this.subscriptionType="";this.currency="";this.price=0;this.total=0;this.Details=()=>r("div",null,r("e-text",{variant:"subHeading"},this.productName),r("e-text",null,this.subscriptionType),r("l-spacer",{value:.5}),r("l-separator",null));this.Skel=()=>r("div",null,r("l-spacer",{value:1}),r("div",{class:"skel__line"}),r("l-spacer",{value:1}),r("div",{class:"skel__line"}),r("l-spacer",{value:1}),r("div",{class:"skel__line"}),r("l-spacer",{value:1}));this.Summary=()=>r("div",null," ",r("table",null,r("tr",null,r("td",null,"Item cost"),r("td",null,this.currency,this.price)),r("tr",null,r("td",null,r("strong",null,"Grand total")),r("td",null,r("strong",null,this.currency,this.total)))));this.orderId=undefined;this.isViewDataFetched=false;this.isConfirmAndPayButtonActive=false;this.isConfirmAndPayButtonDisabled=true}handleButtonClickEvent(e){if(e.detail.action==="createCheckoutSession"){this.createCheckoutSession()}else if(e.detail.action==="goBack"){this.routeToEvent.emit({type:"goBack",data:{}})}}createCheckoutSession(){console.log("Create checkout session")}componentWillLoad(){v.activeView="checkout";document.title="Checkout | Sensefolks"}componentDidLoad(){this.fetchViewData()}fetchViewData(){}render(){return r("c-main",{key:"7c874b05758da31f6f62ddcfaef84ed39c4646be"},r("c-card",{key:"f3257fbdfa4783c95619e548258b5462775c152d"},r("e-text",{key:"e0abe9f07421a085406f68e919aba8e1fba51155",variant:"heading"},"Checkout"),r("l-spacer",{key:"878d0754e85abf6d2435a1371f3aed6eb5821c68",value:1.5}),r("l-separator",{key:"edf78ee6dee4654417d815fc27bbe1d42f1a09e3"}),r("l-spacer",{key:"aa592a1584bdb2f324b5d60e02013cf818a3cb57",value:2.5}),this.isViewDataFetched?r("div",null,r(this.Details,null),r("l-spacer",{value:.25}),r(this.Summary,null),r("l-spacer",{value:.5}),r("e-text",{variant:"footnote"},"Item cost includes GST")):r("div",null,r(this.Skel,null),r("l-spacer",{value:2}),r(this.Skel,null)),r("l-spacer",{key:"0bca41c405823731caaeb2f2ad1977dec6db058d",value:2}),r("l-row",{key:"039727c1aefea81b8c10a6f7c820cc2ddc93be01",justifyContent:"space-between"},r("e-link",{key:"101d4b05ba3e27b1fc67c62a28f018c703b014c3",url:"../"},"Back"),r("e-button",{key:"278b8c4324248a98163d26b6033e212e8a4708ce",action:"createCheckoutSession",disabled:this.isConfirmAndPayButtonDisabled,active:this.isConfirmAndPayButtonActive},"Confirm & pay"))))}};st.style=rt;const it=async()=>{let e=`${g.api.url}${g.api.endpoint.account.details}`;let t={method:"DELETE",headers:{"Content-Type":"application/json"},credentials:"include"};let n;await fetch(e,t).then((e=>e.json())).then((e=>{n=e})).catch((e=>{console.log(e)}));return{success:n.success,message:n.message,payload:n.payload}};const ot=".container{width:90%;max-width:640px}";const at=ot;const ct=class{constructor(e){n(this,e);this.logoutEventEmitter=i(this,"logoutEvent",7);this.isDeletingAccount=false}async handleButtonClickEvent(e){if(e.detail.action==="deleteAccount"){this.deleteAccount()}}async deleteAccount(){this.isDeletingAccount=true;let{success:e,message:t}=await it();this.isDeletingAccount=false;alert(t);if(!e){return}this.logoutEventEmitter.emit()}componentWillLoad(){v.activeView="deleteAccount";document.title="Delete Account | Sensefolks"}render(){return r("c-main",{key:"d9bdd96d239d055d3ac029b736ec5e17bd0368ab"},r("div",{key:"7315c3be64070dda04be11266209ed16fa14c7c6",class:"container"},r("e-text",{key:"****************************************",variant:"heading"},"Delete your account?"),r("l-spacer",{key:"609d1ddea3b99497f9d1c21d4ae6eff89a32024d",value:2}),r("e-text",{key:"cd496cd2f107f34713605aae13521aab92eadff2"},"Deleting your account is an irreversible step. Your account data and survey data will be ",r("u",{key:"3ba6c9ff5fd8360c24a04528918b8a079f3aa38a"},"lost forever"),"."," "),r("l-spacer",{key:"0b27e06e476b1d80f6a514bd5675cf448cefc861",value:1}),r("e-text",{key:"2ad529e69fd0d1d22bb3a01108e2e418b637621b"},r("strong",{key:"f7dae8b78df8dcb4d5701c9a24d5f7c461f9345d"},"Are you absolutely sure that you want to delete your account?")),r("l-spacer",{key:"f113c2954c29ae5f6d8d2f94812d8276979304f1",value:3}),r("l-row",{key:"ff208cb7a1171cdfcf6f645abf5d97cb4bfd2f79",justifyContent:"space-between",align:"center"},r("e-link",{key:"10c3dea4143368f14338809d2eacc79198352683",url:"/account"},"Back"),r("e-button",{key:"dd1894369f6988bdb2a0b69a0670fcb26ef3558e",action:"deleteAccount",theme:"danger",active:this.isDeletingAccount},"Yes, delete my account"))))}};ct.style=at;const lt=async e=>{let t=`${g.api.url}${g.api.endpoint.account.auth.login}`;let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const ut=(e,t)=>{let n={email:e.trim().toLowerCase(),password:t.trim()};return n};const dt=_.object({email:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required(),password:_.string().min(8).max(1024).required()});const ft=e=>{let{error:t}=dt.validate(e);if(t){return{isValid:false,validationMessage:`❌ ${t.details[0].message}`}}else{return{isValid:true,validationMessage:""}}};const ht=":host{display:flex;justify-content:space-around;align-items:center;height:100vh}.form{width:320px;border:0}#logo-container{display:inline-block;padding:var(--padding);border-bottom:1px solid var(--color__grey--800);border-radius:var(--border-radius)}e-input{display:block;margin-top:1em;margin-bottom:1em}e-link{display:inline-block}";const bt=ht;const pt=class{constructor(e){n(this,e);this.authSuccessfulEventEmitter=i(this,"authSuccessfulEvent",7);this.email="";this.password="";this.isLoggingIn=false}handleButtonClickEvent(e){if(e.detail.action==="loginUser"){this.loginUser()}}handleInputEvent(e){if(e.detail.name==="email"){this.email=e.detail.value}else if(e.detail.name==="password"){this.password=e.detail.value}}async loginUser(){let e=ut(this.email,this.password);let{isValid:t,validationMessage:n}=ft(e);if(!t){return alert(n)}this.isLoggingIn=true;let{success:r,message:s}=await lt(e);this.isLoggingIn=false;if(!r){return alert(s)}this.authSuccessfulEventEmitter.emit()}componentWillLoad(){v.activeView="login";document.title="Login | Sensefolks"}render(){return r(s,{key:"e3df6113966b05830e29d4726de17739f500d790"},r("div",{key:"e6a6f3c6855723d9b4b538843f4262e036e2ab0f",class:"form"},r("e-image",{key:"f6c1ad3aea5c32629537915037e9a0687fe7c8c1",src:g.app.branding.logo.logotype.withoutBg,width:"180px"}),r("l-spacer",{key:"eebbd714660a15c8945e098fc4a289739b1c3292",value:.75}),r("l-separator",{key:"b35db383791a9bbfc42df6183123187e1aac20ae"}),r("l-spacer",{key:"db95b4e5777a703368be4f6fa29feb58e2b4c842",value:2}),r("e-text",{key:"18671dadeb537be15ccf579283ed25d506360d7a",variant:"display"},"Log in"),r("l-spacer",{key:"76f5961199fac721f7a7574eccb06aa12ad8c4c4",value:.5}),r("l-row",{key:"8ae4ed8fc5e92b0b7953ecd2db7b7fc5b4563b4e",justifyContent:"flex-start"}),r("e-text",{key:"9c2eeb8db21809eaa5a56082da942242d18a7d95"},"Don't have an account? ",r("e-link",{key:"43ba5be1279516cfedbb13f4126be1cdc3abc9de",url:"/signup"},"Sign up")),r("l-spacer",{key:"98e3183408588dd24c4645c387a2b5bb3dbe6238",value:1.5}),r("e-button-oauth",{key:"8662facba5398df5610774f1adde077f8816a1ae"}),r("l-spacer",{key:"8963bffd08774407d8670814706f9d2e5c7ca5f6",value:2}),r("l-separator",{key:"067c1d3593f242ddc11fb65fb24e0847b2b61f10",variant:"oauth"}),r("l-spacer",{key:"7d2224927e9ae96828599c3f4152111b5858d47b",value:2}),r("e-input",{key:"b32f3d9d8a72ec3d4ed64473ea0b3b4f2233df41",type:"email",name:"email",placeholder:"Email"}),r("l-spacer",{key:"9ee13d0a6b15bb5862485c586eaf87621464bc0c",value:1.5}),r("e-input",{key:"9ffa81245376334f0d8832b813bec34995f9a56d",type:"password",name:"password",placeholder:"Password"}),r("l-spacer",{key:"1ca5850757448ac29cedd8f3ff5538f285d490a3",value:1.5}),r("e-button",{key:"6c1358a1e9be68122d38e5f36e24448f6a35e160",action:"loginUser",active:this.isLoggingIn,size:"wide"},"Log in"),r("l-spacer",{key:"5cf6198cb8dab2dd28d725af571e31e9f1f6b983",value:1}),r("e-link",{key:"3a23f6e0bf4a9f28ddcc4c7d333595ac08ae564e",url:"/password-reset"},"Reset password"),r("l-spacer",{key:"020e1c82cf2d7ec36a2b2e2d0ed1ee2a4360dc7a",value:1.5}),r("l-separator",{key:"4d79ffbbcac4237daba38760263de88f5dc54294"}),r("l-spacer",{key:"39637771a9e21896a10a9c3cab7393ee5ef039cc",value:1.5}),r("e-text",{key:"ad80115b2d58e9c7b78237829fedf29eb7a40151",variant:"footnote"},"By logging in, you accept our"," ",r("e-link",{key:"6a8b678b9d19b43bb734e720a4c683aa93d7bb46",variant:"externalLink",url:g.app.policy.tos.url},"terms of service")," ","&"," ",r("e-link",{key:"54bd9292ee2e909d94a26b6edba8c45c597567b0",variant:"externalLink",url:g.app.policy.privacy.url},"privacy policy"))))}};pt.style=bt;const mt=async e=>{let t=`${g.api.url}${g.api.endpoint.account.password}`;let n={method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const yt=(e,t,n,r)=>{let s={email:e,newPassword:t,newPasswordRepeat:n,passwordResetCode:r};return s};const vt=_.object({email:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required(),newPassword:_.string().trim().min(8).required(),newPasswordRepeat:_.string().equal(_.ref("newPassword")).trim().required(),passwordResetCode:_.string().trim().length(4).required()});const gt=e=>{let{error:t}=vt.validate(e);if(t){return{isValid:false,validationMessage:t.details[0].message}}else{return{isValid:true,validationMessage:""}}};const wt=":host{display:flex;justify-content:space-around;align-items:center;height:100vh}.form{width:320px;border:0}";const kt=wt;const xt=class{constructor(e){n(this,e);this.email="";this.passwordResetCode="";this.newPassword="";this.newPasswordRepeat="";this.SendPasswordResetCode=()=>[r("e-text",{variant:"display"},"Password Reset"),r("l-spacer",{value:1}),r("e-input",{type:"email",name:"email",placeholder:"Email"}),r("l-spacer",{value:1}),r("l-row",{justifyContent:"space-between"},r("e-link",{url:"../"},"Back"),r("e-button",{action:"mailPasswordResetCode",active:this.isMailingPasswordResetCode},"Send reset code"))];this.ConfirmPassword=()=>[r("e-text",{variant:"display"},"Confirmation"),r("l-spacer",{value:1}),r("c-banner",{theme:"success"},r("e-text",null,"Password reset code sent to ",r("u",null,this.email))),r("l-spacer",{value:1}),r("e-input",{type:"password",name:"newPassword",placeholder:"New password (min 8 chars)",value:""}),r("l-spacer",{value:1}),r("e-input",{type:"password",name:"newPasswordRepeat",placeholder:"Repeat new password",value:""}),r("l-spacer",{value:1}),r("e-input",{type:"text",name:"passwordResetCode",placeholder:"4-digit reset code",value:""}),r("l-spacer",{value:1}),r("l-row",{justifyContent:"space-between"},r("e-button",{action:"backToSendPasswordResetCode",variant:"light"},"Back"),r("e-button",{action:"resetPassword",active:this.isSavingNewPassword},"Confirm"))];this.Success=()=>r("c-banner",{theme:"success"},r("e-text",null,r("strong",null,"Password Changed")),r("e-link",{url:"/login"},"Proceed to login"));this.isMailingPasswordResetCode=false;this.isSavingNewPassword=false;this.compState="sendPasswordResetCode"}handleInputEvent(e){if(e.detail.name==="email"){this.email=e.detail.value}else if(e.detail.name==="passwordResetCode"){this.passwordResetCode=e.detail.value}else if(e.detail.name==="newPassword"){this.newPassword=e.detail.value}else if(e.detail.name==="newPasswordRepeat"){this.newPasswordRepeat=e.detail.value}}handleButtonClickEvent(e){if(e.detail.action==="mailPasswordResetCode"){console.log("mail passwrod reset code");this.mailPasswordResetCode()}else if(e.detail.action==="backToSendPasswordResetCode"){this.compState="sendPasswordResetCode"}else if(e.detail.action==="resetPassword"){this.resetPassword()}}async mailPasswordResetCode(){let e=M(this.email,"passwordResetCode");let{isValid:t,validationMessage:n}=L(e);if(!t){return alert(n)}this.isMailingPasswordResetCode=true;await O(e);this.isMailingPasswordResetCode=false;this.compState="confirmPassword"}async resetPassword(){let e=yt(this.email,this.newPassword,this.newPasswordRepeat,this.passwordResetCode);let{isValid:t,validationMessage:n}=gt(e);if(!t){return alert(n)}this.isSavingNewPassword=true;let{success:r,message:s}=await mt(e);this.isSavingNewPassword=false;if(!r){return alert(s)}this.compState="passwordSaved"}componentWillLoad(){v.activeView="resetPassword";document.title="Reset Password | Sensefolks"}render(){return r(s,{key:"6fc9f1038a42c32b5e3c7a2198344df80446a4d7"},r("div",{key:"e486d93ca43f14836b619e389a3121a08b98e036",class:"form"},this.compState==="sendPasswordResetCode"&&r(this.SendPasswordResetCode,{key:"bc7022e451504d9d58dcc7afad72a40d66160bdf"}),this.compState==="confirmPassword"&&r(this.ConfirmPassword,{key:"8c278e546e5bf90a0f5cc92c2c61bdb4bdf49fed"}),this.compState==="passwordSaved"&&r(this.Success,{key:"d29930559a3674b971bf7fef9b9cba3074f74ef4"})))}};xt.style=kt;const _t=':host{display:flex;width:100%;height:100vh;background:var(--color__red--50);justify-content:space-around;align-items:center}.text--danger{font-family:"Space Grotesk", sans-serif;color:var(--color__red--900);margin:0;padding:0}';const jt=_t;const St=class{constructor(e){n(this,e)}componentWillLoad(){v.activeView="paymentFailed";document.title="Payment Failed | Sensefolks"}render(){return r(s,{key:"e388249e859b3472e99d8abb25a4a1a494fefb38"},r("c-card",{key:"8d2db73ef88788b68a341a4a904cb393a3571df1"},r("e-text",{key:"24891a9e2b99fbbe91e5d2ea2b3524866cba8177",variant:"display",theme:"danger"},"Payment Failed"),r("l-spacer",{key:"aa4400160624db9d6d1c78429f4dcad2f602a656",value:1}),r("e-text",{key:"79cdcc83d8133756c1ad06e4498f63093f3d9c57"},"Please try purchasing again. If money was deducted ",r("br",{key:"d8612ba3e6d16988a3cdeb6cd8fe2edf93a78522"}),"from your account/card, kindly write a mail to us at:"),r("e-link",{key:"b701a0b2e7ea2a67b62fda90a0108eac974db659",url:`mailto:${g.app.contact.email}`},g.app.contact.email),r("l-spacer",{key:"1b1ec956ee5d141bf34584ca191a1d1190824ae7",value:1}),r("e-link",{key:"cec0c9c49ebd96b9b999b5d28de8a4ee0523363d",url:"/"},"Go to account")))}};St.style=jt;const $t=":host{display:block}";const Ct=$t;const At=class{constructor(e){n(this,e);this.Skel=()=>r("c-card",null,r("e-text",null,r("strong",null,"Confirming payment..")),r("div",{class:"skel__line"}),r("div",{class:"skel__line"}),r("div",{class:"skel__line"}));this.Default=()=>r("c-card",null,r("e-text",{variant:"display",theme:"success"},"Payment Successful"),r("l-spacer",{value:1}),r("e-text",null,"You have upgraded to ",r("strong",null,"XYZ Plan")),r("l-spacer",{value:1}),r("e-link",{url:"/"},"Go to account"));this.sessionId=undefined;this.isViewDataFetched=false}componentWillLoad(){v.activeView="paymentSuccess";document.title="Payment Success | Sensefolks";console.log(`sessionId: ${this.sessionId}`)}render(){return r(s,{key:"****************************************"},this.isViewDataFetched?r(this.Default,null):r(this.Skel,null))}};At.style=Ct;const Ot=async e=>{let t=`${g.api.url}${g.api.endpoint.account.auth.oauth.google}`;let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const Et=e=>{let t={token:e};return t};const Mt=":host{display:flex;justify-content:space-around;align-items:center;height:100vh}#post-oauth-container{display:block;max-width:320px;margin:0 auto;padding-top:6em}";const Dt=Mt;const It=class{constructor(e){n(this,e);this.authSuccessfulEventEmitter=i(this,"authSuccessfulEvent",7);this.closeModalEventEmitter=i(this,"closeModal",7);this.routeToEvent=i(this,"routeToEvent",7);this.Loader=()=>r("l-row",{align:"center"},r("p-spinner",null),r("l-spacer",{value:1}),r("e-text",null,"Fetching profile info.."));this.Error=()=>r("div",null,r("e-text",{theme:"danger"},r("strong",null,"Oops..")),r("e-text",null,"We could not fetch your google account details. Please try again"),r("e-text",null,r("e-link",{url:"/login"},"Login ")," /"," ",r("e-link",{url:"/signup"},"Sign up")));this.service=undefined;this.activeView="fetching"}componentWillLoad(){v.activeView="postOauth";document.title="Post-Oauth | Sensefolks"}componentDidLoad(){if(this.service==="google"){this.getGoogleProfile(v.oauthToken)}}async getGoogleProfile(e){let t=Et(e);let{success:n}=await Ot(t);if(!n){this.closeModalEventEmitter.emit();this.activeView="error";return}this.authSuccessfulEventEmitter.emit();setTimeout((()=>{this.routeToEvent.emit({type:"push",route:"/",data:{}})}),1e3)}render(){return r(s,{key:"d802f63f49d190cfc3ca636667166b8fad25cc25"},r("div",{key:"ab0d90bc7139cd3e6809860b13c529e9434f47e4",id:"post-oauth-container"},this.activeView==="fetching"&&r(this.Loader,{key:"b929de38fd26af81922f80cb94b09a99aa46b0f0"}),this.activeView==="error"&&r(this.Error,{key:"81ef0b4c703fd679176f3c07b28fe855c3fc6f9a"})))}};It.style=Dt;const Lt=async e=>{let t=`${g.api.url}${g.api.endpoint.account.auth.signup}`;let n={method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)};let r;await fetch(t,n).then((e=>e.json())).then((e=>{r=e})).catch((e=>{console.log(e)}));return{success:r.success,message:r.message,payload:r.payload}};const Rt=(e,t,n)=>{let r={name:e.trim(),email:t.trim().toLowerCase(),password:n.trim()};return r};const Nt=_.object({name:_.string().required(),email:_.string().email({tlds:{allow:false}}).min(5).max(128).lowercase().trim().required(),password:_.string().min(8).max(1024).required()});const Pt=e=>{let{error:t}=Nt.validate(e);if(t){return{isValid:false,validationMessage:`❌ ${t.details[0].message}`}}else{return{isValid:true,validationMessage:""}}};const Tt=":host{display:flex;justify-content:space-around;align-items:center;height:100vh}.form{width:320px;border:0}e-input{display:block;margin-top:1em;margin-bottom:1em}e-link{display:inline-block}";const zt=Tt;const Ft=class{constructor(e){n(this,e);this.authSuccessfulEventEmitter=i(this,"authSuccessfulEvent",7);this.name="";this.email="";this.password="";this.isSigningUp=false}handleButtonClickEvent(e){if(e.detail.action==="signupUser"){this.signupUser()}}handleInputEvent(e){if(e.detail.name==="name"){this.name=e.detail.value}else if(e.detail.name==="email"){this.email=e.detail.value}else if(e.detail.name==="password"){this.password=e.detail.value}}async signupUser(){let e=Rt(this.name,this.email,this.password);let{isValid:t,validationMessage:n}=Pt(e);if(!t){return alert(n)}this.isSigningUp=true;let{success:r,message:s}=await Lt(e);this.isSigningUp=false;if(!r){return alert(s)}this.authSuccessfulEventEmitter.emit()}componentWillLoad(){v.activeView="signup";document.title="Signup | Sensefolks"}render(){return r(s,{key:"4b6bb32f88e06b628b82b3d4651f2e4aab9e20a7"},r("div",{key:"b6164b3a58b094f4bc9a6a30925b1f9c37b71b0f",class:"form"},r("e-image",{key:"cb586ade89594efd7b1975a6c7ab66ea1715aa3b",src:g.app.branding.logo.logotype.withoutBg,width:"180px"}),r("l-spacer",{key:"586a4fefb15b0dc6cf02c5860e4d36cc24ff8504",value:.75}),r("l-separator",{key:"7b398b20a779aa2ef3aa71384b786a13a696267c"}),r("l-spacer",{key:"067cc45e0fc625f27132ad578d01f47abaf29228",value:2}),r("e-text",{key:"0ed5e1e97d01b2f31f92b44a8f1f853c7b969106",variant:"display"},"Sign up"),r("l-spacer",{key:"12c5adcbde529c7c859db3bc214964ff050a490b",value:.5}),r("e-text",{key:"9f51c9ad8ba4443965cf78e0e999f02eca4994ed"},"Have an account? ",r("e-link",{key:"6bb5ede43eaf01254be5c7079773ee55855e18fd",url:"/login"},"Log In")),r("l-spacer",{key:"e107c5f2e14c12bbd780956b2c92095a5761e5e1",value:1.5}),r("e-button-oauth",{key:"8939b04dcfd8464655be14cf0f61b94a33771cdc"}),r("l-spacer",{key:"462440ceac5be43b3d98a2a8d0e5ddfe619a6dec",value:2}),r("l-separator",{key:"7b7a7dcf9675e450f8d915eb986b71113ce07718",variant:"oauth"}),r("l-spacer",{key:"781699e521ed9183f97f70f0609a8338110d5f7e",value:2}),r("e-input",{key:"03ebf42845958bf661074ea83085be02c684a346",type:"text",name:"name",placeholder:"Name"}),r("l-spacer",{key:"2f65ff784909965bd6232b28db7b8c7d4035bdfa",value:1.5}),r("e-input",{key:"cc0915b1035077f9ea919894039f9ae5e96a9a14",type:"email",name:"email",placeholder:"Email"}),r("l-spacer",{key:"726da837165f52c522473b04700db4b0e333a85e",value:1.5}),r("e-input",{key:"2c54b21a4deafb86d2a469e4e92158433fce0522",type:"password",name:"password",placeholder:"Password (Min. 8 letters)"}),r("l-spacer",{key:"819f32d120cc2f3c530629b5aafbb75ad664319b",value:1.5}),r("e-button",{key:"f95688cb0c65989b83edd15f9f0f619cb2496717",action:"signupUser",active:this.isSigningUp,size:"wide"},"Sign up"),r("l-spacer",{key:"63cdb57f4e44dd19cd5fa4bacbf85000071d26bd",value:1}),r("e-link",{key:"dbb27c1b2bb4bcb0df7be327db199b3f21cb6180",url:"/password-reset"},"Reset password"),r("l-spacer",{key:"4e50734752a4b3ccbf586b54b827009069dcec96",value:1.5}),r("l-separator",{key:"19a33debc13efcd29c424a5cbbfe2457fcf86b26"}),r("l-spacer",{key:"bcb3ee53cf086d564752dc0f668a4cde3724d70b",value:1.5}),r("e-text",{key:"2b88d1200a4c6f5287238cfca652e54a425a0901",variant:"footnote"},"By logging in, you accept our"," ",r("e-link",{key:"08c2c417132bc55213e3f3ab6b16dea0ddc353f9",variant:"externalLink",url:g.app.policy.tos.url},"terms of service")," ","&"," ",r("e-link",{key:"47bb1b007f8f155a81dd99b557accb41a383e137",variant:"externalLink",url:g.app.policy.privacy.url},"privacy policy"))))}};Ft.style=zt;const Wt=async()=>{let e=`${g.api.url}${g.api.endpoint.survey.getAll}`;let t={method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"};let n;await fetch(e,t).then((e=>e.json())).then((e=>{n=e})).catch((e=>{console.log(e)}));return{success:n.success,message:n.message,payload:n.payload}};const Ut=".centered{display:flex;justify-content:space-around;align-items:center;height:90vh;margin:0 auto}c-card{width:90%;max-width:400px}e-spinner{margin-top:10px}ol{box-sizing:border-box;padding-left:0;list-style-position:inside}.survey-list{list-style-type:none;list-style-position:inside;padding-left:0}.survey-list li{border:var(--border);padding:var(--padding) calc(var(--padding) * 1.25);background:white;border-radius:var(--border-radius);margin-bottom:2em;transition:all 0.25s}.survey-list li:hover{cursor:pointer;border:1px solid var(--color__grey--800);color:var(--color__grey--800)}.survey-list li:active{background:var(--color__grey--100)}.survey-list__response-date{display:flex;align-items:center;justify-content:space-between;width:200px}.pill{font-size:0.8em;border:1px solid black;padding:0 0.75em;border-radius:var(--border-radius);display:flex;justify-content:space-around}.pill--green{background:var(--color__green--50);color:var(--color__green--800);border:1px solid var(--color__green--800)}.pill--blue{background:#e0f2f1;color:#00695c;border:1px solid #00695c}.pill--pink{background:#fce4ec;color:#ad1457;border:1px solid #ad1457}.pill--orange{background:#fff3e0;color:#ef6c00;border:1px solid #ef6c00}.pill--purple{background:#f3e5f5;color:#6a1b9a;border:1px solid #6a1b9a}#fab{position:fixed;right:2em;bottom:2em}#ready-container{width:90%;max-width:640px;margin:0 auto}";const qt=Ut;const Vt=class{constructor(e){n(this,e);this.surveys=[];this.Fetching=()=>r("div",{class:"centered"},r("e-spinner",{theme:"dark"}));this.Empty=()=>r("c-card",null,r("e-text",{variant:"heading"},"Create & share surveys in less than 2 minutes"),r("l-spacer",{value:1.5}),r("l-separator",null),r("l-spacer",{value:2.5}),r("e-list",null,r("e-list-item",null,r("e-text",null,"STEP 1"),r("e-text",null,"Choose the type of survey")),r("l-spacer",{value:2}),r("e-list-item",null,r("e-text",null,"STEP 2"),r("e-text",null,"Customize the survey")),r("l-spacer",{value:2}),r("e-list-item",null,r("e-text",null,"STEP 3"),r("e-text",null,"Share the survey or embed it on your website"))),r("l-spacer",{value:2.5}),r("l-row",{justifyContent:"center"},r("e-button",{variant:"primary",action:"createSurvey"},"Create Survey")));this.Ready=()=>r("div",{id:"ready-container"},r("e-text",{variant:"heading"},"My Surveys"),r("l-spacer",{value:2}),r("ul",{class:"survey-list"},this.surveys.map((e=>r("li",{onClick:()=>this.handleSurveyClick(e.id)},r("l-row",null,this.generateSurveyPill(e.type),r("div",{class:"survey-list__response-date"},r("e-text",{variant:"footnote"},e.responseCount," responses"),r("e-text",{variant:"footnote"},e.timestamp))),r("l-spacer",{value:.5}),r("e-text",null,e.title),e.websiteUrl&&r("e-text",{variant:"footnote"},r("e-link",{variant:"externalLink",url:e.websiteUrl},e.websiteUrl)))))),r("div",{id:"fab"},r("e-button",{variant:"primary",action:"createSurvey"},"Create Survey")));this.Error=()=>r("c-card",null,r("e-text",{variant:"heading"},"Could not fetch your surveys :("),r("l-spacer",{value:1.5}),r("l-separator",null),r("l-spacer",{value:2.5}));this.compState=""}async handleButtonClickEvent(e){if(e.detail.action==="createSurvey"){v.isWizardActive=true;v.wizardType="createSurvey"}}async handleWizardCompletionEvent(){this.getAllSurveys()}componentWillLoad(){v.activeView="surveys";document.title="Surveys | Sensefolks";this.getAllSurveys()}async getAllSurveys(){if(this.compState!="fetching"){this.compState="fetching"}let{success:e,message:t,payload:n}=await Wt();if(!e){this.compState="error";return alert(t)}this.surveys=n;if(this.surveys.length>0){this.surveys=[...this.surveys];this.compState="ready"}else{this.compState="empty"}}generateSurveyPill(e){if(e==="sensePrice"){return r("span",{class:"pill pill--green"},"SensePrice")}else if(e==="senseFeature"){return r("span",{class:"pill pill--blue"},"SenseFeature")}else if(e==="senseLike"){return r("span",{class:"pill pill--pink"},"SenseLike")}else if(e==="senseEmotion"){return r("span",{class:"pill pill--orange"},"SenseEmotion")}else if(e==="senseQuery"){return r("span",{class:"pill pill--purple"},"SenseQuery")}}handleSurveyClick(e){console.log(e)}render(){return r("c-main",{key:"df1e0ebd3ea0ee55a91a21c1b7ea57f37e28cde2"},v.isWizardActive&&r("p-wizard",{key:"c523b59ca4230199ec74c4e324ab9da6569f7a09"}),this.compState==="fetching"&&r(this.Fetching,{key:"e32d9c8cbf8651d34bb9642ec057571d657e330f"}),this.compState==="empty"&&r(this.Empty,{key:"8a5ba844d95c8fc7b483924b1ef2d8e57eb2ad9c"}),this.compState==="ready"&&r(this.Ready,{key:"ab1157f1df97bf59a6d4012e51b02ddfcb6a8c25"}),this.compState==="error"&&r(this.Error,{key:"04b0444e16f73db2d80a5d7aba1d933bdd199131"}))}};Vt.style=qt;export{z as app_root,U as c_banner,B as c_card,Z as c_main,Q as e_button,ee as e_button_oauth,re as e_image,oe as e_input,le as e_link,fe as e_list,pe as e_list_item,ve as e_spinner,ke as e_text,je as e_text_editable,Ce as l_row,Ee as l_separator,Ie as l_spacer,Ne as p_topbar,ze as p_user_control,Ge as p_wizard,Ye as v_account,Ke as v_billing,tt as v_catch_all,st as v_checkout,ct as v_delete_account,pt as v_login,xt as v_password_reset,St as v_payment_failed,At as v_payment_success,It as v_post_oauth,Ft as v_signup,Vt as v_surveys};
//# sourceMappingURL=p-d018be56.entry.js.map