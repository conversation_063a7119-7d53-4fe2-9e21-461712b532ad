"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncludeRoutes = void 0;
const fs_1 = __importDefault(require("fs"));
const var_1 = require("../../var");
const IncludeRoutes = () => __awaiter(void 0, void 0, void 0, function* () {
    let componentPaths = [];
    let routeDirectoryPaths = [];
    let routeIndexPaths = [];
    let rootDirectory = "";
    let fileIndex = "";
    if (var_1.Var.node.env === "dev") {
        rootDirectory = "src";
        fileIndex = "index.ts";
    }
    else if (var_1.Var.node.env === "prod") {
        rootDirectory = ".";
        fileIndex = "index.js";
    }
    yield fs_1.default
        .readdirSync(`${rootDirectory}/components`)
        .map((componentDirectory) => {
        let componentDirectoryPath = `${rootDirectory}/components/${componentDirectory}`;
        new Promise((resolve, reject) => {
            let isDirectory = fs_1.default
                .lstatSync(componentDirectoryPath)
                .isDirectory();
            resolve();
            if (isDirectory)
                componentPaths.push(`components/${componentDirectory}`);
        });
    });
    yield componentPaths.forEach((componentPath) => {
        let isDirectoryEmpty = true;
        let isRouteDirectoryAvailable = false;
        let directoryLength = fs_1.default.readdirSync(`${rootDirectory}/` + componentPath).length;
        if (directoryLength > 0) {
            isDirectoryEmpty = false;
        }
        fs_1.default.readdirSync(`${rootDirectory}/` + componentPath).map((directoryName) => {
            if (directoryName === "routes")
                isRouteDirectoryAvailable = true;
        });
        if (!isDirectoryEmpty && isRouteDirectoryAvailable) {
            routeDirectoryPaths.push(componentPath + "/routes");
        }
    });
    yield routeDirectoryPaths.forEach((routeDirectoryPath) => {
        let routeIndexPath = "";
        routeIndexPath = `${routeDirectoryPath}/${fileIndex}`;
        routeIndexPaths.push(routeIndexPath);
    });
    return routeIndexPaths;
});
exports.IncludeRoutes = IncludeRoutes;
