"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Var = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
exports.Var = {
    app: {
        name: process.env.APP_NAME,
        contact: {
            email: process.env.APP_EMAIL,
        },
        domain: process.env.APP_DOMAIN,
        emoji: {
            success: "✅",
            failure: "❌",
            warning: "⚠️",
        },
        url: {
            dev: process.env.APP_URL_DEV,
            prod: process.env.APP_URL_PROD,
        },
        website: {
            url: process.env.APP_WEBSITE_URL,
        },
        owner: {
            name: process.env.OWNER_NAME,
            website: {
                url: process.env.OWNER_WEBSITE_URL,
            },
            contact: {
                address: process.env.OWNER_ADDRESS,
                email: process.env.OWNER_EMAIL,
            },
        },
    },
    redis: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
    },
    postgres: {
        host: process.env.POSTGRES_HOST,
        database: process.env.POSTGRES_DATABASE,
        user: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
    },
    postmark: {
        token: process.env.POSTMARK_TOKEN,
        template: {
            accountChangeConfirmation: {
                id: parseInt(process.env.POSTMARK_TEMPLATE_ACCOUNT_CHANGE_CONFIRMATION),
            },
            emailVerificationCode: {
                id: parseInt(process.env.POSTMARK_TEMPLATE_EMAIL_VERIFICATION_CODE),
            },
            passwordResetCode: {
                id: parseInt(process.env.POSTMARK_TEMPLATE_PASSWORD_RESET_CODE),
            },
        },
    },
    node: {
        env: process.env.NODE_ENV,
        port: Number(process.env.NODE_PORT),
        express: {
            session: {
                secret: process.env.EXPRESS_SESSION_SECRET,
                name: process.env.EXPRESS_SESSION_NAME,
                maxAge: process.env.EXPRESS_SESSION_TIMEOUT,
            },
        },
    },
    stripe: {
        key: {
            public: process.env.STRIPE_PUBLIC_KEY,
            secret: process.env.STRIPE_SECRET_KEY,
        },
        webhook: {
            secret: process.env.STRIPE_WEBHOOK_SECRET,
        },
        fee: {
            processing: {
                percentage: Number(process.env.STRIPE_PROCESSING_FEE),
            },
        },
    },
};
