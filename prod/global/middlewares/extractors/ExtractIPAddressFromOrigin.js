"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtractIPAddressFromOrigin = void 0;
const var_1 = require("../../var");
const ExtractIPAddressFromOrigin = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    let ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress;
    if (!ip) {
        console.log(`${var_1.Var.app.emoji.failure} Invalid IP`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid IP`,
        });
    }
    res.locals.clientIPAddress = ip;
    console.log(`${var_1.Var.app.emoji.success} Origin IP: ${res.locals.clientIPAddress}`);
    next();
});
exports.ExtractIPAddressFromOrigin = ExtractIPAddressFromOrigin;
