"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtractCountryFromIPAddress = void 0;
const geoip_lite_1 = __importDefault(require("geoip-lite"));
const var_1 = require("../../var");
const ExtractCountryFromIPAddress = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    let originCountry = geoip_lite_1.default.lookup(res.locals.clientIPAddress);
    if (!originCountry) {
        console.log(`${var_1.Var.app.emoji.warning} Unable to ascertain origin country. Defaulting to 'IN'`);
        originCountry = "IN";
    }
    res.locals.originCountry = originCountry;
    console.log(`${var_1.Var.app.emoji.success} Origin country: ${res.locals.originCountry}`);
    next();
});
exports.ExtractCountryFromIPAddress = ExtractCountryFromIPAddress;
