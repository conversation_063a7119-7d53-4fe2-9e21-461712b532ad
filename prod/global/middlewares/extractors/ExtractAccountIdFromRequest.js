"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtractAccountIdFromRequest = void 0;
const var_1 = require("../../var");
const ExtractAccountIdFromRequest = (req, res, next) => {
    let accountId = req.session.accountId;
    if (!accountId) {
        console.log(`${var_1.Var.app.emoji.failure} Account does not exist`);
        return res.status(200).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Account does not exist`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} Account exists`);
    res.locals.accountId = accountId;
    next();
};
exports.ExtractAccountIdFromRequest = ExtractAccountIdFromRequest;
