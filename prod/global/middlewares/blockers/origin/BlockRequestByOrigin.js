"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockRequestByOrigin = void 0;
const var_1 = require("../../../../global/var");
const BlockRequestByOrigin = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    if (var_1.Var.node.env === "prod") {
        if (res.locals.origin != var_1.Var.app.url.prod) {
            console.log(`${var_1.Var.app.emoji.failure} ${res.locals.origin} is not an authorized origin`);
            return res.status(200).json({
                success: false,
                message: `${var_1.Var.app.emoji.failure} You are not authorized to make this request`,
            });
        }
    }
    console.log(`${var_1.Var.app.emoji.success} ${res.locals.origin} is authorized origin`);
    next();
});
exports.BlockRequestByOrigin = BlockRequestByOrigin;
