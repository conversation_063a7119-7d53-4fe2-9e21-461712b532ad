"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockLoggedOutAccount = void 0;
const var_1 = require("../../../var");
const BlockLoggedOutAccount = (req, res, next) => {
    let isUserLoggedIn = !!req.session.accountId;
    if (!isUserLoggedIn) {
        console.log(`${var_1.Var.app.emoji.failure} ${req.session.accountId} is not logged in`);
        return res.status(200).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} You are not logged in`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} ${req.session.accountId} is logged in`);
    next();
};
exports.BlockLoggedOutAccount = BlockLoggedOutAccount;
