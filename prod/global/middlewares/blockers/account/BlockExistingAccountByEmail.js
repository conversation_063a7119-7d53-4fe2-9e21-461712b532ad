"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockExistingAccountByEmail = void 0;
const dals_1 = require("../../../../components/account/dals");
const var_1 = require("../../../var");
const BlockExistingAccountByEmail = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountByEmail)(res.locals.email);
    if (account) {
        console.log(`${var_1.Var.app.emoji.failure} ${res.locals.email} Account already exists`);
        return res.status(200).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Account already exists`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} account with ${res.locals.email} does not exist`);
    next();
});
exports.BlockExistingAccountByEmail = BlockExistingAccountByEmail;
