"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlockLoggedInAccount = void 0;
const var_1 = require("../../../var");
const BlockLoggedInAccount = (req, res, next) => {
    let isUserLoggedIn = !!req.session.accountId;
    if (isUserLoggedIn) {
        console.log(`${var_1.Var.app.emoji.failure} ${req.session.accountId} is already logged in`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} You are already logged in`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} User is not logged in`);
    next();
};
exports.BlockLoggedInAccount = BlockLoggedInAccount;
