{"name": "sensefolks_api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon", "build": "tsc"}, "repository": {"type": "git", "url": "**************-sensefolks:sensefolks/webapp-api__dev.git"}, "author": "<PERSON><PERSON> (Projckt)", "license": "ISC", "dependencies": {"argon2": "^0.30.3", "axios": "^1.4.0", "cloudinary": "^1.35.0", "connect-redis": "^6.1.3", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "geoip-lite": "^1.4.7", "ioredis": "^5.3.1", "joi": "^17.7.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.2.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.11", "pdf-lib": "^1.17.1", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "postmark": "^3.0.15", "request-country": "^0.1.6", "sequelize": "^6.28.0", "socket.io": "^4.6.2", "streamifier": "^0.1.1", "stripe": "^12.5.0", "ts-node": "^10.9.1", "typescript": "^4.9.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/connect-redis": "^0.0.20", "@types/cors": "^2.8.13", "@types/express": "^4.17.15", "@types/express-session": "^1.17.6", "@types/geoip-lite": "^1.4.1", "@types/jsonwebtoken": "^9.0.3", "@types/luxon": "^3.2.0", "@types/multer": "^1.4.7", "@types/node": "^18.11.18", "@types/streamifier": "^0.1.0", "@types/uuid": "^9.0.0"}}