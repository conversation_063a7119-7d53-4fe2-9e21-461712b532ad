"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.visitModel = void 0;
const sequelize_1 = require("sequelize");
const var_1 = require("../../../global/var");
const modelName = "visit";
const modelAttributes = {
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        allowNull: false,
        unique: true,
        primaryKey: true,
    },
    email: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: { isEmail: true },
    },
    socket_id: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    is_active: { type: sequelize_1.DataTypes.BOOLEAN, defaultValue: true },
    ip_address: { type: sequelize_1.DataTypes.STRING },
};
const modelOptions = {};
exports.visitModel = var_1.Sequelize.define(modelName, modelAttributes, modelOptions);
