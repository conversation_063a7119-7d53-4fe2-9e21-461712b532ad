"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeVisitStatus = exports.writeNewVisit = void 0;
// Write
var writeNewVisit_1 = require("./write/writeNewVisit");
Object.defineProperty(exports, "writeNewVisit", { enumerable: true, get: function () { return writeNewVisit_1.writeNewVisit; } });
var writeVisitStatus_1 = require("./write/writeVisitStatus");
Object.defineProperty(exports, "writeVisitStatus", { enumerable: true, get: function () { return writeVisitStatus_1.writeVisitStatus; } });
