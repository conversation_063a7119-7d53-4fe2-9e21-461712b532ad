"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeVisitStatus = void 0;
const var_1 = require("../../../../global/var");
const models_1 = require("../../models");
const writeVisitStatus = (socketId) => __awaiter(void 0, void 0, void 0, function* () {
    yield models_1.visitModel
        .update({ is_active: false }, {
        where: {
            socket_id: socketId,
        },
    })
        .then(() => {
        console.log(`${var_1.Var.app.emoji.success} Visit status updated: ${socketId}`);
    })
        .catch((err) => {
        console.log(`${var_1.Var.app.emoji.failure} Visit status not updated: ${socketId}`);
        console.log(err);
    });
});
exports.writeVisitStatus = writeVisitStatus;
