"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.purchaseModel = void 0;
const sequelize_1 = require("sequelize");
const var_1 = require("../../../global/var");
const modelName = "purchase";
const modelAttributes = {
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        allowNull: false,
        unique: true,
        primaryKey: true,
    },
    productId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    sessionId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    currency: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    amount: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
    },
    isSuccess: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
};
const modelOptions = {};
exports.purchaseModel = var_1.Sequelize.define(modelName, modelAttributes, modelOptions);
