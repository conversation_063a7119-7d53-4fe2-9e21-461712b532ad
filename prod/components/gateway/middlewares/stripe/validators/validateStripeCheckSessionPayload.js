"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateStripeCheckSessionPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../../global/var");
const stripeCheckSessionPayloadSchema = joi_1.default.object({
    sessionId: joi_1.default.string().required(),
});
const validateStripeCheckSessionPayload = (req, res, next) => {
    let { error } = stripeCheckSessionPayloadSchema.validate(req.body);
    if (error) {
        console.log(`${var_1.Var.app.emoji.failure} Stripe check session payload not valid`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} Stripe check session payload valid`);
    next();
};
exports.validateStripeCheckSessionPayload = validateStripeCheckSessionPayload;
