"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateStripeCreateSessionPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../../global/var");
const stripeCreateSessionPayloadSchema = joi_1.default.object({
    documentId: joi_1.default.string().required(),
});
const validateStripeCreateSessionPayload = (req, res, next) => {
    let { error } = stripeCreateSessionPayloadSchema.validate(req.body);
    if (error) {
        console.log(`${var_1.Var.app.emoji.failure} Stripe checkout session payload not valid`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} Stripe checkout session creation payload valid`);
    next();
};
exports.validateStripeCreateSessionPayload = validateStripeCreateSessionPayload;
