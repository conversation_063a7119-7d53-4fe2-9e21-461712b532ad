"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeCheckSessionController = void 0;
const stripe_1 = __importDefault(require("stripe"));
const dals_1 = require("../../../purchase/dals");
const var_1 = require("../../../../global/var");
const stripeCheckSessionController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const stripe = new stripe_1.default(var_1.Var.stripe.key.secret, {
        apiVersion: "2022-11-15",
    });
    yield stripe.checkout.sessions
        .retrieve(res.locals.sessionId)
        .then((session) => __awaiter(void 0, void 0, void 0, function* () {
        if (session.payment_status != "paid") {
            return res.status(400).json({
                success: false,
                message: `${var_1.Var.app.emoji.failure} Payment unsuccessful`,
                payload: {},
            });
        }
        const updatedPurchase = yield (0, dals_1.writePurchaseStatus)(res.locals.sessionId);
        if (!updatedPurchase.success) {
            return res.status(400).json({
                success: false,
                message: `${var_1.Var.app.emoji.failure} Payment status update failed`,
                payload: {},
            });
        }
        return res.status(200).json({
            success: true,
            message: `${var_1.Var.app.emoji.success} Payment successful`,
        });
    }))
        .catch((err) => {
        console.log(err);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not check session details`,
        });
    });
});
exports.stripeCheckSessionController = stripeCheckSessionController;
