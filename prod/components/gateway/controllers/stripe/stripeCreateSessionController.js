"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeCreateSessionController = void 0;
const stripe_1 = __importDefault(require("stripe"));
const dals_1 = require("../../../purchase/dals");
const var_1 = require("../../../../global/var");
const stripeCreateSessionController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let tierId = "";
    let priceId = "";
    const stripe = new stripe_1.default(var_1.Var.stripe.key.secret, {
        apiVersion: "2022-11-15",
    });
    const session = yield stripe.checkout.sessions.create({
        success_url: `${res.locals.origin}/payment-handle/{CHECKOUT_sessionId}`,
        cancel_url: `${res.locals.origin}/payment-cancel/{CHECKOUT_sessionId}`,
        line_items: [{ price: priceId, quantity: 1 }],
        mode: "payment",
        currency: "USD",
    });
    if (!session.id) {
        console.log(`${var_1.Var.app.emoji.failure} Could not create Stripe checkout session`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not create Stripe checkout session`,
        });
    }
    const newPurchase = yield (0, dals_1.writeNewPurchase)(tierId, session.id, session.currency, session.amount / 100, res.locals.accountId);
    if (!newPurchase.success) {
        console.log(`${var_1.Var.app.emoji.failure} Could not save purchase`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not save purchase`,
        });
    }
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Stripe checkout session created`,
        payload: session.id,
    });
});
exports.stripeCreateSessionController = stripeCreateSessionController;
