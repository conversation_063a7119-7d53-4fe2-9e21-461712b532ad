"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeCheckSessionRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.stripeCheckSessionRoute = (0, express_1.Router)();
exports.stripeCheckSessionRoute.post("/stripe-check-session", middlewares_1.BlockLoggedOutAccount, middlewares_1.ExtractAccountIdFromRequest, middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockNonExistentAccountById, middlewares_2.validateStripeCheckSessionPayload, middlewares_2.formatStripeCheckSessionPayload, controllers_1.stripeCheckSessionController);
