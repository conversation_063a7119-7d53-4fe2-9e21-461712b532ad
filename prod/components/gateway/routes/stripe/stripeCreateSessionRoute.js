"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeCreateSessionRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.stripeCreateSessionRoute = (0, express_1.Router)();
exports.stripeCreateSessionRoute.post("/stripe-create-session", middlewares_1.BlockLoggedOutAccount, middlewares_1.ExtractAccountIdFromRequest, middlewares_1.BlockNonExistentAccountById, middlewares_1.ExtractOriginFromRequest, middlewares_2.validateStripeCreateSessionPayload, middlewares_2.formatStripeCreateSessionPayload, controllers_1.stripeCreateSessionController);
