"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteAccountController = void 0;
const dals_1 = require("../../dals");
const var_1 = require("../../../../global/var");
const deleteAccountController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = res.locals.account;
    let deletedAccount = yield (0, dals_1.writeDeletedAccount)(account.id, account.name, account.email, account.is_email_verified, account.is_google_oauth_linked, account.createdAt.toString());
    console.log(deletedAccount.message);
    if (!deletedAccount.success) {
        return res.status(400).json({
            success: false,
            message: deletedAccount.message,
        });
    }
    let accountDeletionReturnData = yield (0, dals_1.writeAccountDeletion)(account.id);
    if (!accountDeletionReturnData.success) {
        return res.status(400).json({
            success: false,
            message: accountDeletionReturnData.message,
        });
    }
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Account deleted`,
        payload: {},
    });
});
exports.deleteAccountController = deleteAccountController;
