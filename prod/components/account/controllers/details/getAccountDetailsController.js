"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAccountDetailsController = void 0;
const var_1 = require("../../../../global/var");
const getAccountDetailsController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = res.locals.account;
    let responseData = {
        name: account.name,
        email: account.email,
        isEmailVerified: account.is_email_verified,
        isSessionActive: true,
    };
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Account details fetched`,
        payload: responseData,
    });
});
exports.getAccountDetailsController = getAccountDetailsController;
