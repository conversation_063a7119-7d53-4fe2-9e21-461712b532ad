"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkSessionController = void 0;
const dals_1 = require("../../dals");
const var_1 = require("../../../../global/var");
const checkSessionController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountById)(res.locals.accountId);
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Account deleted`,
        payload: {},
    });
});
exports.checkSessionController = checkSessionController;
