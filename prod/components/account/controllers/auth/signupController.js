"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.signupController = void 0;
const dals_1 = require("../../dals");
const helpers_1 = require("../../helpers");
const helpers_2 = require("../../../../global/helpers");
const var_1 = require("../../../../global/var");
const signupController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let { name, email, password } = res.locals;
    let emailVerificationCode = yield (0, helpers_2.Generate4DigitCode)();
    let hashedPassword = yield (0, helpers_1.hashPassword)(password);
    let newAccount = yield (0, dals_1.writeNewAccount)(name, email, hashedPassword, emailVerificationCode);
    if (!newAccount.success) {
        return res.status(400).json({
            success: false,
            message: newAccount.message,
        });
    }
    console.log(newAccount.message);
    (0, helpers_1.login)(req, res, newAccount.payload.id);
    let mailEmailVerificationCodeReturnData = yield (0, helpers_1.mailEmailVerificationCode)(newAccount.payload.name.split(" ")[0], newAccount.payload.email, emailVerificationCode);
    if (!mailEmailVerificationCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: mailEmailVerificationCodeReturnData.message,
        });
    }
    console.log(mailEmailVerificationCodeReturnData.message);
    let responseData = {
        name: newAccount.payload.name,
        email: newAccount.payload.email,
        isEmailVerified: newAccount.payload.isEmailVerified,
        isSessionActive: true,
    };
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} User signed up`,
        payload: responseData,
    });
});
exports.signupController = signupController;
