"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginController = void 0;
const helpers_1 = require("../../helpers");
const dals_1 = require("../../dals");
const helpers_2 = require("../../helpers");
const var_1 = require("../../../../global/var");
const loginController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let { email, password } = res.locals;
    let account = yield (0, dals_1.readAccountByEmail)(email);
    if (!account) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Account not found`,
        });
    }
    if (!account.dataValues.password) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid password`,
        });
    }
    let isPasswordValid = yield (0, helpers_2.verifyPasswordHash)(account === null || account === void 0 ? void 0 : account.dataValues.password, password);
    if (!isPasswordValid) {
        console.log(`${var_1.Var.app.emoji.failure} ${email} password is not valid`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid password`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} ${email} password is valid`);
    res.locals.accountId = account === null || account === void 0 ? void 0 : account.dataValues.id;
    (0, helpers_1.login)(req, res, res.locals.accountId);
    let responseData = {
        name: account.name,
        email: account.email,
        isEmailVerified: account.is_email_verified,
        isSessionActive: true,
    };
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Logged in`,
        payload: responseData,
    });
});
exports.loginController = loginController;
