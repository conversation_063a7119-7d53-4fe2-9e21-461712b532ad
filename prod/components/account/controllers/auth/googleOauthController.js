"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleOauthController = void 0;
const helpers_1 = require("../../helpers");
const dals_1 = require("../../dals");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const var_1 = require("../../../../global/var");
const googleOauthController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    if (!res.locals.token) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid token`,
        });
    }
    let decodedToken = jsonwebtoken_1.default.decode(res.locals.token);
    let email = decodedToken.email;
    let isEmailVerified = decodedToken.email_verified;
    let givenName = decodedToken.given_name;
    let familyName = decodedToken.family_name;
    if (!email || !isEmailVerified || !givenName || !familyName) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid Google account details`,
        });
    }
    // Get account
    let account = yield (0, dals_1.readAccountByEmail)(email);
    let accountId = "";
    let name = `${givenName} ${familyName}`;
    if (!account) {
        let newAccount = yield (0, dals_1.writeNewAccountFromGoogleOauth)(name, email, true, true);
        console.log(newAccount.message);
        if (!newAccount.payload.id) {
            return res.status(400).json({
                success: false,
                message: newAccount.message,
            });
        }
        accountId = newAccount.payload.id;
    }
    else {
        if (!account.dataValues.is_google_oauth_linked) {
            let updatedAccountReturnData = yield (0, dals_1.writeGoogleOauthStatus)(email);
            if (!updatedAccountReturnData.success) {
                return res.status(400).json({
                    success: false,
                    message: updatedAccountReturnData.message,
                });
            }
        }
        accountId = account.dataValues.id;
    }
    (0, helpers_1.login)(req, res, accountId);
    let responseData = {
        name: name,
        email: email,
        isEmailVerified: true,
        isSessionActive: true,
    };
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Logged in`,
        payload: responseData,
    });
});
exports.googleOauthController = googleOauthController;
