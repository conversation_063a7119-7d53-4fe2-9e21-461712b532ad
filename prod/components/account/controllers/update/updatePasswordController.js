"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePasswordController = void 0;
const dals_1 = require("../../dals");
const helpers_1 = require("../../helpers");
const var_1 = require("../../../../global/var");
const luxon_1 = require("luxon");
const updatePasswordController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountByEmail)(res.locals.email);
    if (account.dataValues.password_reset_code != res.locals.passwordResetCode) {
        console.log(`${res.locals.passwordResetCode} is not a valid verification code for ${res.locals.email}`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid password reset code`,
        });
    }
    let timeNow = luxon_1.DateTime.now();
    let passwordResetCodeExpiry = luxon_1.DateTime.fromJSDate(account.password_reset_code_timestamp);
    let diff = timeNow
        .diff(passwordResetCodeExpiry, "minutes")
        .toObject().minutes;
    if (diff > 30) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Password reset code has expired. Kindly re-generate it`,
        });
    }
    let newHashedPassword = yield (0, helpers_1.hashPassword)(res.locals.newPassword);
    let newPasswordReturnData = yield (0, dals_1.writeNewPassword)(res.locals.email, newHashedPassword);
    console.log(newPasswordReturnData.message);
    if (!newPasswordReturnData.success) {
        console.log(`${var_1.Var.app.emoji.failure} Password reset failed`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Failed to reset password`,
        });
    }
    let mailAccountChangeConfirmationReturnData = yield (0, helpers_1.mailAccountChangeConfirmation)(res.locals.email, "password", account.dataValues.name.split(" ")[0]);
    console.log(mailAccountChangeConfirmationReturnData.message);
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Password reset successful`,
    });
});
exports.updatePasswordController = updatePasswordController;
