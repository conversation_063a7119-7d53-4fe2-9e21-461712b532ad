"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateAccountController = void 0;
const dals_1 = require("../../dals");
const helpers_1 = require("../../helpers");
const helpers_2 = require("../../../../global/helpers");
const var_1 = require("../../../../global/var");
const helpers_3 = require("../../helpers");
const updateAccountController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = res.locals.account;
    let email = account.dataValues.email;
    let accountUpdateReturnData;
    if (res.locals.attribute === "name") {
        accountUpdateReturnData = yield (0, dals_1.writeNewName)(email, res.locals.value);
    }
    else if (res.locals.attribute === "email") {
        let emailVerificationCode = yield (0, helpers_2.Generate4DigitCode)();
        accountUpdateReturnData = yield (0, dals_1.writeNewEmail)(email, res.locals.value, emailVerificationCode);
        let mailEmailVerificationCodeReturnData = yield (0, helpers_3.mailEmailVerificationCode)(account.name.split(" ")[0], res.locals.value, emailVerificationCode);
        if (!mailEmailVerificationCodeReturnData.success) {
            return res.status(400).json({
                success: false,
                message: mailEmailVerificationCodeReturnData.message,
            });
        }
        console.log(mailEmailVerificationCodeReturnData.message);
    }
    else if (res.locals.attribute === "password") {
        let newHashedPassword = yield (0, helpers_1.hashPassword)(res.locals.value);
        accountUpdateReturnData = yield (0, dals_1.writeNewPassword)(email, newHashedPassword);
    }
    console.log(accountUpdateReturnData.message);
    if (!accountUpdateReturnData.success) {
        console.log(`${var_1.Var.app.emoji.failure} Failed to update ${res.locals.attribute}`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Failed to update ${res.locals.attribute}`,
        });
    }
    let mailName = "";
    if (res.locals.attribute === "name") {
        mailName = res.locals.value.split(" ")[0];
    }
    else {
        mailName = account.dataValues.name.split(" ")[0];
    }
    let mailAccountChangeConfirmationReturnData = (0, helpers_1.mailAccountChangeConfirmation)(email, res.locals.attribute, mailName);
    console.log(mailAccountChangeConfirmationReturnData.message);
    return res.status(200).json({
        success: accountUpdateReturnData.success,
        message: accountUpdateReturnData.message,
        payload: {},
    });
});
exports.updateAccountController = updateAccountController;
