"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyEmailController = void 0;
const dals_1 = require("../../dals");
const var_1 = require("../../../../global/var");
const luxon_1 = require("luxon");
const verifyEmailController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountByEmail)(res.locals.email);
    if (!account) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not verify your email`,
        });
    }
    let emailVerificationCode = account.dataValues.email_verification_code;
    if (!emailVerificationCode) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid verification code`,
        });
    }
    if (emailVerificationCode != res.locals.emailVerificationCode) {
        console.log(`${res.locals.emailVerificationCode} is not a valid email verification code for ${res.locals.email}`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Invalid email verification code`,
        });
    }
    let timeNow = luxon_1.DateTime.now();
    let emailVerificationCodeExpiry = luxon_1.DateTime.fromJSDate(account.email_verification_code_timestamp);
    let diff = timeNow
        .diff(emailVerificationCodeExpiry, "minutes")
        .toObject().minutes;
    if (diff > 30) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Email verification code has expired. Kindly re-generate it`,
        });
    }
    if (!account.dataValues.is_email_verified) {
        let writeEmailVerificationStatusReturnData = yield (0, dals_1.writeEmailVerificationStatus)(account.email, true);
        console.log(writeEmailVerificationStatusReturnData.message);
        if (!writeEmailVerificationStatusReturnData.success) {
            return res.status(400).json({
                success: false,
                message: `${var_1.Var.app.emoji.failure} Could not verify email`,
            });
        }
        console.log(`${var_1.Var.app.emoji.success} Email verified`);
    }
    let writeVerificationCodeReturnData = yield (0, dals_1.writeEmailVerificationCode)(account.email, "", false);
    console.log(writeVerificationCodeReturnData.message);
    if (!writeVerificationCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not verify email`,
        });
    }
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Email verified`,
        payload: {
            email: account.email,
        },
    });
});
exports.verifyEmailController = verifyEmailController;
