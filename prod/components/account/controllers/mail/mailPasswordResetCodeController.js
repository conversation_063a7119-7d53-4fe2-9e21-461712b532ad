"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailPasswordResetCodeController = void 0;
const dals_1 = require("../../dals");
const helpers_1 = require("../../helpers");
const helpers_2 = require("../../../../global/helpers");
const var_1 = require("../../../../global/var");
const mailPasswordResetCodeController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountByEmail)(res.locals.email);
    if (!account) {
        return res.status(200).json({
            success: false,
            message: `${var_1.Var.app.emoji.success} If you have an account with us, you will receive the password reset code in the registered email`,
        });
    }
    let passwordResetCode = yield (0, helpers_2.Generate4DigitCode)();
    let writePasswordResetCodeReturnData = yield (0, dals_1.writePasswordResetCode)(account.email, passwordResetCode);
    console.log(writePasswordResetCodeReturnData.message);
    if (!writePasswordResetCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not save password reset code`,
        });
    }
    let mailPasswordResetCodeReturnData = yield (0, helpers_1.mailPasswordResetCode)(account.name.split(" ")[0], account.email, passwordResetCode);
    if (!mailPasswordResetCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: mailPasswordResetCodeReturnData.message,
        });
    }
    console.log(mailPasswordResetCodeReturnData.message);
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Password reset code sent`,
        payload: {},
    });
});
exports.mailPasswordResetCodeController = mailPasswordResetCodeController;
