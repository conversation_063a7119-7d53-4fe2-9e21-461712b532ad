"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailEmailVerificationCodeController = void 0;
const dals_1 = require("../../dals");
const helpers_1 = require("../../helpers");
const helpers_2 = require("../../../../global/helpers");
const var_1 = require("../../../../global/var");
const mailEmailVerificationCodeController = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    let account = yield (0, dals_1.readAccountByEmail)(res.locals.email);
    if (!account) {
        return res.status(200).json({
            success: false,
            message: `${var_1.Var.app.emoji.success} If you have an account with us, you will receive the password reset code in the registered email`,
        });
    }
    let emailVerificationCode = yield (0, helpers_2.Generate4DigitCode)();
    let writeEmailVerificationCodeReturnData = yield (0, dals_1.writeEmailVerificationCode)(account.email, emailVerificationCode, true);
    console.log(writeEmailVerificationCodeReturnData.message);
    if (!writeEmailVerificationCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} Could not save email verification code`,
        });
    }
    let mailEmailVerificationCodeReturnData = yield (0, helpers_1.mailEmailVerificationCode)(account.name.split(" ")[0], account.email, emailVerificationCode);
    if (!mailEmailVerificationCodeReturnData.success) {
        return res.status(400).json({
            success: false,
            message: mailEmailVerificationCodeReturnData.message,
        });
    }
    console.log(mailEmailVerificationCodeReturnData.message);
    return res.status(200).json({
        success: true,
        message: `${var_1.Var.app.emoji.success} Email verification code sent`,
        payload: {},
    });
});
exports.mailEmailVerificationCodeController = mailEmailVerificationCodeController;
