"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyEmailController = exports.updateAccountController = exports.googleOauthController = exports.mailPasswordResetCodeController = exports.mailEmailVerificationCodeController = exports.getAccountDetailsController = exports.deleteAccountController = exports.updatePasswordController = exports.logoutController = exports.loginController = exports.signupController = void 0;
// Auth
var signupController_1 = require("./auth/signupController");
Object.defineProperty(exports, "signupController", { enumerable: true, get: function () { return signupController_1.signupController; } });
var loginController_1 = require("./auth/loginController");
Object.defineProperty(exports, "loginController", { enumerable: true, get: function () { return loginController_1.loginController; } });
var logoutController_1 = require("./auth/logoutController");
Object.defineProperty(exports, "logoutController", { enumerable: true, get: function () { return logoutController_1.logoutController; } });
// Confirm
var updatePasswordController_1 = require("./update/updatePasswordController");
Object.defineProperty(exports, "updatePasswordController", { enumerable: true, get: function () { return updatePasswordController_1.updatePasswordController; } });
// Delete
var deleteAccountController_1 = require("./delete/deleteAccountController");
Object.defineProperty(exports, "deleteAccountController", { enumerable: true, get: function () { return deleteAccountController_1.deleteAccountController; } });
// Details
var getAccountDetailsController_1 = require("./details/getAccountDetailsController");
Object.defineProperty(exports, "getAccountDetailsController", { enumerable: true, get: function () { return getAccountDetailsController_1.getAccountDetailsController; } });
// Mail
var mailEmailVerificationCodeController_1 = require("./mail/mailEmailVerificationCodeController");
Object.defineProperty(exports, "mailEmailVerificationCodeController", { enumerable: true, get: function () { return mailEmailVerificationCodeController_1.mailEmailVerificationCodeController; } });
var mailPasswordResetCodeController_1 = require("./mail/mailPasswordResetCodeController");
Object.defineProperty(exports, "mailPasswordResetCodeController", { enumerable: true, get: function () { return mailPasswordResetCodeController_1.mailPasswordResetCodeController; } });
// OAuth
var googleOauthController_1 = require("./auth/googleOauthController");
Object.defineProperty(exports, "googleOauthController", { enumerable: true, get: function () { return googleOauthController_1.googleOauthController; } });
// Update
var updateAccountController_1 = require("./update/updateAccountController");
Object.defineProperty(exports, "updateAccountController", { enumerable: true, get: function () { return updateAccountController_1.updateAccountController; } });
// Verify
var verifyEmailController_1 = require("./verify/verifyEmailController");
Object.defineProperty(exports, "verifyEmailController", { enumerable: true, get: function () { return verifyEmailController_1.verifyEmailController; } });
