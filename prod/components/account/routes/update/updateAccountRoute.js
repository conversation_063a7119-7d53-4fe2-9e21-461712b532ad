"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateAccountRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.updateAccountRoute = (0, express_1.Router)();
exports.updateAccountRoute.put("/account", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_1.BlockLoggedOutAccount, middlewares_1.ExtractAccountIdFromRequest, middlewares_1.BlockNonExistentAccountById, middlewares_2.validateAccountUpdatePayload, middlewares_2.formatAccountUpdatePayload, controllers_1.updateAccountController);
