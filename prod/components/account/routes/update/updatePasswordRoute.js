"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePasswordRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.updatePasswordRoute = (0, express_1.Router)();
exports.updatePasswordRoute.put("/password", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_2.validatePasswordResetPayload, middlewares_2.formatPasswordResetPayload, middlewares_1.BlockNonExistentAccountByEmail, controllers_1.updatePasswordController);
