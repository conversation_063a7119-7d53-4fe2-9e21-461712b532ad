"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteAccountRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const controllers_1 = require("../../controllers");
exports.deleteAccountRoute = (0, express_1.Router)();
exports.deleteAccountRoute.delete("/account", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_1.BlockLoggedOutAccount, middlewares_1.ExtractAccountIdFromRequest, middlewares_1.BlockNonExistentAccountById, controllers_1.deleteAccountController);
