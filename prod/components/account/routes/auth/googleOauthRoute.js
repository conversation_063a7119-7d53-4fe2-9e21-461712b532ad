"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleOauthRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const controllers_1 = require("../../controllers");
const middlewares_2 = require("../../middlewares");
exports.googleOauthRoute = (0, express_1.Router)();
exports.googleOauthRoute.post("/google-oauth", middlewares_1.BlockLoggedInAccount, middlewares_2.validateGoogleOauthPayload, middlewares_2.formatGoogleOauthPayload, controllers_1.googleOauthController);
