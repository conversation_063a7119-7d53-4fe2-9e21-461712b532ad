"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.signupRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.signupRoute = (0, express_1.Router)();
exports.signupRoute.post("/signup", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_1.BlockLoggedInAccount, middlewares_2.validateSignupPayload, middlewares_2.formatSignupPayload, middlewares_1.BlockExistingAccountByEmail, controllers_1.signupController);
