"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loginRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.loginRoute = (0, express_1.Router)();
exports.loginRoute.post("/login", middlewares_1.BlockLoggedInAccount, middlewares_2.validateLoginPayload, middlewares_2.formatLoginPayload, middlewares_1.BlockNonExistentAccountByEmail, controllers_1.loginController);
