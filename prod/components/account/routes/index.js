"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyEmailRoute = exports.updatePasswordRoute = exports.updateAccountRoute = exports.googleOauthRoute = exports.mailPasswordResetCodeRoute = exports.mailEmailVerificationCodeRoute = exports.getAccountDetailsRoute = exports.deleteAccountRoute = exports.signupRoute = exports.logoutRoute = exports.loginRoute = void 0;
// Auth
var loginRoute_1 = require("./auth/loginRoute");
Object.defineProperty(exports, "loginRoute", { enumerable: true, get: function () { return loginRoute_1.loginRoute; } });
var logoutRoute_1 = require("./auth/logoutRoute");
Object.defineProperty(exports, "logoutRoute", { enumerable: true, get: function () { return logoutRoute_1.logoutRoute; } });
var signupRoute_1 = require("./auth/signupRoute");
Object.defineProperty(exports, "signupRoute", { enumerable: true, get: function () { return signupRoute_1.signupRoute; } });
// Delete
var deleteAccountRoute_1 = require("./delete/deleteAccountRoute");
Object.defineProperty(exports, "deleteAccountRoute", { enumerable: true, get: function () { return deleteAccountRoute_1.deleteAccountRoute; } });
// Details
var getAccountDetailsRoute_1 = require("./details/getAccountDetailsRoute");
Object.defineProperty(exports, "getAccountDetailsRoute", { enumerable: true, get: function () { return getAccountDetailsRoute_1.getAccountDetailsRoute; } });
// Mail
var mailEmailVerificationCodeRoute_1 = require("./mail/mailEmailVerificationCodeRoute");
Object.defineProperty(exports, "mailEmailVerificationCodeRoute", { enumerable: true, get: function () { return mailEmailVerificationCodeRoute_1.mailEmailVerificationCodeRoute; } });
var mailPasswordResetCodeRoute_1 = require("./mail/mailPasswordResetCodeRoute");
Object.defineProperty(exports, "mailPasswordResetCodeRoute", { enumerable: true, get: function () { return mailPasswordResetCodeRoute_1.mailPasswordResetCodeRoute; } });
// OAuth
var googleOauthRoute_1 = require("./auth/googleOauthRoute");
Object.defineProperty(exports, "googleOauthRoute", { enumerable: true, get: function () { return googleOauthRoute_1.googleOauthRoute; } });
// Update
var updateAccountRoute_1 = require("./update/updateAccountRoute");
Object.defineProperty(exports, "updateAccountRoute", { enumerable: true, get: function () { return updateAccountRoute_1.updateAccountRoute; } });
var updatePasswordRoute_1 = require("./update/updatePasswordRoute");
Object.defineProperty(exports, "updatePasswordRoute", { enumerable: true, get: function () { return updatePasswordRoute_1.updatePasswordRoute; } });
// Verify
var verifyEmailRoute_1 = require("./verify/verifyEmailRoute");
Object.defineProperty(exports, "verifyEmailRoute", { enumerable: true, get: function () { return verifyEmailRoute_1.verifyEmailRoute; } });
