"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailPasswordResetCodeRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.mailPasswordResetCodeRoute = (0, express_1.Router)();
exports.mailPasswordResetCodeRoute.post("/mail-password-reset-code", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_2.validateMailPayload, middlewares_2.formatMailPayload, controllers_1.mailPasswordResetCodeController);
