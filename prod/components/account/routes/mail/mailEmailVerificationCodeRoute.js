"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailEmailVerificationCodeRoute = void 0;
const express_1 = require("express");
const middlewares_1 = require("../../../../global/middlewares");
const middlewares_2 = require("../../middlewares");
const controllers_1 = require("../../controllers");
exports.mailEmailVerificationCodeRoute = (0, express_1.Router)();
exports.mailEmailVerificationCodeRoute.post("/mail-email-verification-code", middlewares_1.ExtractOriginFromRequest, middlewares_1.BlockRequestByOrigin, middlewares_1.ExtractIPAddressFromOrigin, middlewares_1.ExtractCountryFromIPAddress, middlewares_2.validateMailPayload, middlewares_2.formatMailPayload, controllers_1.mailEmailVerificationCodeController);
