"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailEmailVerificationCode = void 0;
const postmark = __importStar(require("postmark"));
const var_1 = require("../../../../global/var");
const mailEmailVerificationCode = (name, email, emailVerificationCode) => __awaiter(void 0, void 0, void 0, function* () {
    const postmarkClient = new postmark.Client(var_1.Var.postmark.token);
    let isSuccessful = false;
    let returnData;
    let templateId = var_1.Var.postmark.template.emailVerificationCode.id;
    yield postmarkClient.sendEmailWithTemplate({
        From: `${var_1.Var.app.name} no-reply@${var_1.Var.app.domain}`,
        TemplateId: templateId,
        To: email,
        TemplateModel: {
            emailVerificationCode: emailVerificationCode,
            name: name,
            app: var_1.Var.app,
        },
    }, (error, success) => {
        if (error) {
            returnData = error;
            isSuccessful = false;
        }
        if (success) {
            returnData = success;
            isSuccessful = true;
        }
    });
    return {
        success: isSuccessful,
        message: isSuccessful
            ? `${var_1.Var.app.emoji.success} Verification code sent to ${email}`
            : `${var_1.Var.app.emoji.failure} Could not send verification code. Please contact ${var_1.Var.app.contact.email}`,
        payload: returnData,
    };
});
exports.mailEmailVerificationCode = mailEmailVerificationCode;
