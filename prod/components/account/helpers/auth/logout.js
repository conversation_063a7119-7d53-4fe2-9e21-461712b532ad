"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const var_1 = require("../../../../global/var");
dotenv_1.default.config();
const logout = (req, res) => {
    new Promise((resolve, reject) => {
        req.session.destroy((error) => {
            if (error) {
                reject(error);
                return res.status(400).json({
                    success: false,
                    message: `${var_1.Var.app.emoji.failure} Failed to logout`,
                    payload: {},
                });
            }
            res.clearCookie(process.env.EXPRESS_SESSION_NAME);
            resolve();
            let responseData = { isSessionActive: false };
            return res.status(200).json({
                success: true,
                message: `${var_1.Var.app.emoji.success} Logged out`,
                payload: responseData,
            });
        });
    });
};
exports.logout = logout;
