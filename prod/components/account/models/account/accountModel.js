"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.accountModel = void 0;
const sequelize_1 = require("sequelize");
const var_1 = require("../../../../global/var");
const modelName = "account";
const modelAttributes = {
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        allowNull: false,
        unique: true,
        primaryKey: true,
    },
    name: { type: sequelize_1.DataTypes.STRING, allowNull: false },
    email: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: { isEmail: true },
    },
    password: { type: sequelize_1.DataTypes.STRING, allowNull: true },
    is_email_verified: { type: sequelize_1.DataTypes.BOOLEAN, defaultValue: false },
    email_verification_code: {
        type: sequelize_1.DataTypes.STRING,
    },
    email_verification_code_timestamp: {
        type: sequelize_1.DataTypes.DATE,
    },
    password_reset_code: {
        type: sequelize_1.DataTypes.STRING,
    },
    password_reset_code_timestamp: {
        type: sequelize_1.DataTypes.DATE,
    },
    is_google_oauth_linked: { type: sequelize_1.DataTypes.BOOLEAN, defaultValue: false },
};
const modelOptions = {};
exports.accountModel = var_1.Sequelize.define(modelName, modelAttributes, modelOptions);
