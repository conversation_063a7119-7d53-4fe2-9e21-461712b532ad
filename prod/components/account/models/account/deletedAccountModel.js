"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deletedAccountModel = void 0;
const sequelize_1 = require("sequelize");
const var_1 = require("../../../../global/var");
const modelName = "deletedAccount";
const modelAttributes = {
    id: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        unique: true,
        primaryKey: true,
    },
    name: { type: sequelize_1.DataTypes.STRING, allowNull: false },
    email: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
        validate: { isEmail: true },
    },
    is_email_verified: { type: sequelize_1.DataTypes.BOOLEAN, defaultValue: false },
    is_google_oauth_linked: { type: sequelize_1.DataTypes.BOOLEAN, defaultValue: false },
    registered_on: { type: sequelize_1.DataTypes.STRING },
};
const modelOptions = {};
exports.deletedAccountModel = var_1.Sequelize.define(modelName, modelAttributes, modelOptions);
