"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeNewAccount = void 0;
const models_1 = require("../../models");
const var_1 = require("../../../../global/var");
const var_2 = require("../../../../global/var");
const writeNewAccount = (name, email, hashedPassword, emailVerificationCode) => __awaiter(void 0, void 0, void 0, function* () {
    let isSuccessful = false;
    let returnData;
    yield models_1.accountModel
        .create({
        name: name,
        email: email,
        password: hashedPassword,
        email_verification_code: emailVerificationCode,
        email_verification_code_timestamp: var_1.Sequelize.fn("NOW"),
    })
        .then((newAccount) => {
        isSuccessful = true;
        returnData = {
            id: newAccount.dataValues.id,
            name: newAccount.dataValues.name,
            email: newAccount.dataValues.email,
            isEmailVerified: newAccount.dataValues.is_email_verified,
            emailVerificationCode: newAccount.dataValues.email_verification_code,
        };
    })
        .catch((err) => {
        returnData = err;
    });
    return {
        success: isSuccessful,
        message: isSuccessful
            ? `${var_2.Var.app.emoji.success} New account created`
            : `${var_2.Var.app.emoji.failure} Could not create account. Please contact ${var_2.Var.app.contact.email}`,
        payload: returnData,
    };
});
exports.writeNewAccount = writeNewAccount;
