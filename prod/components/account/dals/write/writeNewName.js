"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeNewName = void 0;
const var_1 = require("../../../../global/var");
const models_1 = require("../../models");
const writeNewName = (email, newName) => __awaiter(void 0, void 0, void 0, function* () {
    let isSuccessful = false;
    let returnData;
    yield models_1.accountModel
        .update({ name: newName }, {
        where: {
            email: email,
        },
    })
        .then((updatedAccount) => {
        isSuccessful = true;
        returnData = updatedAccount;
    })
        .catch((err) => (returnData = err));
    return {
        success: isSuccessful,
        message: isSuccessful
            ? `${var_1.Var.app.emoji.success} Name updated`
            : `${var_1.Var.app.emoji.failure} Failed to update name. Please contact ${var_1.Var.app.contact.email}`,
        payload: isSuccessful ? { newName: newName } : returnData,
    };
});
exports.writeNewName = writeNewName;
