"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeEmailVerificationCode = void 0;
const var_1 = require("../../../../global/var");
const models_1 = require("../../models");
const var_2 = require("../../../../global/var");
const writeEmailVerificationCode = (email, emailVerificationCode, init) => __awaiter(void 0, void 0, void 0, function* () {
    let isSuccessful = false;
    let returnData;
    yield models_1.accountModel
        .update({
        email_verification_code: emailVerificationCode,
        email_verification_code_timestamp: init ? var_2.Sequelize.fn("NOW") : null,
    }, {
        where: {
            email: email,
        },
    })
        .then((updatedAccount) => {
        isSuccessful = true;
        returnData = updatedAccount;
    })
        .catch((err) => (returnData = err));
    return {
        success: isSuccessful,
        message: isSuccessful
            ? `${var_1.Var.app.emoji.success} Email verification code saved`
            : `${var_1.Var.app.emoji.failure} Email verification code not saved. Please contact ${var_1.Var.app.contact.email}`,
        payload: returnData,
    };
});
exports.writeEmailVerificationCode = writeEmailVerificationCode;
