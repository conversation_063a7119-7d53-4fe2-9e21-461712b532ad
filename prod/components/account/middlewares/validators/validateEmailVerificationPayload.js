"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEmailVerificationPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../global/var");
const emailVerificationPayloadSchema = joi_1.default.object({
    email: joi_1.default.string()
        .email({ tlds: { allow: false } })
        .min(5)
        .max(128)
        .lowercase()
        .trim()
        .required(),
    emailVerificationCode: joi_1.default.string().trim().length(4).required(),
});
const validateEmailVerificationPayload = (req, res, next) => {
    let { error } = emailVerificationPayloadSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    next();
};
exports.validateEmailVerificationPayload = validateEmailVerificationPayload;
