"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateGoogleOauthPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../global/var");
const googleOauthPayloadSchema = joi_1.default.object({
    token: joi_1.default.string().required(),
});
const validateGoogleOauthPayload = (req, res, next) => {
    let { error } = googleOauthPayloadSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} validateGoogleOauthPayload validated`);
    next();
};
exports.validateGoogleOauthPayload = validateGoogleOauthPayload;
