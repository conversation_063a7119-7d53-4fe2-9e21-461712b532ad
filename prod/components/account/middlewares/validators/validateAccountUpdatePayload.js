"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAccountUpdatePayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../global/var");
const accountUpdatePayloadSchema = joi_1.default.object({
    entity: joi_1.default.string().trim().required(),
    attribute: joi_1.default.string().valid("name", "email", "password").required(),
    value: joi_1.default.when("attribute", {
        switch: [
            { is: "name", then: joi_1.default.string().required() },
            {
                is: "email",
                then: joi_1.default.string()
                    .email({ tlds: { allow: false } })
                    .min(5)
                    .max(128)
                    .lowercase()
                    .trim()
                    .required(),
            },
            { is: "password", then: joi_1.default.string().min(8).max(1024).required() },
        ],
    }),
});
const validateAccountUpdatePayload = (req, res, next) => {
    let { error } = accountUpdatePayloadSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    next();
};
exports.validateAccountUpdatePayload = validateAccountUpdatePayload;
