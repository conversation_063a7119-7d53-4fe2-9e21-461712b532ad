"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePasswordResetPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../global/var");
const passwordResetPayloadSchema = joi_1.default.object({
    email: joi_1.default.string()
        .email({ tlds: { allow: false } })
        .min(5)
        .max(128)
        .lowercase()
        .trim()
        .required(),
    newPassword: joi_1.default.string().trim().min(8).required(),
    newPasswordRepeat: joi_1.default.string()
        .equal(joi_1.default.ref("newPassword"))
        .trim()
        .required(),
    passwordResetCode: joi_1.default.string().trim().length(4).required(),
});
const validatePasswordResetPayload = (req, res, next) => {
    let { error } = passwordResetPayloadSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    next();
};
exports.validatePasswordResetPayload = validatePasswordResetPayload;
