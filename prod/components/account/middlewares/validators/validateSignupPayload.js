"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateSignupPayload = void 0;
const joi_1 = __importDefault(require("joi"));
const var_1 = require("../../../../global/var");
const signupPayloadSchema = joi_1.default.object({
    name: joi_1.default.string().required(),
    email: joi_1.default.string()
        .email({ tlds: { allow: false } })
        .min(5)
        .max(128)
        .lowercase()
        .required(),
    password: joi_1.default.string().min(8).max(1024).trim().required(),
});
const validateSignupPayload = (req, res, next) => {
    let { error } = signupPayloadSchema.validate(req.body);
    if (error) {
        console.log(`${var_1.Var.app.emoji.failure} Signup payload not valid`);
        return res.status(400).json({
            success: false,
            message: `${var_1.Var.app.emoji.failure} ${error.details[0].message}`,
        });
    }
    console.log(`${var_1.Var.app.emoji.success} Signup payload valid`);
    next();
};
exports.validateSignupPayload = validateSignupPayload;
