"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const http_1 = __importDefault(require("http"));
const helpers_1 = require("./global/helpers");
const var_1 = require("./global/var");
const socket_io_1 = require("socket.io");
const var_2 = require("./global/var");
const dals_1 = require("./components/visit/dals");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
(() => __awaiter(void 0, void 0, void 0, function* () {
    yield var_1.Sequelize.authenticate()
        .then((result) => {
        console.log(`${var_2.Var.app.emoji.success} Database authenticated`);
    })
        .catch((err) => {
        console.log(`${var_2.Var.app.emoji.failure} Could not authenticate database`);
        console.log(err);
    });
    (0, helpers_1.IncludeModelAssociations)();
    // await Sequelize.sync()
    //   .then((result) => {
    //     console.log(`${Var.app.emoji.success} Models synced`);
    //   })
    //   .catch((err) => {
    //     console.log(err);
    //     console.log(`${Var.app.emoji.failure} Could not sync models`);
    //   });
    // Alters table
    yield var_1.Sequelize.sync({ alter: true })
        .then((result) => {
        console.log(`${var_2.Var.app.emoji.success} Models altered & synced`);
    })
        .catch((err) => {
        console.log(err);
        console.log(`${var_2.Var.app.emoji.failure} Could not sync models`);
    });
    const nodeServer = http_1.default.createServer(app_1.default);
    const io = new socket_io_1.Server(nodeServer, {
        cors: {
            origin: "*",
        },
    });
    io.on("connection", (socket) => {
        const email = socket.handshake.query.email;
        console.log(`${var_2.Var.app.emoji.success} ${email} connected via ${socket.id}`);
        (0, dals_1.writeNewVisit)(email, socket.id, socket.handshake.address);
        socket.on("disconnect", () => {
            console.log(`${var_2.Var.app.emoji.failure} Client disconnected: ${socket.id}`);
            (0, dals_1.writeVisitStatus)(socket.id);
        });
    });
    nodeServer.listen(var_2.Var.node.port, () => {
        console.log(`${var_2.Var.app.emoji.success} Server is running on port: ${var_2.Var.node.port}`);
    });
}))();
