<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survey Components Accessibility Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions {
            background: #f5f5f5;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .checklist input[type="checkbox"] {
            margin-right: 0.5rem;
        }
        
        /* High contrast mode test */
        @media (prefers-contrast: high) {
            .test-section {
                border: 2px solid;
                background: canvas;
                color: canvastext;
            }
        }
        
        /* Reduced motion test */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <h1>Survey Components Accessibility Test Page</h1>
    
    <div class="instructions">
        <h2>Testing Instructions</h2>
        <p>This page helps you test the accessibility features of the survey components. Use the checklists below to verify each accessibility feature works correctly.</p>
        <p><strong>Note:</strong> You'll need to load actual survey components to test them. This page provides the testing framework.</p>
    </div>

    <div class="test-section">
        <h2>Keyboard Navigation Test</h2>
        <div class="instructions">
            <p>Test keyboard navigation by using only your keyboard (no mouse). Tab through all interactive elements.</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="kb1"> <label for="kb1">Can navigate to all interactive elements using Tab</label></li>
            <li><input type="checkbox" id="kb2"> <label for="kb2">Can navigate backwards using Shift+Tab</label></li>
            <li><input type="checkbox" id="kb3"> <label for="kb3">Arrow keys work for radio button groups</label></li>
            <li><input type="checkbox" id="kb4"> <label for="kb4">Enter/Space activates buttons and selections</label></li>
            <li><input type="checkbox" id="kb5"> <label for="kb5">Escape cancels operations where applicable</label></li>
            <li><input type="checkbox" id="kb6"> <label for="kb6">Focus indicators are clearly visible</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Screen Reader Test</h2>
        <div class="instructions">
            <p>Test with a screen reader (NVDA, JAWS, VoiceOver, or browser built-in reader).</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="sr1"> <label for="sr1">All text content is announced</label></li>
            <li><input type="checkbox" id="sr2"> <label for="sr2">Form labels are properly associated</label></li>
            <li><input type="checkbox" id="sr3"> <label for="sr3">Required fields are announced as required</label></li>
            <li><input type="checkbox" id="sr4"> <label for="sr4">Error messages are announced</label></li>
            <li><input type="checkbox" id="sr5"> <label for="sr5">Progress and status updates are announced</label></li>
            <li><input type="checkbox" id="sr6"> <label for="sr6">Instructions are clear and helpful</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Focus Management Test</h2>
        <div class="instructions">
            <p>Test focus behavior during interactions and navigation.</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="fm1"> <label for="fm1">Focus moves logically through the interface</label></li>
            <li><input type="checkbox" id="fm2"> <label for="fm2">Focus is restored after modal interactions</label></li>
            <li><input type="checkbox" id="fm3"> <label for="fm3">Focus indicators are visible in all states</label></li>
            <li><input type="checkbox" id="fm4"> <label for="fm4">Focus doesn't get trapped unexpectedly</label></li>
            <li><input type="checkbox" id="fm5"> <label for="fm5">Skip links work where provided</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Form Accessibility Test</h2>
        <div class="instructions">
            <p>Test form elements and validation behavior.</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="fa1"> <label for="fa1">All form fields have proper labels</label></li>
            <li><input type="checkbox" id="fa2"> <label for="fa2">Required fields are clearly marked</label></li>
            <li><input type="checkbox" id="fa3"> <label for="fa3">Error messages are descriptive and helpful</label></li>
            <li><input type="checkbox" id="fa4"> <label for="fa4">Validation errors are announced to screen readers</label></li>
            <li><input type="checkbox" id="fa5"> <label for="fa5">Field groups are properly associated</label></li>
            <li><input type="checkbox" id="fa6"> <label for="fa6">Autocomplete attributes are used where appropriate</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Visual Accessibility Test</h2>
        <div class="instructions">
            <p>Test visual accessibility features and high contrast mode.</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="va1"> <label for="va1">Text has sufficient color contrast</label></li>
            <li><input type="checkbox" id="va2"> <label for="va2">Focus indicators are visible in high contrast mode</label></li>
            <li><input type="checkbox" id="va3"> <label for="va3">Error states don't rely solely on color</label></li>
            <li><input type="checkbox" id="va4"> <label for="va4">Text can be scaled to 200% without horizontal scrolling</label></li>
            <li><input type="checkbox" id="va5"> <label for="va5">Interface works in high contrast mode</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Component-Specific Tests</h2>
        
        <h3>sf-sensechoice (Choice Tasks)</h3>
        <ul class="checklist">
            <li><input type="checkbox" id="sc1"> <label for="sc1">Arrow keys navigate between options</label></li>
            <li><input type="checkbox" id="sc2"> <label for="sc2">Selection announcements are clear</label></li>
            <li><input type="checkbox" id="sc3"> <label for="sc3">Task progress is announced</label></li>
        </ul>

        <h3>sf-sensepriority (Priority Ranking)</h3>
        <ul class="checklist">
            <li><input type="checkbox" id="sp1"> <label for="sp1">Keyboard reordering works with Enter/Space</label></li>
            <li><input type="checkbox" id="sp2"> <label for="sp2">Drag mode is clearly announced</label></li>
            <li><input type="checkbox" id="sp3"> <label for="sp3">Ranking changes are announced</label></li>
        </ul>

        <h3>sf-sensequery (Form Surveys)</h3>
        <ul class="checklist">
            <li><input type="checkbox" id="sq1"> <label for="sq1">Form validation works accessibly</label></li>
            <li><input type="checkbox" id="sq2"> <label for="sq2">Error messages are properly associated</label></li>
            <li><input type="checkbox" id="sq3"> <label for="sq3">Multi-step forms announce progress</label></li>
        </ul>

        <h3>sf-senseprice (Price Surveys)</h3>
        <ul class="checklist">
            <li><input type="checkbox" id="spr1"> <label for="spr1">Price input has proper labels and validation</label></li>
            <li><input type="checkbox" id="spr2"> <label for="spr2">Currency symbols are announced correctly</label></li>
            <li><input type="checkbox" id="spr3"> <label for="spr3">Recurring price indicators are accessible</label></li>
        </ul>

        <h3>sf-sensepoll (Poll Surveys)</h3>
        <ul class="checklist">
            <li><input type="checkbox" id="spo1"> <label for="spo1">Single/multiple choice modes work accessibly</label></li>
            <li><input type="checkbox" id="spo2"> <label for="spo2">Selection/deselection is announced</label></li>
            <li><input type="checkbox" id="spo3"> <label for="spo3">Follow-up questions are properly linked</label></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Browser and Device Testing</h2>
        <div class="instructions">
            <p>Test across different browsers and devices to ensure consistent accessibility.</p>
        </div>
        <ul class="checklist">
            <li><input type="checkbox" id="bd1"> <label for="bd1">Chrome with keyboard navigation</label></li>
            <li><input type="checkbox" id="bd2"> <label for="bd2">Firefox with screen reader</label></li>
            <li><input type="checkbox" id="bd3"> <label for="bd3">Safari with VoiceOver</label></li>
            <li><input type="checkbox" id="bd4"> <label for="bd4">Edge with high contrast mode</label></li>
            <li><input type="checkbox" id="bd5"> <label for="bd5">Mobile Safari with VoiceOver</label></li>
            <li><input type="checkbox" id="bd6"> <label for="bd6">Android with TalkBack</label></li>
        </ul>
    </div>

    <script>
        // Simple progress tracking
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const totalTests = checkboxes.length;
        
        function updateProgress() {
            const completed = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const percentage = Math.round((completed / totalTests) * 100);
            document.title = `Accessibility Test (${percentage}% complete)`;
        }
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });
        
        // Initial progress
        updateProgress();
    </script>
</body>
</html>
