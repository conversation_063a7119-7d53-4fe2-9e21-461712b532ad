# Survey Components Accessibility Guide

This document outlines the comprehensive accessibility features implemented across all survey components to ensure WCAG 2.1 AA compliance and excellent user experience for all users, including those using assistive technologies.

## Overview

All five survey components have been enhanced with comprehensive accessibility features:

- **sf-sensechoice**: Choice-based surveys with radio button groups
- **sf-sensepriority**: Drag-and-drop ranking with keyboard alternatives
- **sf-sensequery**: Form surveys with validation and error handling
- **sf-senseprice**: Price input surveys with currency formatting
- **sf-sensepoll**: Poll surveys with single/multiple choice options

Each component includes:

- **Keyboard Navigation**: Full keyboard support with logical tab order
- **Screen Reader Support**: Proper ARIA labels, roles, and live regions
- **Focus Management**: Visible focus indicators and focus trapping
- **Form Accessibility**: Proper labels, error handling, and validation
- **High Contrast Support**: Enhanced visibility for users with visual impairments
- **Reduced Motion Support**: Respects user preferences for motion

## Keyboard Navigation

### Universal Controls
- **Tab/Shift+Tab**: Navigate between interactive elements
- **Enter/Space**: Activate buttons and select options
- **Escape**: Cancel operations or exit modal states

### Component-Specific Controls

#### sf-sensechoice (Choice Tasks)
- **Arrow Keys**: Navigate between choice options
- **Enter/Space**: Select highlighted option
- **Tab**: Move to next/previous buttons

#### sf-sensepriority (Priority Ranking)
- **Arrow Up/Down**: Navigate between ranking items
- **Enter/Space**: Grab/drop items for reordering
- **Arrow Left/Right**: Move grabbed items up/down in ranking
- **Escape**: Cancel drag operation

#### sf-sensequery (Form Surveys)
- **Tab**: Navigate between form fields
- **Arrow Keys**: Navigate radio button groups
- **Space**: Toggle checkboxes

#### sf-senseprice (Price Surveys)
- **Tab**: Navigate between form fields
- **Number input**: Direct price entry with validation
- **Enter**: Submit price when valid

#### sf-sensepoll (Poll Surveys)
- **Tab**: Navigate between poll options
- **Space**: Toggle checkboxes (multiple choice)
- **Arrow Keys**: Navigate radio buttons (single choice)

## ARIA Implementation

### Roles and Properties
- `role="main"`: Survey container
- `role="radiogroup"`: Radio button collections
- `role="group"`: Checkbox collections
- `role="listitem"`: Ranking items
- `role="alert"`: Error messages

### Labels and Descriptions
- `aria-label`: Descriptive labels for complex elements
- `aria-labelledby`: References to heading elements
- `aria-describedby`: References to help text and errors
- `aria-invalid`: Indicates form validation errors

### Live Regions
- `aria-live="polite"`: Non-intrusive announcements
- `aria-atomic="true"`: Complete message announcements

## Screen Reader Support

### Announcements
- Selection confirmations: "Selected: Option 1"
- Navigation updates: "Moving to task 2"
- Ranking changes: "Moved Item A to position 3"
- Form validation: "This field is required"
- Email validation: "Please enter a valid email address"
- Price validation: "Please enter a valid price"
- Poll selections: "Selected: Choice A" / "Deselected: Choice B"

### Descriptive Content
- Progress indicators: "Choice Task 2 of 5"
- Option descriptions: Detailed attribute information
- Instructions: Clear guidance for each interaction type

## Form Accessibility

### Labels and Required Fields
- Proper `<label>` elements with `htmlFor` attributes
- Required field indicators with `aria-label="required"`
- Field grouping with `<fieldset>` for related inputs

### Error Handling
- `aria-invalid` attributes on invalid fields
- `role="alert"` for error messages
- `aria-describedby` linking fields to error text
- Real-time validation feedback
- Email format validation with accessible error messages

### Input Types
- Semantic input types (`email`, `number`, `text`)
- Proper `autocomplete` attributes where applicable
- Placeholder text that doesn't replace labels

## Visual Accessibility

### Focus Indicators
```css
input:focus, button:focus {
  outline: 2px solid #005fcc;
  outline-offset: 2px;
}
```

### High Contrast Mode
```css
@media (prefers-contrast: high) {
  [role="radio"]:focus {
    outline: 3px solid;
  }
}
```

### Color and Contrast
- Error states use both color and text indicators
- Focus indicators don't rely solely on color
- Sufficient color contrast ratios (4.5:1 minimum)

## Motion and Animation

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

## Implementation Guidelines

### For Developers

1. **Always test with keyboard only**
   - Ensure all functionality is accessible via keyboard
   - Verify logical tab order
   - Test focus visibility

2. **Use semantic HTML**
   - Prefer native form elements over custom implementations
   - Use proper heading hierarchy
   - Include landmark roles

3. **Provide alternative text**
   - Add `alt` attributes to images
   - Use `aria-label` for icon buttons
   - Provide context for complex interactions

4. **Test with screen readers**
   - Use NVDA, JAWS, or VoiceOver
   - Verify announcements are clear and helpful
   - Test navigation patterns

### CSS Parts for Styling

All components expose CSS parts for custom styling while maintaining accessibility:

```css
/* Focus indicators */
sf-sensechoice::part(concept):focus {
  outline: 2px solid var(--focus-color);
}

/* Error states */
sf-sensequery::part(error-message) {
  color: var(--error-color);
  font-weight: bold;
}
```

## Testing Checklist

### Automated Testing
- [ ] Run axe-core accessibility tests
- [ ] Validate HTML semantics
- [ ] Check color contrast ratios

### Manual Testing
- [ ] Navigate entire survey using only keyboard
- [ ] Test with screen reader (NVDA/JAWS/VoiceOver)
- [ ] Verify focus indicators are visible
- [ ] Test in high contrast mode
- [ ] Validate error message announcements

### User Testing
- [ ] Test with actual users who use assistive technologies
- [ ] Gather feedback on navigation patterns
- [ ] Validate instruction clarity

## Browser Support

Accessibility features are supported in:
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [WebAIM Screen Reader Testing](https://webaim.org/articles/screenreader_testing/)

## Support

For accessibility questions or issues, please:
1. Check this documentation
2. Review component-specific README files
3. Test with assistive technologies
4. Report issues with detailed reproduction steps
