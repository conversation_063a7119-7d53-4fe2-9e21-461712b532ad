import { isValid<PERSON>ey, formatErrorMessage } from './utils';

describe('isValidKey', () => {
  it('returns false for undefined key', () => {
    expect(isValidKey(undefined)).toEqual(false);
  });

  it('returns false for null key', () => {
    expect(isValid<PERSON>ey(null)).toEqual(false);
  });

  it('returns false for empty string', () => {
    expect(isValidKey('')).toEqual(false);
  });

  it('returns false for whitespace string', () => {
    expect(isValidKey('   ')).toEqual(false);
  });

  it('returns true for valid key', () => {
    expect(isValidKey('valid-key')).toEqual(true);
  });
});

describe('formatErrorMessage', () => {
  it('returns string as-is', () => {
    expect(formatErrorMessage('Test error')).toEqual('Test error');
  });

  it('returns error message from Error object', () => {
    const error = new Error('Test error message');
    expect(formatErrorMessage(error)).toEqual('Test error message');
  });

  it('returns default message for unknown error types', () => {
    expect(formatErrorMessage(123)).toEqual('An unknown error occurred');
  });
});
