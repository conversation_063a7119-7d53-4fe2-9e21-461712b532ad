/**
 * Utility functions for sf-sensepoll
 */

/**
 * Interface for respondent detail options
 */
interface RespondentDetailOption {
  value: string;
  label: string;
}

/**
 * Interface for respondent detail configuration
 */
interface RespondentDetail {
  label: string;
  value: string;
  inputType: string;
  required?: boolean;
  placeholder?: string;
  options?: RespondentDetailOption[];
  defaultValue?: any;
}

/**
 * Check if a survey key is valid UUID
 * @param key The survey key to validate
 * @returns True if the key is a valid UUID, false otherwise
 */
export function isValidKey(key: string | undefined | null): boolean {
  if (typeof key !== 'string' || key.trim().length === 0) {
    return false;
  }

  // UUID v4 regex pattern
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(key.trim());
}

/**
 * Format error messages for display
 * @param error The error object or message
 * @returns A formatted error message string
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
}

/**
 * Browser-compatible helper to check if array contains a value
 * @param array The array to search
 * @param value The value to find
 * @returns true if value is found in array
 */
function arrayContains(array: string[], value: string): boolean {
  for (let i = 0; i < array.length; i++) {
    if (array[i] === value) {
      return true;
    }
  }
  return false;
}

/**
 * Browser-compatible helper to trim and split comma-separated values
 * @param value The comma-separated string
 * @returns Array of trimmed values
 */
function parseCommaSeparatedValues(value: string): string[] {
  if (!value) {
    return [];
  }
  const parts = value.split(',');
  const result = [];
  for (let i = 0; i < parts.length; i++) {
    const trimmed = parts[i].replace(/^\s+|\s+$/g, ''); // Manual trim for IE compatibility
    if (trimmed) {
      result.push(trimmed);
    }
  }
  return result;
}

/**
 * Get field configuration for rendering
 * @param detail The respondent detail configuration
 * @param value The current value of the field
 * @returns Field configuration object
 */
export function getFieldConfig(detail: RespondentDetail, value: string) {
  const inputType = detail.inputType || 'text';
  const placeholder = detail.placeholder || 'Enter your ' + detail.label.toLowerCase();
  const required = detail.required !== false;

  return {
    inputType: inputType,
    placeholder: placeholder,
    required: required,
    options: detail.options || [],
    defaultValue: detail.defaultValue,
    hasOptions: detail.options && detail.options.length > 0,
    selectedValues: parseCommaSeparatedValues(value),
    fieldValue: detail.value,
    currentValue: value || '',
  };
}

/**
 * Browser-compatible helper to add value to comma-separated list
 * @param currentValue The current comma-separated string
 * @param valueToAdd The value to add
 * @returns Updated comma-separated string
 */
export function addToCommaSeparatedList(currentValue: string, valueToAdd: string): string {
  const values = parseCommaSeparatedValues(currentValue);
  if (!arrayContains(values, valueToAdd)) {
    values.push(valueToAdd);
  }
  return values.join(', ');
}

/**
 * Browser-compatible helper to remove value from comma-separated list
 * @param currentValue The current comma-separated string
 * @param valueToRemove The value to remove
 * @returns Updated comma-separated string
 */
export function removeFromCommaSeparatedList(currentValue: string, valueToRemove: string): string {
  const values = parseCommaSeparatedValues(currentValue);
  const result = [];
  for (let i = 0; i < values.length; i++) {
    if (values[i] !== valueToRemove) {
      result.push(values[i]);
    }
  }
  return result.join(', ');
}
