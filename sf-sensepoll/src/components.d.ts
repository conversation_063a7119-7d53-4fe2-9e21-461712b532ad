/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface SfSensepoll {
        "surveyKey": string;
    }
}
declare global {
    interface HTMLSfSensepollElement extends Components.SfSensepoll, HTMLStencilElement {
    }
    var HTMLSfSensepollElement: {
        prototype: HTMLSfSensepollElement;
        new (): HTMLSfSensepollElement;
    };
    interface HTMLElementTagNameMap {
        "sf-sensepoll": HTMLSfSensepollElement;
    }
}
declare namespace LocalJSX {
    interface SfSensepoll {
        "surveyKey"?: string;
    }
    interface IntrinsicElements {
        "sf-sensepoll": SfSensepoll;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "sf-sensepoll": LocalJSX.SfSensepoll & JSXBase.HTMLAttributes<HTMLSfSensepollElement>;
        }
    }
}
