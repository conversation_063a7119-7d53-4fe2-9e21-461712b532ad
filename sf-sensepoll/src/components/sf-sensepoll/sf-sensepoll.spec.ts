import { newSpecPage } from '@stencil/core/testing';
import { SfSensepoll } from './sf-sensepoll';

describe('sf-sensepoll', () => {
  it('renders with error when no survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensepoll],
      html: '<sf-sensepoll></sf-sensepoll>',
    });
    expect(root).toEqualHtml(`
      <sf-sensepoll>
        <mock:shadow-root>
          <p part="message error-message">Please provide a valid survey key</p>
        </mock:shadow-root>
      </sf-sensepoll>
    `);
  });

  it('renders loading state when survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensepoll],
      html: '<sf-sensepoll survey-key="test-key"></sf-sensepoll>',
    });
    expect(root).toEqualHtml(`
      <sf-sensepoll survey-key="test-key">
        <mock:shadow-root>
          <p part="message loading-message">Loading survey...</p>
        </mock:shadow-root>
      </sf-sensepoll>
    `);
  });
});
