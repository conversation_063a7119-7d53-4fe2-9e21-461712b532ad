import { newE2EPage } from '@stencil/core/testing';

describe('sf-sensepoll', () => {
  it('renders', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepoll></sf-sensepoll>');
    const element = await page.find('sf-sensepoll');
    expect(element).toHaveClass('hydrated');
  });

  it('shows error message when no survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepoll></sf-sensepoll>');
    const element = await page.find('sf-sensepoll >>> p[part="message error-message"]');
    expect(element.textContent).toEqual('Please provide a valid survey key');
  });

  it('shows loading message when survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensepoll survey-key="test-key"></sf-sensepoll>');
    const element = await page.find('sf-sensepoll >>> p[part="message loading-message"]');
    expect(element.textContent).toEqual('Loading survey...');
  });
});
