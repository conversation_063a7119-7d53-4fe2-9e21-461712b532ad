import { Component, Host, h, Prop, State, Element } from '@stencil/core';
import { isV<PERSON><PERSON><PERSON><PERSON>, getFieldConfig, addToCommaSeparatedList, removeFromCommaSeparatedList } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/senseQuery';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/senseQuery';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  QUESTION = 0,
  CATEGORY = 1,
  RESPONDENT_DETAILS = 2,
  THANK_YOU = 3,
}

interface Category {
  label: string;
  value: string;
}

interface RespondentDetail {
  label: string;
  value: string;
  inputType: string; // 'text', 'email', 'dropdown', 'radio', 'checkbox', 'number'
  required?: boolean;
  placeholder?: string;
  options?: Array<{
    value: string;
    label: string;
  }>;
  defaultValue?: any;
}

interface SurveyConfig {
  question: string;
  categories?: Category[];
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

@Component({
  tag: 'sf-sensequery',
  styleUrl: 'sf-sensequery.css',
  shadow: true,
})
export class SfSensequery {
  @Element() el: HTMLElement;

  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.QUESTION;

  @State() questionInput: string = '';

  @State() selectedCategory: string | null = null;

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  @State() announceMessage: string = '';

  @State() formErrors: { [key: string]: string } = {};

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;

    const endpoint: string = `${SURVEY_API_ENDPOINT}/${this.surveyKey}`;

    try {
      const response = await fetch(endpoint);
      const apiResponse: ApiResponse = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.message);
      }

      if (!apiResponse.payload || !apiResponse.payload.config || !apiResponse.payload.config.question) {
        throw new Error('Invalid survey data received');
      }

      this.config = apiResponse.payload.config;
      this.respondentDetails = apiResponse.payload.respondentDetails || [];
    } catch (err) {
      this.error = err instanceof Error ? err.message : String(err);
      console.error('Error fetching survey data:', err);
    } finally {
      this.loading = false;
    }
  }

  private handleQuestionChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.questionInput = input.value;
  }

  private handleCategorySelect(value: string) {
    this.selectedCategory = value;
  }

  private handleRespondentDetailChange(fieldId: string, event: Event) {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    let value: string;

    if (target.type === 'checkbox') {
      // Handle checkbox inputs using browser-compatible helpers
      const currentValue = this.userRespondentDetails[fieldId] || '';
      if (target.checked) {
        value = addToCommaSeparatedList(currentValue, target.value);
      } else {
        value = removeFromCommaSeparatedList(currentValue, target.value);
      }
    } else {
      // Handle all other input types (text, email, number, radio, select)
      value = target.value;
    }

    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [fieldId]: value,
    };

    // Update form errors after field change
    this.updateFormErrors();

    // Announce validation errors for accessibility
    const detail = this.respondentDetails.find(d => d.value === fieldId);
    if (detail && value && target.type === 'email') {
      if (!this.isValidEmail(value)) {
        this.announceToScreenReader('Invalid email format');
      }
    }
  }

  private createInputHandler(fieldValue: string) {
    const self = this;
    return function (e: Event) {
      self.handleRespondentDetailChange(fieldValue, e);
    };
  }

  private isValueInArray(array: string[], value: string): boolean {
    for (let i = 0; i < array.length; i++) {
      if (array[i] === value) {
        return true;
      }
    }
    return false;
  }

  private renderField(detail: RespondentDetail) {
    const config = getFieldConfig(detail, this.userRespondentDetails[detail.value] || '');
    const inputHandler = this.createInputHandler(detail.value);
    const fieldId = `field-${detail.value}`;
    const errorId = `error-${detail.value}`;
    const hasError = !!this.formErrors[detail.value];

    switch (config.inputType) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <input
            part="input form-input"
            type={config.inputType}
            id={fieldId}
            value={config.currentValue}
            onInput={inputHandler}
            placeholder={config.placeholder}
            required={config.required}
            aria-invalid={hasError}
            aria-describedby={hasError ? errorId : undefined}
          />
        );

      case 'dropdown':
        if (!config.hasOptions) {
          return (
            <input
              part="input form-input"
              type="text"
              id={fieldId}
              value={config.currentValue}
              onInput={inputHandler}
              placeholder={config.placeholder}
              required={config.required}
              aria-invalid={hasError}
              aria-describedby={hasError ? errorId : undefined}
            />
          );
        }
        return (
          <select
            part="select form-select"
            id={fieldId}
            onChange={inputHandler}
            required={config.required}
            aria-invalid={hasError}
            aria-describedby={hasError ? errorId : undefined}
          >
            {!config.defaultValue && (
              <option value="" disabled>
                {config.placeholder}
              </option>
            )}
            {config.options.map(function (option) {
              return (
                <option key={option.value} value={option.value} selected={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}>
                  {option.label}
                </option>
              );
            })}
          </select>
        );

      case 'radio':
        if (!config.hasOptions) {
          return (
            <input
              part="input form-input"
              type="text"
              id={fieldId}
              value={config.currentValue}
              onInput={inputHandler}
              placeholder={config.placeholder}
              required={config.required}
              aria-invalid={hasError}
              aria-describedby={hasError ? errorId : undefined}
            />
          );
        }
        return (
          <div part="radio-group" role="radiogroup" aria-invalid={hasError} aria-describedby={hasError ? errorId : undefined}>
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="radio-option">
                  <input
                    part="radio-input"
                    type="radio"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}
                    onChange={inputHandler}
                    required={config.required}
                  />
                  <label part="radio-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      case 'checkbox':
        if (!config.hasOptions) {
          return (
            <input
              part="input form-input"
              type="text"
              id={fieldId}
              value={config.currentValue}
              onInput={inputHandler}
              placeholder={config.placeholder}
              required={config.required}
              aria-invalid={hasError}
              aria-describedby={hasError ? errorId : undefined}
            />
          );
        }
        const self = this;
        return (
          <div part="checkbox-group" role="group" aria-invalid={hasError} aria-describedby={hasError ? errorId : undefined}>
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="checkbox-option">
                  <input
                    part="checkbox-input"
                    type="checkbox"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={self.isValueInArray(config.selectedValues, option.value)}
                    onChange={inputHandler}
                  />
                  <label part="checkbox-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      default:
        return (
          <input
            part="input form-input"
            type="text"
            id={fieldId}
            value={config.currentValue}
            onInput={inputHandler}
            placeholder={config.placeholder}
            required={config.required}
            aria-invalid={hasError}
            aria-describedby={hasError ? errorId : undefined}
          />
        );
    }
  }

  private isCurrentStepValid(): boolean {
    if (this.currentStep === SurveyStep.QUESTION) {
      return this.questionInput.trim().length > 0;
    }

    if (this.currentStep === SurveyStep.CATEGORY) {
      if (this.config?.categories?.length) {
        return this.selectedCategory !== null;
      }
    }

    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      // Check if all required respondent details are filled
      const requiredFields = this.respondentDetails.filter(detail => detail.required !== false);
      return requiredFields.every(field => {
        const value = this.userRespondentDetails[field.value];
        return value && value.trim().length > 0;
      });
    }

    return true;
  }

  private announceToScreenReader(message: string) {
    this.announceMessage = message;
    setTimeout(() => {
      this.announceMessage = '';
    }, 1000);
  }

  private validateField(value: string, required: boolean, inputType?: string): string | null {
    if (required && (!value || value.trim().length === 0)) {
      return 'This field is required';
    }

    if (value && value.trim().length > 0) {
      if (inputType === 'email') {
        if (!this.isValidEmail(value)) {
          return 'Please enter a valid email address';
        }
      }
    }

    return null;
  }

  private isValidEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  private updateFormErrors() {
    const errors: { [key: string]: string } = {};

    this.respondentDetails.forEach(detail => {
      const value = this.userRespondentDetails[detail.value] || '';
      const config = getFieldConfig(detail, value);
      const error = this.validateField(value, detail.required !== false, config.inputType);
      if (error) {
        errors[detail.value] = error;
      }
    });

    this.formErrors = errors;
  }

  private hasCategoriesStep(): boolean {
    return this.config?.categories?.length > 0;
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails && this.respondentDetails.length > 0;
  }

  private nextStep() {
    if (!this.isCurrentStepValid()) {
      return;
    }
    switch (this.currentStep) {
      case SurveyStep.QUESTION:
        if (!this.hasCategoriesStep() && !this.hasRespondentDetailsStep()) {
          this.submitSurvey();
        } else if (!this.hasCategoriesStep()) {
          this.currentStep = SurveyStep.RESPONDENT_DETAILS;
        } else {
          this.currentStep = SurveyStep.CATEGORY;
        }
        break;

      case SurveyStep.CATEGORY:
        if (!this.hasRespondentDetailsStep()) {
          this.submitSurvey();
        } else {
          this.currentStep = SurveyStep.RESPONDENT_DETAILS;
        }
        break;

      case SurveyStep.RESPONDENT_DETAILS:
        this.submitSurvey();
        break;
    }
  }

  private prevStep() {
    switch (this.currentStep) {
      case SurveyStep.CATEGORY:
        this.currentStep = SurveyStep.QUESTION;
        break;

      case SurveyStep.RESPONDENT_DETAILS:
        if (!this.hasCategoriesStep()) {
          this.currentStep = SurveyStep.QUESTION;
        } else {
          this.currentStep = SurveyStep.CATEGORY;
        }
        break;
    }
  }

  private async submitSurvey() {
    if (!this.questionInput.trim()) {
      return;
    }

    this.announceToScreenReader('Submitting survey...');
    this.loading = true;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          query: this.questionInput,
          category: this.selectedCategory,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'senseQuery',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.currentStep = SurveyStep.THANK_YOU;
      this.submitted = true;
      this.announceToScreenReader('Survey submitted successfully!');
    } catch (err) {
      this.error = err instanceof Error ? err.message : String(err);
      this.announceToScreenReader('Error submitting survey. Please try again.');
      console.error('Error submitting response:', err);
    } finally {
      this.loading = false;
    }
  }

  private handleTextareaFocus() {
    if (this.surveyStartTime === 0) {
      this.surveyStartTime = Date.now();
    }
  }

  private handleTextareaBlur() {
    if (this.questionInput.trim() === '') {
      this.surveyStartTime = 0;
    }
  }

  private getUserAgentInfo() {
    // Use modern navigator.userAgentData when available, fallback to userAgent parsing
    const getPlatform = () => {
      if ('userAgentData' in navigator && (navigator as any).userAgentData?.platform) {
        return (navigator as any).userAgentData.platform;
      }
      // Fallback: extract platform info from userAgent
      const ua = navigator.userAgent;
      if (ua.includes('Win')) return 'Windows';
      if (ua.includes('Mac')) return 'macOS';
      if (ua.includes('Linux')) return 'Linux';
      if (ua.includes('Android')) return 'Android';
      if (ua.includes('iOS')) return 'iOS';
      return 'Unknown';
    };

    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: getPlatform(),
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private renderQuestionStep() {
    const isFinalStep = !this.hasCategoriesStep() && !this.hasRespondentDetailsStep();

    return (
      <div part="step question-step">
        <h2 part="heading question-heading">{this.config?.question || 'What is your question?'}</h2>
        <textarea
          part="input textarea"
          value={this.questionInput}
          onInput={e => this.handleQuestionChange(e)}
          onFocus={() => this.handleTextareaFocus()}
          onBlur={() => this.handleTextareaBlur()}
          placeholder="Type your question here..."
          rows={4}
        ></textarea>
        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()} disabled={!this.questionInput.trim()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderCategoryStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step category-step">
        <h2 part="heading category-heading">Select a category for your question</h2>
        <div part="categories-container">
          {this.config?.categories?.map(category => (
            <label part="category-option">
              <input
                part="radio-input"
                type="radio"
                name="category"
                value={category.value}
                checked={this.selectedCategory === category.value}
                onChange={() => this.handleCategorySelect(category.value)}
              />
              <span part="category-label">{category.label}</span>
            </label>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button next-button" onClick={() => this.nextStep()} disabled={!this.selectedCategory}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-heading">Tell us about yourself</h2>
        <div part="respondent-fields-container">
          {this.respondentDetails.map(detail => {
            const fieldId = `field-${detail.value}`;
            const errorId = `error-${detail.value}`;
            const hasError = !!this.formErrors[detail.value];

            return (
              <div part="field" class={{ 'field-error': hasError }}>
                <label part="field-label" htmlFor={fieldId}>
                  {detail.label}
                  {detail.required !== false && (
                    <span part="required-indicator" aria-label="required">
                      {' '}
                      *
                    </span>
                  )}
                </label>
                {this.renderField(detail)}
                {hasError && (
                  <div part="error-message" id={errorId} role="alert" aria-live="polite">
                    {this.formErrors[detail.value]}
                  </div>
                )}
              </div>
            );
          })}
          {this.respondentDetails.length === 0 && <p part="empty-message">No additional information is required.</p>}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitSurvey()} disabled={!this.isCurrentStepValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private retryOperation() {
    this.error = null;

    return this.fetchSurveyData();
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your submission!'}</h2>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.QUESTION]: this.renderQuestionStep.bind(this),
      [SurveyStep.CATEGORY]: this.renderCategoryStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.QUESTION];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host part="container" role="main" aria-label="Survey Form">
        {this.renderCurrentStep()}
        {/* ARIA live region for screen reader announcements */}
        <div aria-live="polite" aria-atomic="true" class="sr-only" part="announcements">
          {this.announceMessage}
        </div>
      </Host>
    );
  }
}
