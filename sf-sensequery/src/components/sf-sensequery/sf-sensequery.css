:host {
  display: block;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for accessibility */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: 2px solid #005fcc;
  outline-offset: 2px;
}

/* Error state styling */
.field-error input,
.field-error select,
.field-error textarea {
  border-color: #d32f2f;
}

[part='error-message'] {
  color: #d32f2f;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Required field indicator */
[part='required-indicator'] {
  color: #d32f2f;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  input:focus,
  select:focus,
  textarea:focus,
  button:focus {
    outline: 3px solid;
  }

  [part='error-message'] {
    background-color: #d32f2f;
    color: white;
    padding: 0.25rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
