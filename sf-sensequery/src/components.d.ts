/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface SfSensequery {
        "surveyKey": string;
    }
}
declare global {
    interface HTMLSfSensequeryElement extends Components.SfSensequery, HTMLStencilElement {
    }
    var HTMLSfSensequeryElement: {
        prototype: HTMLSfSensequeryElement;
        new (): HTMLSfSensequeryElement;
    };
    interface HTMLElementTagNameMap {
        "sf-sensequery": HTMLSfSensequeryElement;
    }
}
declare namespace LocalJSX {
    interface SfSensequery {
        "surveyKey"?: string;
    }
    interface IntrinsicElements {
        "sf-sensequery": SfSensequery;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "sf-sensequery": LocalJSX.SfSensequery & JSXBase.HTMLAttributes<HTMLSfSensequeryElement>;
        }
    }
}
