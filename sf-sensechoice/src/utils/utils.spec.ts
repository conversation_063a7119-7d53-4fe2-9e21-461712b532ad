import { isValid<PERSON><PERSON>, formatErrorMessage, shuffleArray, generateId } from './utils';

describe('isValidKey', () => {
  it('returns false for undefined key', () => {
    expect(isValid<PERSON>ey(undefined)).toEqual(false);
  });

  it('returns false for null key', () => {
    expect(isValid<PERSON>ey(null)).toEqual(false);
  });

  it('returns false for empty string', () => {
    expect(isValidKey('')).toEqual(false);
  });

  it('returns false for whitespace string', () => {
    expect(isValidKey('   ')).toEqual(false);
  });

  it('returns true for valid key', () => {
    expect(isValidKey('valid-key')).toEqual(true);
  });
});

describe('formatErrorMessage', () => {
  it('returns string as-is', () => {
    expect(formatErrorMessage('Test error')).toEqual('Test error');
  });

  it('returns error message from Error object', () => {
    const error = new Error('Test error message');
    expect(formatErrorMessage(error)).toEqual('Test error message');
  });

  it('returns default message for unknown error types', () => {
    expect(formatErrorMessage(123)).toEqual('An unknown error occurred');
  });
});

describe('shuffleArray', () => {
  it('returns array with same length', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffleArray(original);
    expect(shuffled.length).toEqual(original.length);
  });

  it('returns array with same elements', () => {
    const original = [1, 2, 3, 4, 5];
    const shuffled = shuffleArray(original);
    expect(shuffled.sort()).toEqual(original.sort());
  });

  it('does not modify original array', () => {
    const original = [1, 2, 3, 4, 5];
    const originalCopy = [...original];
    shuffleArray(original);
    expect(original).toEqual(originalCopy);
  });
});

describe('generateId', () => {
  it('generates a string', () => {
    const id = generateId();
    expect(typeof id).toBe('string');
  });

  it('generates unique IDs', () => {
    const id1 = generateId();
    const id2 = generateId();
    expect(id1).not.toEqual(id2);
  });

  it('generates IDs of expected length', () => {
    const id = generateId();
    expect(id.length).toBe(9);
  });
});
