/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface SfSensechoice {
        "surveyKey": string;
    }
}
declare global {
    interface HTMLSfSensechoiceElement extends Components.SfSensechoice, HTMLStencilElement {
    }
    var HTMLSfSensechoiceElement: {
        prototype: HTMLSfSensechoiceElement;
        new (): HTMLSfSensechoiceElement;
    };
    interface HTMLElementTagNameMap {
        "sf-sensechoice": HTMLSfSensechoiceElement;
    }
}
declare namespace LocalJSX {
    interface SfSensechoice {
        "surveyKey"?: string;
    }
    interface IntrinsicElements {
        "sf-sensechoice": SfSensechoice;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "sf-sensechoice": LocalJSX.SfSensechoice & JSXBase.HTMLAttributes<HTMLSfSensechoiceElement>;
        }
    }
}
