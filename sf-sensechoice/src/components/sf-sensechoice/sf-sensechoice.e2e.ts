import { newE2EPage } from '@stencil/core/testing';

describe('sf-sensechoice', () => {
  it('renders', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensechoice></sf-sensechoice>');
    const element = await page.find('sf-sensechoice');
    expect(element).toHaveClass('hydrated');
  });

  it('shows error message when no survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensechoice></sf-sensechoice>');
    const element = await page.find('sf-sensechoice >>> p[part="message error-message"]');
    expect(element.textContent).toEqual('Please provide a valid survey key');
  });

  it('shows loading message when survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-sensechoice survey-key="test-key"></sf-sensechoice>');
    const element = await page.find('sf-sensechoice >>> p[part="message loading-message"]');
    expect(element.textContent).toEqual('Loading survey...');
  });
});
