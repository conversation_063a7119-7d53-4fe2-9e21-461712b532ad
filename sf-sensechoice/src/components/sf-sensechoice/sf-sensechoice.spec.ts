import { newSpecPage } from '@stencil/core/testing';
import { SfSensechoice } from './sf-sensechoice';

describe('sf-sensechoice', () => {
  it('renders with error when no survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensechoice],
      html: '<sf-sensechoice></sf-sensechoice>',
    });
    expect(root).toEqualHtml(`
      <sf-sensechoice>
        <mock:shadow-root>
          <p part="message error-message">Please provide a valid survey key</p>
        </mock:shadow-root>
      </sf-sensechoice>
    `);
  });

  it('renders loading state when survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSensechoice],
      html: '<sf-sensechoice survey-key="test-key"></sf-sensechoice>',
    });
    expect(root).toEqualHtml(`
      <sf-sensechoice survey-key="test-key">
        <mock:shadow-root>
          <p part="message loading-message">Loading survey...</p>
        </mock:shadow-root>
      </sf-sensechoice>
    `);
  });
});
