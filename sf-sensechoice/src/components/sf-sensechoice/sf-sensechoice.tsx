import { Component, Host, h, Prop, State, Element, Listen } from '@stencil/core';
import { isV<PERSON><PERSON><PERSON><PERSON>, getFieldConfig, addToCommaSeparatedList, removeFromCommaSeparatedList } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/senseChoice';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/senseChoice';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  CHOICE_TASK = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface ConjointConcept {
  conceptId: string;
  attributes: { [key: string]: string }; // attributeValue -> selected variant value
  selected?: boolean;
}

interface ConjointChoiceTask {
  taskId: string;
  taskNumber: number;
  alternatives: ConjointConcept[];
  includeNoneOption: boolean;
  selected?: boolean;
}

interface RespondentDetail {
  label: string;
  value: string;
  inputType: string; // 'text', 'email', 'dropdown', 'radio', 'checkbox', 'number'
  required?: boolean;
  placeholder?: string;
  options?: Array<{
    value: string;
    label: string;
  }>;
  defaultValue?: any;
}

interface SurveyConfig {
  type: string; // 'lite' or 'full'
  attributes: { label: string; value: string }[];
  attributeVariants: { [key: string]: { label: string; value: string }[] }; // attributeValue -> variants array
  selectedConcepts: ConjointConcept[]; // User-curated concepts
  choiceTasks: ConjointChoiceTask[]; // User-curated tasks
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

interface TaskResponse {
  taskId: string;
  selectedConceptId: string | null; // null for "none of these"
}

@Component({
  tag: 'sf-sensechoice',
  styleUrl: 'sf-sensechoice.css',
  shadow: true,
})
export class SfSensechoice {
  @Element() el: HTMLElement;

  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.CHOICE_TASK;

  @State() currentTaskIndex: number = 0;

  @State() taskResponses: TaskResponse[] = [];

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  @State() focusedConceptIndex: number = -1;

  @State() announceMessage: string = '';

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);
      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      this.config = data.payload.config;
      this.respondentDetails = data.payload.respondentDetails || [];

      // Initialize task responses
      if (this.config?.choiceTasks) {
        this.taskResponses = this.config.choiceTasks.map(task => ({
          taskId: task.taskId,
          selectedConceptId: null,
        }));
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || this.taskResponses.length === 0) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          choiceResponses: this.taskResponses,
          surveyType: this.config.type,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'senseChoice',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    // Use modern navigator.userAgentData when available, fallback to userAgent parsing
    const getPlatform = () => {
      if ('userAgentData' in navigator && (navigator as any).userAgentData?.platform) {
        return (navigator as any).userAgentData.platform;
      }
      // Fallback: extract platform info from userAgent
      const ua = navigator.userAgent;
      if (ua.includes('Win')) return 'Windows';
      if (ua.includes('Mac')) return 'macOS';
      if (ua.includes('Linux')) return 'Linux';
      if (ua.includes('Android')) return 'Android';
      if (ua.includes('iOS')) return 'iOS';
      return 'Unknown';
    };

    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: getPlatform(),
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.CHOICE_TASK) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.CHOICE_TASK;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  @Listen('keydown')
  handleKeyDown(event: KeyboardEvent) {
    if (this.currentStep === SurveyStep.CHOICE_TASK) {
      this.handleChoiceTaskKeydown(event);
    }
  }

  private handleChoiceTaskKeydown(event: KeyboardEvent) {
    const currentTask = this.getCurrentTask();
    if (!currentTask) return;

    const totalOptions = currentTask.alternatives.length + (currentTask.includeNoneOption ? 1 : 0);

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        this.focusedConceptIndex = (this.focusedConceptIndex + 1) % totalOptions;
        this.focusCurrentConcept();
        break;

      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        this.focusedConceptIndex = this.focusedConceptIndex <= 0 ? totalOptions - 1 : this.focusedConceptIndex - 1;
        this.focusCurrentConcept();
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        if (this.focusedConceptIndex >= 0) {
          if (this.focusedConceptIndex < currentTask.alternatives.length) {
            const concept = currentTask.alternatives[this.focusedConceptIndex];
            this.handleConceptSelection(concept.conceptId);
          } else if (currentTask.includeNoneOption) {
            this.handleConceptSelection(null);
          }
        }
        break;
    }
  }

  private focusCurrentConcept() {
    const conceptElements = this.el.shadowRoot?.querySelectorAll('[data-concept-index]');
    if (conceptElements && this.focusedConceptIndex >= 0 && this.focusedConceptIndex < conceptElements.length) {
      (conceptElements[this.focusedConceptIndex] as HTMLElement).focus();
    }
  }

  private announceToScreenReader(message: string) {
    this.announceMessage = message;
    // Clear the message after a short delay to allow screen readers to announce it
    setTimeout(() => {
      this.announceMessage = '';
    }, 1000);
  }

  private isValidEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  private validateRespondentField(detail: RespondentDetail, value: string): string | null {
    if (detail.required !== false && (!value || value.trim().length === 0)) {
      return 'This field is required';
    }

    if (value && value.trim().length > 0) {
      const config = getFieldConfig(detail, value);
      if (config.inputType === 'email') {
        if (!this.isValidEmail(value)) {
          return 'Please enter a valid email address';
        }
      }
    }

    return null;
  }

  private handleRespondentDetailChange(key: string, event: Event) {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    let value: string;

    if (target.type === 'checkbox') {
      // Handle checkbox inputs using browser-compatible helpers
      const currentValue = this.userRespondentDetails[key] || '';
      if (target.checked) {
        value = addToCommaSeparatedList(currentValue, target.value);
      } else {
        value = removeFromCommaSeparatedList(currentValue, target.value);
      }
    } else {
      // Handle all other input types (text, email, number, radio, select)
      value = target.value;
    }

    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private createInputHandler(fieldValue: string) {
    const self = this;
    return function (e: Event) {
      self.handleRespondentDetailChange(fieldValue, e);
    };
  }

  private isValueInArray(array: string[], value: string): boolean {
    for (let i = 0; i < array.length; i++) {
      if (array[i] === value) {
        return true;
      }
    }
    return false;
  }

  private renderField(detail: RespondentDetail) {
    const config = getFieldConfig(detail, this.userRespondentDetails[detail.value] || '');
    const inputHandler = this.createInputHandler(detail.value);

    switch (config.inputType) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <input part="input form-input" type={config.inputType} value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />
        );

      case 'dropdown':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <select part="select form-select" onChange={inputHandler} required={config.required}>
            {!config.defaultValue && (
              <option value="" disabled>
                {config.placeholder}
              </option>
            )}
            {config.options.map(function (option) {
              return (
                <option key={option.value} value={option.value} selected={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}>
                  {option.label}
                </option>
              );
            })}
          </select>
        );

      case 'radio':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <div part="radio-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="radio-option">
                  <input
                    part="radio-input"
                    type="radio"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}
                    onChange={inputHandler}
                    required={config.required}
                  />
                  <label part="radio-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      case 'checkbox':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        const self = this;
        return (
          <div part="checkbox-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="checkbox-option">
                  <input
                    part="checkbox-input"
                    type="checkbox"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={self.isValueInArray(config.selectedValues, option.value)}
                    onChange={inputHandler}
                  />
                  <label part="checkbox-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      default:
        return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
    }
  }

  private isRespondentDetailsValid(): boolean {
    return this.respondentDetails.every(detail => {
      const value = this.userRespondentDetails[detail.value] || '';
      const error = this.validateRespondentField(detail, value);
      return error === null;
    });
  }

  private getCurrentTask(): ConjointChoiceTask | null {
    if (!this.config?.choiceTasks || this.currentTaskIndex >= this.config.choiceTasks.length) {
      return null;
    }
    return this.config.choiceTasks[this.currentTaskIndex];
  }

  private getCurrentTaskResponse(): TaskResponse | null {
    const currentTask = this.getCurrentTask();
    if (!currentTask) return null;

    return this.taskResponses.find(response => response.taskId === currentTask.taskId) || null;
  }

  private handleConceptSelection(conceptId: string | null) {
    const currentTask = this.getCurrentTask();
    if (!currentTask) return;

    const responseIndex = this.taskResponses.findIndex(response => response.taskId === currentTask.taskId);
    if (responseIndex >= 0) {
      this.taskResponses = [
        ...this.taskResponses.slice(0, responseIndex),
        { ...this.taskResponses[responseIndex], selectedConceptId: conceptId },
        ...this.taskResponses.slice(responseIndex + 1),
      ];
    }

    // Announce selection to screen readers
    if (conceptId === null) {
      this.announceToScreenReader('Selected: None of these options');
    } else {
      const conceptIndex = currentTask.alternatives.findIndex(c => c.conceptId === conceptId);
      this.announceToScreenReader(`Selected: Option ${conceptIndex + 1}`);
    }
  }

  private nextTask() {
    if (this.currentTaskIndex < (this.config?.choiceTasks?.length || 0) - 1) {
      this.currentTaskIndex++;
      this.focusedConceptIndex = -1; // Reset focus when moving to next task
      this.announceToScreenReader(`Moving to task ${this.currentTaskIndex + 1}`);
    } else {
      // All tasks completed, move to next step
      this.nextStep();
    }
  }

  private prevTask() {
    if (this.currentTaskIndex > 0) {
      this.currentTaskIndex--;
      this.focusedConceptIndex = -1; // Reset focus when moving to previous task
      this.announceToScreenReader(`Moving to task ${this.currentTaskIndex + 1}`);
    }
  }

  private isCurrentTaskCompleted(): boolean {
    const response = this.getCurrentTaskResponse();
    return response !== null && response.selectedConceptId !== undefined;
  }

  private getAttributeLabel(attributeValue: string): string {
    const attribute = this.config?.attributes?.find(attr => attr.value === attributeValue);
    return attribute?.label || attributeValue;
  }

  private getVariantLabel(attributeValue: string, variantValue: string): string {
    const variants = this.config?.attributeVariants?.[attributeValue];
    const variant = variants?.find(v => v.value === variantValue);
    return variant?.label || variantValue;
  }

  private renderConcept(concept: ConjointConcept, isSelected: boolean, onSelect: () => void, index: number) {
    const conceptId = `concept-${concept.conceptId}`;
    const radioId = `radio-${concept.conceptId}`;
    const attributesText = Object.entries(concept.attributes)
      .map(([attributeValue, variantValue]) => `${this.getAttributeLabel(attributeValue)}: ${this.getVariantLabel(attributeValue, variantValue)}`)
      .join(', ');

    return (
      <div
        part={`concept ${isSelected ? 'concept-selected' : 'concept-unselected'}`}
        role="radio"
        aria-checked={isSelected}
        aria-labelledby={conceptId}
        aria-describedby={`${conceptId}-attributes`}
        tabindex={index === this.focusedConceptIndex ? 0 : -1}
        data-concept-index={index}
        onClick={onSelect}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onSelect();
          }
        }}
      >
        <div part="concept-header">
          <input part="concept-radio" type="radio" id={radioId} checked={isSelected} onChange={onSelect} aria-hidden="true" tabindex="-1" />
          <span part="concept-title" id={conceptId}>
            Option {concept.conceptId}
          </span>
        </div>
        <div part="concept-attributes" id={`${conceptId}-attributes`} aria-label={`Attributes: ${attributesText}`}>
          {Object.entries(concept.attributes).map(([attributeValue, variantValue]) => (
            <div part="concept-attribute" key={attributeValue}>
              <span part="attribute-label">{this.getAttributeLabel(attributeValue)}:</span>
              <span part="attribute-value">{this.getVariantLabel(attributeValue, variantValue)}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  private renderChoiceTaskStep() {
    const currentTask = this.getCurrentTask();
    const currentResponse = this.getCurrentTaskResponse();

    if (!currentTask) {
      return (
        <div part="step choice-task-step">
          <p part="message error-message">No choice tasks available</p>
        </div>
      );
    }

    const totalTasks = this.config?.choiceTasks?.length || 0;
    const isLastTask = this.currentTaskIndex === totalTasks - 1;
    const isFinalStep = isLastTask && !this.hasRespondentDetailsStep();

    return (
      <div part="step choice-task-step">
        <div part="task-header">
          <h2 part="heading task-heading" id="task-heading">
            Choice Task {currentTask.taskNumber} of {totalTasks}
          </h2>
          <p part="task-instructions" id="task-instructions">
            Please select your preferred option from the alternatives below:
          </p>
        </div>

        <div part="concepts-container" role="radiogroup" aria-labelledby="task-heading">
          {currentTask.alternatives.map((concept, index) =>
            this.renderConcept(concept, currentResponse?.selectedConceptId === concept.conceptId, () => this.handleConceptSelection(concept.conceptId), index),
          )}

          {currentTask.includeNoneOption && (
            <div
              part={`concept none-option ${currentResponse?.selectedConceptId === null ? 'concept-selected' : 'concept-unselected'}`}
              role="radio"
              aria-checked={currentResponse?.selectedConceptId === null}
              aria-label="None of these options"
              tabindex={currentTask.alternatives.length === this.focusedConceptIndex ? 0 : -1}
              data-concept-index={currentTask.alternatives.length}
              onClick={() => this.handleConceptSelection(null)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  this.handleConceptSelection(null);
                }
              }}
            >
              <div part="concept-header">
                <input
                  part="concept-radio"
                  type="radio"
                  checked={currentResponse?.selectedConceptId === null}
                  onChange={() => this.handleConceptSelection(null)}
                  aria-hidden="true"
                  tabindex="-1"
                />
                <span part="concept-title">None of these options</span>
              </div>
            </div>
          )}
        </div>

        <div part="button-container">
          {this.currentTaskIndex > 0 && (
            <button part="button back-button" onClick={() => this.prevTask()}>
              Previous
            </button>
          )}
          <button part="button next-button" onClick={() => this.nextTask()} disabled={!this.isCurrentTaskCompleted()}>
            {isLastTask ? (isFinalStep ? 'Submit' : 'Next') : 'Next Task'}
          </button>
        </div>

        <div part="progress-indicator">
          <div part="progress-bar">
            <div part="progress-fill" style={{ width: `${((this.currentTaskIndex + 1) / totalTasks) * 100}%` }}></div>
          </div>
          <span part="progress-text">
            Task {this.currentTaskIndex + 1} of {totalTasks}
          </span>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              {this.renderField(detail)}
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        <div part="completion-summary">
          <p part="summary-text">You completed {this.config?.choiceTasks?.length || 0} choice tasks.</p>
        </div>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.CHOICE_TASK]: this.renderChoiceTaskStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.CHOICE_TASK];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container" role="main" aria-label="Survey">
          {this.renderCurrentStep()}
          {/* ARIA live region for screen reader announcements */}
          <div aria-live="polite" aria-atomic="true" class="sr-only" part="announcements">
            {this.announceMessage}
          </div>
        </div>
      </Host>
    );
  }
}
