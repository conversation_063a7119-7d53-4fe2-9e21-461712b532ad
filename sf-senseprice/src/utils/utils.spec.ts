import { isValidKey, formatErrorMessage, formatCurrency, isValidPrice } from './utils';

describe('isValidKey', () => {
  it('returns false for undefined key', () => {
    expect(isValidKey(undefined)).toEqual(false);
  });

  it('returns false for null key', () => {
    expect(isValid<PERSON>ey(null)).toEqual(false);
  });

  it('returns false for empty string', () => {
    expect(isValidKey('')).toEqual(false);
  });

  it('returns false for whitespace string', () => {
    expect(isValidKey('   ')).toEqual(false);
  });

  it('returns true for valid key', () => {
    expect(isValidKey('valid-key')).toEqual(true);
  });
});

describe('formatErrorMessage', () => {
  it('returns string as-is', () => {
    expect(formatErrorMessage('Test error')).toEqual('Test error');
  });

  it('returns error message from Error object', () => {
    const error = new Error('Test error message');
    expect(formatErrorMessage(error)).toEqual('Test error message');
  });

  it('returns default message for unknown error types', () => {
    expect(formatErrorMessage(123)).toEqual('An unknown error occurred');
  });
});

describe('formatCurrency', () => {
  it('formats USD currency correctly', () => {
    expect(formatCurrency(100, 'USD')).toEqual('$100.00');
  });

  it('formats EUR currency correctly', () => {
    expect(formatCurrency(50.5, 'EUR')).toEqual('€50.50');
  });

  it('handles invalid currency with fallback', () => {
    expect(formatCurrency(25, 'INVALID')).toEqual('INVALID 25.00');
  });
});

describe('isValidPrice', () => {
  it('returns true for valid number', () => {
    expect(isValidPrice(100)).toEqual(true);
  });

  it('returns true for valid string number', () => {
    expect(isValidPrice('50.5')).toEqual(true);
  });

  it('returns true for zero', () => {
    expect(isValidPrice(0)).toEqual(true);
  });

  it('returns false for negative numbers', () => {
    expect(isValidPrice(-10)).toEqual(false);
  });

  it('returns false for invalid string', () => {
    expect(isValidPrice('invalid')).toEqual(false);
  });
});
