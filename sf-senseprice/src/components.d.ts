/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
export namespace Components {
    interface SfSenseprice {
        "surveyKey": string;
    }
}
declare global {
    interface HTMLSfSensepriceElement extends Components.SfSenseprice, HTMLStencilElement {
    }
    var HTMLSfSensepriceElement: {
        prototype: HTMLSfSensepriceElement;
        new (): HTMLSfSensepriceElement;
    };
    interface HTMLElementTagNameMap {
        "sf-senseprice": HTMLSfSensepriceElement;
    }
}
declare namespace LocalJSX {
    interface SfSenseprice {
        "surveyKey"?: string;
    }
    interface IntrinsicElements {
        "sf-senseprice": SfSenseprice;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "sf-senseprice": LocalJSX.SfSenseprice & JSXBase.HTMLAttributes<HTMLSfSensepriceElement>;
        }
    }
}
