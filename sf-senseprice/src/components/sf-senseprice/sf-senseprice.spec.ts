import { newSpecPage } from '@stencil/core/testing';
import { SfSenseprice } from './sf-senseprice';

describe('sf-senseprice', () => {
  it('renders with error when no survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSenseprice],
      html: '<sf-senseprice></sf-senseprice>',
    });
    expect(root).toEqualHtml(`
      <sf-senseprice>
        <mock:shadow-root>
          <p part="message error-message">Please provide a valid survey key</p>
        </mock:shadow-root>
      </sf-senseprice>
    `);
  });

  it('renders loading state when survey key is provided', async () => {
    const { root } = await newSpecPage({
      components: [SfSenseprice],
      html: '<sf-senseprice survey-key="test-key"></sf-senseprice>',
    });
    expect(root).toEqualHtml(`
      <sf-senseprice survey-key="test-key">
        <mock:shadow-root>
          <p part="message loading-message">Loading survey...</p>
        </mock:shadow-root>
      </sf-senseprice>
    `);
  });
});
