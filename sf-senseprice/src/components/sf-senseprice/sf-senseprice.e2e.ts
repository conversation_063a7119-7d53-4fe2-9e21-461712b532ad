import { newE2EPage } from '@stencil/core/testing';

describe('sf-senseprice', () => {
  it('renders', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-senseprice></sf-senseprice>');
    const element = await page.find('sf-senseprice');
    expect(element).toHaveClass('hydrated');
  });

  it('shows error message when no survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-senseprice></sf-senseprice>');
    const element = await page.find('sf-senseprice >>> p[part="message error-message"]');
    expect(element.textContent).toEqual('Please provide a valid survey key');
  });

  it('shows loading message when survey key is provided', async () => {
    const page = await newE2EPage();

    await page.setContent('<sf-senseprice survey-key="test-key"></sf-senseprice>');
    const element = await page.find('sf-senseprice >>> p[part="message loading-message"]');
    expect(element.textContent).toEqual('Loading survey...');
  });
});
