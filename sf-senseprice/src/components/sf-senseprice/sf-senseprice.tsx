import { Component, Host, h, Prop, State, Element } from '@stencil/core';
import { isValidKey, formatCurrency, isValidPrice, getFieldConfig, addToCommaSeparatedList, removeFromCommaSeparatedList } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/sensePrice';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/sensePrice';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  PRICE_INPUT = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface RespondentDetail {
  label: string;
  value: string;
  inputType: string; // 'text', 'email', 'dropdown', 'radio', 'checkbox', 'number'
  required?: boolean;
  placeholder?: string;
  options?: Array<{
    value: string;
    label: string;
  }>;
  defaultValue?: any;
}

interface SurveyConfig {
  currency: string;
  priceType: string; // 'recurring' or 'non-recurring'
  recurringBasis?: string; // 'monthly' or 'annual' (only if priceType is 'recurring')
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

@Component({
  tag: 'sf-senseprice',
  styleUrl: 'sf-senseprice.css',
  shadow: true,
})
export class SfSenseprice {
  @Element() el: HTMLElement;

  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.PRICE_INPUT;

  @State() priceInput: string = '';

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  @State() announceMessage: string = '';

  @State() formErrors: { [key: string]: string } = {};

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);
      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      this.config = data.payload.config;
      this.respondentDetails = data.payload.respondentDetails || [];
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || !isValidPrice(this.priceInput)) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          price: parseFloat(this.priceInput),
          currency: this.config.currency,
          priceType: this.config.priceType,
          recurringBasis: this.config.recurringBasis,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'sensePrice',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    // Use modern navigator.userAgentData when available, fallback to userAgent parsing
    const getPlatform = () => {
      if ('userAgentData' in navigator && (navigator as any).userAgentData?.platform) {
        return (navigator as any).userAgentData.platform;
      }
      // Fallback: extract platform info from userAgent
      const ua = navigator.userAgent;
      if (ua.includes('Win')) return 'Windows';
      if (ua.includes('Mac')) return 'macOS';
      if (ua.includes('Linux')) return 'Linux';
      if (ua.includes('Android')) return 'Android';
      if (ua.includes('iOS')) return 'iOS';
      return 'Unknown';
    };

    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: getPlatform(),
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.PRICE_INPUT) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
        this.announceToScreenReader('Moving to respondent details step');
      } else {
        this.announceToScreenReader('Submitting price survey...');
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.announceToScreenReader('Submitting survey...');
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.PRICE_INPUT;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handlePriceChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.priceInput = target.value;
    this.validatePrice();
  }

  private validatePrice() {
    const errors: { [key: string]: string } = { ...this.formErrors };

    if (!this.priceInput || this.priceInput.trim() === '') {
      errors['price'] = 'Price is required';
    } else if (!this.isPriceValid()) {
      errors['price'] = 'Please enter a valid price';
    } else {
      delete errors['price'];
    }

    this.formErrors = errors;
  }

  private announceToScreenReader(message: string) {
    this.announceMessage = message;
    setTimeout(() => {
      this.announceMessage = '';
    }, 1000);
  }

  private isValidEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  private validateRespondentField(detail: RespondentDetail, value: string): string | null {
    if (detail.required !== false && (!value || value.trim().length === 0)) {
      return 'This field is required';
    }

    if (value && value.trim().length > 0) {
      const config = getFieldConfig(detail, value);
      if (config.inputType === 'email') {
        if (!this.isValidEmail(value)) {
          return 'Please enter a valid email address';
        }
      }
    }

    return null;
  }

  private handleRespondentDetailChange(key: string, event: Event) {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    let value: string;

    if (target.type === 'checkbox') {
      // Handle checkbox inputs using browser-compatible helpers
      const currentValue = this.userRespondentDetails[key] || '';
      if (target.checked) {
        value = addToCommaSeparatedList(currentValue, target.value);
      } else {
        value = removeFromCommaSeparatedList(currentValue, target.value);
      }
    } else {
      // Handle all other input types (text, email, number, radio, select)
      value = target.value;
    }

    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private createInputHandler(fieldValue: string) {
    const self = this;
    return function (e: Event) {
      self.handleRespondentDetailChange(fieldValue, e);
    };
  }

  private isValueInArray(array: string[], value: string): boolean {
    for (let i = 0; i < array.length; i++) {
      if (array[i] === value) {
        return true;
      }
    }
    return false;
  }

  private renderField(detail: RespondentDetail) {
    const config = getFieldConfig(detail, this.userRespondentDetails[detail.value] || '');
    const inputHandler = this.createInputHandler(detail.value);
    const fieldId = `field-${detail.value}`;
    const errorId = `error-${detail.value}`;
    const hasError = !!this.formErrors[detail.value];

    switch (config.inputType) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <input
            part="input form-input"
            type={config.inputType}
            id={fieldId}
            value={config.currentValue}
            onInput={inputHandler}
            placeholder={config.placeholder}
            required={config.required}
            aria-invalid={hasError}
            aria-describedby={hasError ? errorId : undefined}
          />
        );

      case 'dropdown':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <select part="select form-select" onChange={inputHandler} required={config.required}>
            {!config.defaultValue && (
              <option value="" disabled>
                {config.placeholder}
              </option>
            )}
            {config.options.map(function (option) {
              return (
                <option key={option.value} value={option.value} selected={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}>
                  {option.label}
                </option>
              );
            })}
          </select>
        );

      case 'radio':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        return (
          <div part="radio-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="radio-option">
                  <input
                    part="radio-input"
                    type="radio"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={config.currentValue === option.value || (!config.currentValue && config.defaultValue === option.value)}
                    onChange={inputHandler}
                    required={config.required}
                  />
                  <label part="radio-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      case 'checkbox':
        if (!config.hasOptions) {
          return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
        }
        const self = this;
        return (
          <div part="checkbox-group">
            {config.options.map(function (option) {
              return (
                <div key={option.value} part="checkbox-option">
                  <input
                    part="checkbox-input"
                    type="checkbox"
                    id={config.fieldValue + '-' + option.value}
                    name={config.fieldValue}
                    value={option.value}
                    checked={self.isValueInArray(config.selectedValues, option.value)}
                    onChange={inputHandler}
                  />
                  <label part="checkbox-label" htmlFor={config.fieldValue + '-' + option.value}>
                    {option.label}
                  </label>
                </div>
              );
            })}
          </div>
        );

      default:
        return <input part="input form-input" type="text" value={config.currentValue} onInput={inputHandler} placeholder={config.placeholder} required={config.required} />;
    }
  }

  private isRespondentDetailsValid(): boolean {
    return this.respondentDetails.every(detail => {
      const value = this.userRespondentDetails[detail.value] || '';
      const error = this.validateRespondentField(detail, value);
      return error === null;
    });
  }

  private isPriceValid(): boolean {
    return isValidPrice(this.priceInput) && this.priceInput.trim().length > 0;
  }

  private getFormattedPrice(): string {
    if (!this.isPriceValid() || !this.config) {
      return '';
    }
    return formatCurrency(parseFloat(this.priceInput), this.config.currency);
  }

  private getPriceLabel(): string {
    if (!this.config) {
      return 'Enter your price';
    }

    let label = `Enter your price in ${this.config.currency.toUpperCase()}`;

    if (this.config.priceType === 'recurring' && this.config.recurringBasis) {
      label += ` (${this.config.recurringBasis})`;
    }

    return label;
  }

  private renderPriceInputStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();
    const priceInputId = 'price-input';
    const hasError = !!this.formErrors['price'];

    return (
      <div part="step price-input-step">
        <h2 part="heading price-heading" id="price-heading">
          {this.getPriceLabel()}
        </h2>
        <div part="price-input-container" role="group" aria-labelledby="price-heading">
          <label part="currency-symbol" htmlFor={priceInputId} aria-hidden="true">
            {this.config?.currency?.toUpperCase() || '$'}
          </label>
          <input
            part="input price-input"
            type="number"
            id={priceInputId}
            min="0"
            step="0.01"
            value={this.priceInput}
            onInput={e => this.handlePriceChange(e)}
            placeholder="0.00"
            aria-label={`Enter price in ${this.config?.currency?.toUpperCase() || 'USD'}`}
            aria-invalid={hasError}
            aria-describedby={hasError ? 'price-error' : this.isPriceValid() ? 'price-preview' : undefined}
            required
          />
          {this.config?.priceType === 'recurring' && this.config?.recurringBasis && (
            <div part="recurring-indicator" aria-label={`per ${this.config.recurringBasis.slice(0, -2)}`}>
              /{this.config.recurringBasis}
            </div>
          )}
        </div>
        {hasError && (
          <div part="error-message" id="price-error" role="alert" aria-live="polite">
            {this.formErrors['price']}
          </div>
        )}
        {this.isPriceValid() && (
          <div part="price-preview">
            Preview: {this.getFormattedPrice()}
            {this.config?.priceType === 'recurring' && this.config?.recurringBasis && ` per ${this.config.recurringBasis.slice(0, -2)}`}
          </div>
        )}
        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()} disabled={!this.isPriceValid()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              {this.renderField(detail)}
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        {this.isPriceValid() && this.config && (
          <div part="response-summary">
            <p part="summary-text">
              You indicated a price of {this.getFormattedPrice()}
              {this.config.priceType === 'recurring' && this.config.recurringBasis && ` per ${this.config.recurringBasis.slice(0, -2)}`}
            </p>
          </div>
        )}
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.PRICE_INPUT]: this.renderPriceInputStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.PRICE_INPUT];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Please provide a valid survey key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container" role="main" aria-label="Price Survey">
          {this.renderCurrentStep()}
          {/* ARIA live region for screen reader announcements */}
          <div aria-live="polite" aria-atomic="true" class="sr-only" part="announcements">
            {this.announceMessage}
          </div>
        </div>
      </Host>
    );
  }
}
