# sf-senseprice

A customizable, embeddable price feedback web component that collects pricing preferences through an intuitive interface.

## Overview

The `sf-senseprice` component provides a streamlined price collection experience that can be embedded on any website. It supports both one-time and recurring pricing models with currency formatting and validation.

## Installation

### NPM

```bash
npm install @sensefolks/sf-senseprice --save
```

### Script Tag

```html
<script type="module" src="https://unpkg.com/@sensefolks/sf-senseprice/dist/sf-senseprice/sf-senseprice.esm.js"></script>
<script nomodule src="https://unpkg.com/@sensefolks/sf-senseprice/dist/sf-senseprice/sf-senseprice.js"></script>
```

## Basic Usage

Add the component to your HTML with a survey key:

```html
<sf-senseprice survey-key="your-survey-key"></sf-senseprice>
```

## Features

- **Currency Support**: Automatic currency formatting for multiple currencies
- **Pricing Models**: Support for both one-time and recurring pricing
- **Recurring Basis**: Monthly or annual recurring options
- **Price Validation**: Built-in validation for price inputs
- **Real-time Preview**: Live preview of formatted prices
- **Respondent Details**: Optional collection of respondent information
- **Error Handling**: Graceful error handling with retry functionality
- **Framework Agnostic**: Works with React, Vue, Angular, and vanilla JavaScript

## Configuration

The component expects a survey configuration from the API with the following structure:

```typescript
interface SurveyConfig {
  currency: string; // Currency code (e.g., 'USD', 'EUR')
  priceType: string; // 'recurring' or 'non-recurring'
  recurringBasis?: string; // 'monthly' or 'annual' (only if priceType is 'recurring')
  thankYouMessage: string; // Thank you message
}
```

## Styling

The component is completely unstyled by default and uses CSS parts for customization.

### Example Styling

```css
/* Main container styling */
sf-senseprice {
  display: block;
  font-family: system-ui, -apple-system, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  color: #333;
}

/* Price input container */
sf-senseprice::part(price-input-container) {
  display: flex;
  align-items: center;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 1rem 0;
}

sf-senseprice::part(currency-symbol) {
  font-weight: 600;
  margin-right: 0.5rem;
  color: #6b7280;
}

sf-senseprice::part(price-input) {
  flex: 1;
  border: none;
  outline: none;
  font-size: 1.25rem;
  font-weight: 500;
}

sf-senseprice::part(recurring-indicator) {
  margin-left: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Price preview */
sf-senseprice::part(price-preview) {
  background-color: #f3f4f6;
  padding: 0.75rem;
  border-radius: 6px;
  margin: 1rem 0;
  font-weight: 500;
  color: #374151;
}

/* Response summary */
sf-senseprice::part(response-summary) {
  background-color: #ecfdf5;
  border: 1px solid #d1fae5;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
}

sf-senseprice::part(summary-text) {
  color: #065f46;
  font-weight: 500;
  margin: 0;
}

/* Buttons */
sf-senseprice::part(button) {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

sf-senseprice::part(next-button),
sf-senseprice::part(submit-button) {
  background-color: #3b82f6;
  color: white;
  border: none;
}

sf-senseprice::part(next-button):hover,
sf-senseprice::part(submit-button):hover {
  background-color: #2563eb;
}

sf-senseprice::part(back-button) {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  margin-right: 0.75rem;
}
```

## Framework Integration

### React

```jsx
import React from 'react';
import '@sensefolks/sf-senseprice';

function PriceComponent() {
  return <sf-senseprice survey-key="your-survey-key"></sf-senseprice>;
}
```

### Vue

```html
<template>
  <sf-senseprice survey-key="your-survey-key"></sf-senseprice>
</template>

<script>
  import '@sensefolks/sf-senseprice';

  export default {
    name: 'PriceComponent',
  };
</script>
```

### Angular

```typescript
// In your module
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}

// In your component
import '@sensefolks/sf-senseprice';
```

<!-- Auto Generated Below -->


## Properties

| Property    | Attribute    | Description | Type     | Default     |
| ----------- | ------------ | ----------- | -------- | ----------- |
| `surveyKey` | `survey-key` |             | `string` | `undefined` |


## Shadow Parts

| Part                           | Description |
| ------------------------------ | ----------- |
| `"back-button"`                |             |
| `"button"`                     |             |
| `"button-container"`           |             |
| `"checkbox-group"`             |             |
| `"checkbox-input"`             |             |
| `"checkbox-label"`             |             |
| `"checkbox-option"`            |             |
| `"currency-symbol"`            |             |
| `"error-container"`            |             |
| `"error-message"`              |             |
| `"form-container"`             |             |
| `"form-field"`                 |             |
| `"form-input"`                 |             |
| `"form-label"`                 |             |
| `"form-select"`                |             |
| `"heading"`                    |             |
| `"input"`                      |             |
| `"loading-message"`            |             |
| `"message"`                    |             |
| `"next-button"`                |             |
| `"price-heading"`              |             |
| `"price-input"`                |             |
| `"price-input-container"`      |             |
| `"price-input-step"`           |             |
| `"price-preview"`              |             |
| `"radio-group"`                |             |
| `"radio-input"`                |             |
| `"radio-label"`                |             |
| `"radio-option"`               |             |
| `"recurring-indicator"`        |             |
| `"required-indicator"`         |             |
| `"respondent-details-heading"` |             |
| `"respondent-details-step"`    |             |
| `"response-summary"`           |             |
| `"retry-button"`               |             |
| `"select"`                     |             |
| `"step"`                       |             |
| `"submit-button"`              |             |
| `"summary-text"`               |             |
| `"survey-container"`           |             |
| `"thank-you-heading"`          |             |
| `"thank-you-step"`             |             |


----------------------------------------------

*Built with [StencilJS](https://stenciljs.com/)*
