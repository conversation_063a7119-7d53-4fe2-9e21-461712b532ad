# Response API

API for handling responses with a single endpoint for submitting responses. This API is focused solely on accepting survey submissions and does not provide endpoints for retrieving survey details.

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on the `.env.example` file
4. Start the development server:
   ```
   npm run dev
   ```

> **Note:** This API requires existing `surveys` and `responses` tables in the database. It connects to these tables without altering their structure.

## API Endpoints

### Submit Response

```
POST /v1/responses
```

Submit a response to a survey.

#### Request Body

```json
{
  "surveyPublicKey": "uuid-of-survey",
  "responseData": {
    "question1": "answer1",
    "question2": "answer2",
    ...
  },
  "respondentDetails": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    ...
  },
  "userAgent": {
    "browser": "Chrome",
    "version": "91.0.4472.124",
    "os": "Windows",
    "device": "Desktop",
    ...
  },
  "completionTime": 12500
}
```

Notes:

- `surveyPublicKey` (required): The public key of the survey
- `responseData` (required): The response data for the survey questions
- `respondentDetails` (optional): Information about the respondent (stored as "respondent_details" in the database)
- `userAgent` (optional): Information about the user's browser and device
- `completionTime` (optional): Time taken to complete the survey in milliseconds

#### Response

```json
{
  "success": true,
  "message": "Response submitted successfully",
  "responseId": "uuid-of-response"
}
```

## Architecture

The API uses a layered architecture:

- **Routes**: Define the API endpoints
- **Middlewares**: Handle request validation, survey verification, and metadata extraction
- **Controllers**: Process the request and return the response
- **Data Access Layer (DAL)**: Handle database operations (reading survey data and writing response data)
- **Models**: Define the database schema (for both surveys and responses tables)

## Caching

The API uses Redis for caching survey data to improve performance. Survey existence is checked from Redis cache using the format `survey:${publicKey}`, with a fallback to the database if not found in cache. The API does not alter the structure of existing tables.
