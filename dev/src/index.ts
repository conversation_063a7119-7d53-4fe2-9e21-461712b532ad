import app from './app';
import http from 'http';
import { IncludeModelAssociations, validateEnvironment, validateSessionConfig } from './global/helpers';
import { Sequelize, Var } from './global/var';
import { redisClient, logger } from './global/services';
import dotenv from 'dotenv';
dotenv.config();

// SECURITY HARDENING: Validate environment before starting
validateEnvironment();
validateSessionConfig();

/**
 * Main application entry point
 * Initializes Redis, Database, and HTTP server with proper error handling
 */
(async () => {
  try {
    // Initialize Redis connection with proper error handling and logging
    await new Promise<void>((resolve, reject) => {
      redisClient.on('ready', () => {
        // Use logger for consistent logging across environments
        logger.info('Redis client connected successfully');
        // DEBUG: Additional console output in development only
        if (Var.node.env === 'dev') {
          console.log(`${Var.app.emoji.success} [DEV] Redis client connected`);
        }
        resolve();
      });
      redisClient.on('error', err => {
        // Use logger for error handling with proper data masking
        logger.error('Could not connect to Redis', err);
        // DEBUG: Additional console output in development only
        if (Var.node.env === 'dev') {
          console.error(`${Var.app.emoji.failure} [DEV] Redis connection error:`, err);
        }
        reject(err);
      });
    });
  } catch (err) {
    // Critical error - use logger but also ensure console output for immediate visibility in dev
    logger.error('Exiting due to Redis connection failure', err);
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} [DEV] CRITICAL: Redis connection failed - exiting`);
    }
    process.exit(1);
  }

  try {
    // Authenticate database connection
    await Sequelize.authenticate();
    logger.info('Database authenticated successfully');
    // DEBUG: Additional console output in development only
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} [DEV] Database authenticated`);
    }
  } catch (err) {
    // Critical database error
    logger.error('Could not authenticate database', err);
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} [DEV] CRITICAL: Database authentication failed - exiting`);
      console.error(err);
    }
    process.exit(1);
  }

  // Include model associations
  IncludeModelAssociations();
  logger.debug('Model associations included');

  // Sync database models with environment-specific behavior
  try {
    if (Var.node.env === 'dev' && Var.node.db.reset) {
      logger.warn('Forcing database reset as requested by RESET_DB=true');
      // DEBUG: Console output for development database operations
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} [DEV] Forcing database reset as requested by RESET_DB=true...`);
      }
      await Sequelize.sync({ force: true });
      logger.info('Database reset and tables created successfully');
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} [DEV] Database reset and tables created`);
      }
    } else if (Var.node.env === 'dev' && Var.node.db.alter) {
      logger.warn('Altering database tables as requested by ALTER_DB=true');
      // DEBUG: Console output for development database operations
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} [DEV] Altering database tables as requested by ALTER_DB=true...`);
      }
      await Sequelize.sync({ alter: true });
      logger.info('Database tables altered successfully');
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} [DEV] Database tables altered`);
      }
    } else {
      logger.info('Syncing database models without dropping or altering tables');
      // DEBUG: Console output for development database operations
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.info} [DEV] Syncing database models without dropping or altering tables...`);
      }
      await Sequelize.sync({ force: false, alter: false });
      logger.info("Database tables synced (tables created if they didn't exist)");
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} [DEV] Database tables synced (tables created if they didn't exist)`);
      }
    }
  } catch (err) {
    logger.error('Could not sync database models', err);
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} [DEV] CRITICAL: Database sync failed`);
      console.error(err);
    }
  }

  // Create and start HTTP server
  const nodeServer = http.createServer(app);

  nodeServer.listen(Var.node.port, () => {
    logger.info(`Server is running on port: ${Var.node.port}`);
    // DEBUG: Keep console output for server start confirmation in development
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} [DEV] Development server started on port: ${Var.node.port}`);
    }
  });
})();
