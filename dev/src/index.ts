import app from './app';
import http from 'http';

import { Sequelize } from './global/var';
import { Var } from './global/var';
import { initRedisClient, closeRedisConnection } from './global/services';
import dotenv from 'dotenv';

dotenv.config();

const startServer = async () => {
  let server: http.Server | null = null;

  try {
    console.log(`${Var.app.emoji.success} Connecting to database...`);
    await Sequelize.authenticate();
    console.log(`${Var.app.emoji.success} Database connection established successfully`);

    console.log(`${Var.app.emoji.success} Loading models...`);
    await import('./components/survey/models/surveyModel');
    await import('./components/response/models/responseModel');

    console.log(`${Var.app.emoji.success} Syncing database tables...`);
    try {
      // Sync with the database, creating tables if they don't exist
      await Sequelize.sync({ alter: false });
      console.log(`${Var.app.emoji.success} Database tables synced successfully`);
    } catch (error) {
      console.error(`${Var.app.emoji.failure} Error syncing database tables:`, error);
      process.exit(1);
    }

    console.log(`${Var.app.emoji.success} Initializing Redis client...`);
    initRedisClient();

    server = http.createServer(app);

    server.listen(Var.node.port, () => {
      console.log(`${Var.app.emoji.success} Response API is running on port: ${Var.node.port}`);
      console.log(`${Var.app.emoji.success} Environment: ${Var.node.env}`);
      console.log(`${Var.app.emoji.success} Health check available at: http://localhost:${Var.node.port}/health`);
    });

    server.on('error', error => {
      console.error(`${Var.app.emoji.failure} Server error:`, error);
      process.exit(1);
    });

    setupGracefulShutdown(server);
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Failed to start server:`, error);
    process.exit(1);
  }
};

const setupGracefulShutdown = (server: http.Server) => {
  const shutdown = async (signal: string) => {
    console.log(`\n${Var.app.emoji.warning} Received ${signal}, shutting down gracefully...`);

    server.close(() => {
      console.log(`${Var.app.emoji.success} HTTP server closed`);

      Sequelize.close()
        .then(async () => {
          console.log(`${Var.app.emoji.success} Database connection closed`);

          try {
            await closeRedisConnection();
            console.log(`${Var.app.emoji.success} Redis connection closed`);
          } catch (err) {
            console.error(`${Var.app.emoji.failure} Error closing Redis connection:`, err);
          }

          console.log(`${Var.app.emoji.success} Shutdown complete`);
          process.exit(0);
        })
        .catch(err => {
          console.error(`${Var.app.emoji.failure} Error closing database connection:`, err);
          process.exit(1);
        });
    });

    setTimeout(() => {
      console.error(`${Var.app.emoji.failure} Shutdown timed out, forcing exit`);
      process.exit(1);
    }, 10000);
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  process.on('uncaughtException', error => {
    console.error(`${Var.app.emoji.failure} Uncaught exception:`, error);
    shutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, _promise) => {
    console.error(`${Var.app.emoji.failure} Unhandled promise rejection:`, reason);
    shutdown('unhandledRejection');
  });
};

startServer();
