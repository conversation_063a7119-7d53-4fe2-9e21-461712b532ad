import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

const signupPayloadSchema = Joi.object({
  name: Joi.string().min(1).max(100).trim().required(),
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .min(5)
    .max(128)
    .lowercase()
    .required(),
  password: Joi.string()
    .min(8) // Minimum 8 characters - Argon2 hashing provides security
    .max(1024)
    .trim()
    .required(),
});

export const validateSignupPayload = (req: Request, res: Response, next: NextFunction) => {
  const { error } = signupPayloadSchema.validate(req.body);

  if (error) {
    logger.warn('Signup payload validation failed', { error: error.details[0].message });
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  logger.debug('Signup payload validation passed');
  next();
};
