import Jo<PERSON> from "joi";
import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";

const accountUpdatePayloadSchema = Joi.object({
  attribute: Joi.string().valid("name", "email", "password").required(),
  value: Joi.when("attribute", {
    switch: [
      { is: "name", then: Joi.string().required() },
      {
        is: "email",
        then: Joi.string()
          .email({ tlds: { allow: false } })
          .min(5)
          .max(128)
          .lowercase()
          .trim()
          .required(),
      },
      { is: "password", then: Joi.string().min(8).max(1024).required() },
    ],
  }),
});

export const validateAccountUpdatePayload = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let { error } = accountUpdatePayloadSchema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  next();
};
