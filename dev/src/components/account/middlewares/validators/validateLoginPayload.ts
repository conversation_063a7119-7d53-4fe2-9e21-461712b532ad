import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';

import { Var } from '../../../../global/var';

// SECURITY HARDENING: Enhanced login validation
const loginPayloadSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .min(5)
    .max(128)
    .lowercase()
    .trim()
    .required(),
  password: Joi.string()
    .min(8) // Keep existing minimum for login (users may have old passwords)
    .max(1024)
    .required(),
}).options({
  stripUnknown: true, // HARDENING: Remove unknown fields
  abortEarly: false, // HARDENING: Validate all fields
});

export const validateLoginPayload = (req: Request, res: Response, next: NextFunction) => {
  const { error } = loginPayloadSchema.validate(req.body);

  if (error) {
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  next();
};
