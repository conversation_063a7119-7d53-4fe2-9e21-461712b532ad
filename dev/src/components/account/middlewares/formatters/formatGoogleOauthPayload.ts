import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";
import { OAuth2Client, TokenPayload } from 'google-auth-library'; // Import OAuth2Client and TokenPayload
import { logger } from "../../../../global/services/logger"; // Assuming logger is here

// Initialize the Google OAuth2 client
// IMPORTANT: Ensure Var.google.oauth.clientId is set in your environment variables
const GOOGLE_CLIENT_ID = Var.google.oauth.clientId;

if (!GOOGLE_CLIENT_ID) {
  logger.error('CRITICAL: GOOGLE_CLIENT_ID is not configured in environment variables. Google OAuth will not work.');
  // Optionally, you could throw an error here to prevent the app from starting/running with misconfiguration
  throw new Error('CRITICAL: GOOGLE_CLIENT_ID is not configured.');
}

const client = new OAuth2Client(GOOGLE_CLIENT_ID);

export const formatGoogleOauthPayload = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { token } = req.body;

  if (!token) {
    logger.warn('Google OAuth: Token missing from request body');
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Google token is required.`,
    });
  }



  if (!GOOGLE_CLIENT_ID) {
    logger.error('Google OAuth: GOOGLE_CLIENT_ID is not configured. Cannot verify token.');
    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Google OAuth is not configured correctly on the server.`,
    });
  }

  try {
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: GOOGLE_CLIENT_ID, // Specify the CLIENT_ID of the app that accesses the backend
    });

    const payload: TokenPayload | undefined = ticket.getPayload();

    if (!payload) {
      logger.error('Google OAuth: Invalid token or unable to retrieve payload after verification.');
      return res.status(401).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid Google token. Payload missing.`, 
      });
    }

    // Basic check for essential claims required by the application
    if (!payload.email || payload.email_verified === undefined || !payload.sub) { // email_verified can be false
        logger.error('Google OAuth: Token payload missing essential claims (email, email_verified, sub).', { 
            email: !!payload.email,
            email_verified: payload.email_verified,
            sub: !!payload.sub 
        });
        return res.status(401).json({
            success: false,
            message: `${Var.app.emoji.failure} Invalid Google token. Essential claims missing or invalid.`,
        });
    }

    res.locals.googleVerifiedPayload = payload; // Store the verified payload
    logger.info('Google OAuth: Token successfully verified and payload extracted for email: ' + payload.email);
    next();

  } catch (error) {
    logger.error('Google OAuth: Token verification failed.', { 
        error: error instanceof Error ? error.message : String(error),
        // Avoid logging the full token in production for security
        tokenSnippet: typeof token === 'string' ? token.substring(0, 20) + '...' : 'N/A' 
    });
    
    let errorMessage = 'Failed to verify Google token.';

    if (error instanceof Error) {
        if (error.message.includes('Token used too late') || error.message.includes('expired')) {
            errorMessage = 'Google token has expired. Please try signing in again.';
        } else if (error.message.includes('Invalid token signature')) {
            errorMessage = 'Invalid Google token signature.';
        } else if (error.message.includes('audience')) {
            errorMessage = 'Google token audience mismatch. Possible configuration error or token intended for a different service.';
            // Potentially a server-side issue if audience is misconfigured
        } else if (error.message.includes('issuer')) {
            errorMessage = 'Invalid Google token issuer.';
        }
    }

    return res.status(401).json({
      success: false,
      message: `${Var.app.emoji.failure} ${errorMessage}`,
    });
  }
};
