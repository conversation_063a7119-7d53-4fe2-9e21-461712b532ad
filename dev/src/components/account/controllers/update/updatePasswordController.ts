import { Request, Response, NextFunction } from 'express';
import { readAccountByEmail, writeNewPassword, writeClearPasswordResetCode } from '../../dals';
import { hashPassword, mailAccountChangeNotification } from '../../helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';
import { verify6DigitCode, isTokenExpired } from '../../../security/helpers';

export const updatePasswordController = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, passwordResetCode, newPassword } = res.locals;

    logger.info(`Password reset attempt`, { email });

    const account = await readAccountByEmail(email);
    if (!account) {
      logger.warn(`Password reset failed: Account not found`, { email });
      return res.status(404).json({
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
      });
    }

    const storedHashedCode = account.dataValues.password_reset_code;
    if (!storedHashedCode) {
      logger.warn(`Password reset failed: No reset code found`, { email });
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} No reset code found. Please request a new code.`,
      });
    }

    // SECURITY IMPLEMENTATION: Enhanced 6-digit code validation with timing-safe comparison
    const isValidCode = verify6DigitCode(passwordResetCode, storedHashedCode);
    const expiryTimestamp = account.dataValues.password_reset_code_timestamp;
    const isExpired = !expiryTimestamp || isTokenExpired(new Date(expiryTimestamp));

    if (!isValidCode || isExpired) {
      // Log potential attack with additional context
      logger.warn(`Password reset failed: Invalid or expired 6-digit reset code`, {
        email,
        attemptedCode: passwordResetCode.substring(0, 3) + '***',
        isValidCode,
        isExpired,
        expiryTimestamp,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        timestamp: new Date().toISOString(),
      });

      // Generic error message to prevent information disclosure
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid or expired reset code. Please request a new code.`,
      });
    }

    const newHashedPassword = await hashPassword(newPassword);

    const updateResult = await writeNewPassword(email, newHashedPassword);
    if (!updateResult.success) {
      logger.error(`Failed to update password`, {
        email,
        error: updateResult.payload,
      });
      return next(new AppError('Failed to update password', 500));
    }

    try {
      await writeClearPasswordResetCode(email);
    } catch (error) {
      logger.error(`Failed to invalidate reset code`, { email, error });
    }

    try {
      await mailAccountChangeNotification(email, 'password', account.dataValues.name.split(' ')[0]);
      logger.info(`Password updated successfully`, { email });
    } catch (error) {
      logger.error(`Failed to send password change notification`, { email, error });
    }

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Password reset successful`,
    });
  } catch (error) {
    logger.error(`Unexpected error during password reset`, {
      email: res.locals.email,
      error,
    });
    return next(new AppError('An error occurred during password reset', 500));
  }
};
