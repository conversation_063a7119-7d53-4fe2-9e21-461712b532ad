import { Request, Response } from 'express';
import { writeNewEmail, writeNewName, writeNew<PERSON>assword, writeEmailVerificationCode } from '../../dals';
import { hashPassword, mailAccountChangeNotification } from '../../helpers';
import { generate6DigitCode } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { mailEmailVerificationCode } from '../../helpers';

export const updateAccountController = async (_req: Request, res: Response) => {
  let account: any = res.locals.account;
  let email: string = account.dataValues.email;

  let accountUpdateReturnData: any;

  if (res.locals.attribute === 'name') {
    accountUpdateReturnData = await writeNewName(email, res.locals.value);
  } else if (res.locals.attribute === 'email') {
    accountUpdateReturnData = await writeNewEmail(email, res.locals.value);
    if (!accountUpdateReturnData.success) {
      return res.status(400).json({
        success: false,
        message: accountUpdateReturnData.message,
      });
    }

    const { code, hashedCode, expiresAt } = generate6DigitCode(24 * 60); // 24 hours

    const verificationResult = await writeEmailVerificationCode(res.locals.value, hashedCode, expiresAt);

    if (!verificationResult.success) {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Failed to generate verification code. Please contact ${Var.app.contact.email}`,
      });
    }

    let mailEmailVerificationCodeReturnData: any = await mailEmailVerificationCode(account.dataValues.name.split(' ')[0], res.locals.value, code);
    if (!mailEmailVerificationCodeReturnData.success) {
      return res.status(400).json({
        success: false,
        message: mailEmailVerificationCodeReturnData.message,
      });
    }
    console.log(mailEmailVerificationCodeReturnData.message);
  } else if (res.locals.attribute === 'password') {
    let newHashedPassword: string = await hashPassword(res.locals.value);
    accountUpdateReturnData = await writeNewPassword(email, newHashedPassword);
  }

  console.log(accountUpdateReturnData.message);
  if (!accountUpdateReturnData.success) {
    console.log(`${Var.app.emoji.failure} Failed to update ${res.locals.attribute}`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Failed to update ${res.locals.attribute}`,
    });
  }

  let mailName: string = '';
  if (res.locals.attribute === 'name') {
    mailName = res.locals.value.split(' ')[0];
  } else {
    mailName = account.dataValues.name.split(' ')[0];
  }
  let mailAccountChangeNotificationReturnData: any = await mailAccountChangeNotification(email, res.locals.attribute, mailName);
  console.log(mailAccountChangeNotificationReturnData.message);

  return res.status(200).json({
    success: accountUpdateReturnData.success,
    message: accountUpdateReturnData.message,
    payload: {},
  });
};
