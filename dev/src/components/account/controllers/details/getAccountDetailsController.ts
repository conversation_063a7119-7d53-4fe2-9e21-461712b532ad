import { Request, Response } from 'express';
import { Var } from '../../../../global/var';

export const getAccountDetailsController = async (_req: Request, res: Response) => {
  let account: any = res.locals.account;

  let responseData: any = {
    name: account.dataValues.name,
    email: account.dataValues.email,
    isEmailVerified: account.dataValues.is_email_verified,
    isSessionActive: true,
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Account details fetched`,
    payload: responseData,
  });
};
