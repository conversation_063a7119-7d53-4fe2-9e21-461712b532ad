import { Request, Response } from 'express';
import { login } from '../../helpers';
import { readAccountByEmail, writeNewAccountFromGoogleOauth, writeGoogleOauthStatus } from '../../dals';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { TokenPayload } from 'google-auth-library';

export const googleOauthController = async (req: Request, res: Response) => {
  const verifiedPayload = res.locals.googleVerifiedPayload as TokenPayload | undefined;

  if (!verifiedPayload) {
    logger.error('Google OAuth Controller: Verified payload missing from res.locals. This should not happen if middleware is correctly configured.');
    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Internal server error processing Google login. Verified payload not found.`,
    });
  }

  const email: string = verifiedPayload.email!;
  const isEmailVerified: boolean = verifiedPayload.email_verified!;
  const givenName: string = verifiedPayload.given_name || '';
  const familyName: string = verifiedPayload.family_name || '';
  // const googleUserId: string = verifiedPayload.sub!; // Google User ID, available if needed

  
  if (isEmailVerified === false) {
    logger.warn('Google OAuth: User attempting to login with an unverified Google email address.', { email });
    return res.status(403).json({
        success: false,
        message: `${Var.app.emoji.failure} Your Google email address is not verified. Please verify it with Google first.`,
    });
  }

  const account = await readAccountByEmail(email);
  let accountId: string = '';
  const name: string = `${givenName} ${familyName}`.trim() || email.split('@')[0]; // Fallback name if given/family names are empty

  if (!account) {
    logger.info('Creating new account for Google OAuth user', { email });
    const newAccount = await writeNewAccountFromGoogleOauth(name, email, isEmailVerified, true);

    if (!newAccount.success || !newAccount.payload?.id) {
      logger.error('Failed to create account from Google OAuth', {
        email,
        message: newAccount.message,
      });
      return res.status(500).json({
        success: false,
        message: newAccount.message || `${Var.app.emoji.failure} Failed to create account using Google OAuth. Please try again.`, 
      });
    }
    accountId = newAccount.payload.id;
    logger.info(`New account created successfully via Google OAuth for ${email}, accountId: ${accountId}`);
  } else {
    accountId = account.dataValues.id;
    if (!account.dataValues.is_google_oauth_linked) {
      logger.info('Linking existing account with Google OAuth', { email, accountId });
      const updatedAccountData = await writeGoogleOauthStatus(email); // Sets Google OAuth status to true

      if (!updatedAccountData.success) {
        logger.error('Failed to link existing account with Google OAuth', {
          email,
          accountId,
          message: updatedAccountData.message,
        });
      }
    }
    if (account.dataValues.is_email_verified !== isEmailVerified) {
        logger.info(`Updating email verification status for account ${accountId} based on Google OAuth`, { isEmailVerified });
    }
  }

  await login(req, res, accountId); 

  const responseData = {
    name: name,
    email: email,
    isEmailVerified: isEmailVerified,
    isSessionActive: true, 
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Logged in successfully via Google.`, 
    payload: responseData,
  });
};
