import { Request, Response, NextFunction } from 'express';
import { writeEmailVerificationCode, writeEmailVerificationStatus, readAccountByEmail } from '../../dals';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';
import { verify6DigitCode, isTokenExpired } from '../../../security/helpers';

export const verifyEmailController = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, emailVerificationCode } = res.locals;

    logger.info(`Email verification attempt`, { email });

    const account = await readAccountByEmail(email);
    if (!account) {
      logger.warn(`Email verification failed: Account not found`, { email });
      return res.status(404).json({
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
      });
    }

    const storedHashedCode = account.dataValues.email_verification_code;
    if (!storedHashedCode) {
      logger.warn(`Email verification failed: No verification code found`, { email });
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} No verification code found. Please request a new code.`,
      });
    }

    const isValidCode = verify6DigitCode(emailVerificationCode, storedHashedCode);
    if (!isValidCode) {
      logger.warn(`Email verification failed: Invalid 6-digit verification code`, {
        email,
        attemptedCode: emailVerificationCode.substring(0, 3) + '***',
      });
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid verification code`,
      });
    }

    const expiryTimestamp = account.dataValues.email_verification_code_timestamp;
    if (!expiryTimestamp || isTokenExpired(new Date(expiryTimestamp))) {
      logger.warn(`Email verification failed: Code expired`, {
        email,
        expiryTimestamp,
      });
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Verification code has expired. Please request a new code.`,
      });
    }
    if (!account.dataValues.is_email_verified) {
      const verificationResult = await writeEmailVerificationStatus(account.dataValues.email, true);

      if (!verificationResult.success) {
        logger.error(`Failed to update email verification status`, {
          email,
          error: verificationResult.payload,
        });
        return next(new AppError('Failed to verify email. Please try again later.', 500));
      }
      logger.info(`Email verified successfully`, { email });
    } else {
      logger.info(`Email already verified`, { email });
    }

    try {
      await writeEmailVerificationCode(account.dataValues.email, null, null);
      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} Email verified successfully`,
        payload: {
          email: account.dataValues.email,
          isEmailVerified: true,
        },
      });
    } catch (error) {
      logger.error(`Failed to clear verification code`, { email, error });
      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} Email verified successfully`,
        payload: {
          email: account.dataValues.email,
          isEmailVerified: true,
        },
      });
    }
  } catch (error) {
    logger.error(`Unexpected error during email verification`, {
      email: res.locals.email,
      error,
    });
    return next(new AppError('An error occurred during email verification', 500));
  }
};
