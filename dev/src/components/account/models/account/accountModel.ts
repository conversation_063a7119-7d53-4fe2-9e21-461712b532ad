import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../../global/var';

/**
 * Account Model Definition
 *
 * This model represents user accounts in the system with comprehensive
 * authentication, security, and account management features.
 *
 * Security Features:
 * - UUID primary keys for non-enumerable identifiers
 * - Argon2id password hashing (handled in application layer)
 * - Email verification system with time-based codes
 * - Password reset with secure token system
 * - Account locking mechanism for brute force protection
 * - Login attempt tracking and rate limiting
 * - OAuth integration support (Google)
 *
 * Data Privacy:
 * - Passwords are hashed using Argon2id before storage
 * - Verification codes are hashed before storage
 * - Sensitive operations are logged for audit trails
 * - GDPR compliance through account deletion tracking
 */

const modelName: string = 'account';

const modelAttributes: ModelAttributes = {
  // Flexible metadata storage for future extensibility
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Flexible JSON storage for account metadata and future features',
  },

  // Primary identifier - UUID for security and non-enumeration
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
    comment: 'Primary account identifier - UUID for security',
  },

  // Basic account information
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'User display name - required for personalization',
  },

  // Email - unique identifier and communication channel
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { isEmail: true },
    comment: 'User email address - unique identifier and primary communication channel',
  },

  // Password storage - hashed using Argon2id
  password: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Argon2id hashed password - null for OAuth-only accounts',
  },

  // Email verification system
  is_email_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Email verification status - required for full account access',
  },
  email_verification_code: {
    type: DataTypes.STRING,
    comment: 'Hashed 6-digit email verification code',
  },
  email_verification_code_timestamp: {
    type: DataTypes.DATE,
    comment: 'Expiration timestamp for email verification code',
  },

  // Password reset system
  password_reset_code: {
    type: DataTypes.STRING,
    comment: 'Hashed 6-digit password reset code',
  },
  password_reset_code_timestamp: {
    type: DataTypes.DATE,
    comment: 'Expiration timestamp for password reset code',
  },

  // OAuth integration
  is_google_oauth_linked: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Google OAuth integration status',
  },

  // Security and account protection
  is_locked: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Account lock status for brute force protection',
  },
  lock_expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Automatic lock expiration timestamp',
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last successful login timestamp for monitoring',
  },
  login_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Failed login attempt counter for rate limiting',
  },
};

/**
 * Model configuration options
 *
 * These options configure how Sequelize handles the model:
 * - tableName: Explicit table name to avoid pluralization issues
 * - underscored: Use snake_case for database columns (created_at, updated_at)
 * - freezeTableName: Prevent Sequelize from pluralizing table names
 * - timestamps: Automatically managed by Sequelize (created_at, updated_at)
 */
const modelOptions: ModelOptions = {
  tableName: 'accounts',
  underscored: true, // Use snake_case for column names
  freezeTableName: true, // Prevent automatic pluralization
  // timestamps: true is default - adds created_at and updated_at
};

/**
 * Export the configured account model
 *
 * This model is used throughout the application for:
 * - User authentication and authorization
 * - Account management operations
 * - Security and audit logging
 * - OAuth integration
 * - Email verification and password reset flows
 */
export const accountModel = Sequelize.define(modelName, modelAttributes, modelOptions);
