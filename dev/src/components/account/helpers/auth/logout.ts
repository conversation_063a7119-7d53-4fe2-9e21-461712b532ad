import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';

export const logout = (req: Request, res: Response, next: NextFunction) => {
  try {
    const accountId = req.session?.accountId;

    req.session!.destroy((error: Error) => {
      if (error) {
        logger.error('Error during logout', { error, accountId });
        return next(new AppError('Failed to logout', 500));
      }
      // Clear session cookie
      res.clearCookie(Var.node.express.session.name, {
        path: '/',
        httpOnly: true,
        secure: Var.node.env === 'prod',
        sameSite: 'lax',
        domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
      });

      // Clear CSRF cookie
      const csrfCookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';
      res.clearCookie(csrfCookieName, {
        path: '/',
        httpOnly: true,
        secure: Var.node.env === 'prod',
        sameSite: 'lax',
        domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
      });

      logger.info('User logged out successfully, session and CSRF cookies cleared', { accountId });

      return res.status(200).json({
        success: true,
        message: `${Var.app.emoji.success} Logged out successfully`,
        payload: { isSessionActive: false },
      });
    });
  } catch (error) {
    logger.error('Unexpected error during logout', { error });
    return next(new AppError('An error occurred during logout', 500));
  }
};
