import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeEmailVerificationStatus = async (email: string, isEmailVerified: boolean) => {
  try {
    const [updatedRowsCount] = await accountModel.update(
      { is_email_verified: isEmailVerified },
      {
        where: { email },
      },
    );

    if (updatedRowsCount === 0) {
      logger.warn('Email verification status update failed: Account not found', { email, isEmailVerified });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
        payload: null,
      };
    }

    logger.info('Email verification status updated successfully', { email, isEmailVerified });
    return {
      success: true,
      message: `${Var.app.emoji.success} Email verification status saved`,
      payload: { email, isEmailVerified },
    };
  } catch (error) {
    logger.error('Failed to update email verification status', { email, isEmailVerified, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not save email verification status. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
