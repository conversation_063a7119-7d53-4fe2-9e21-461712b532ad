import { Var } from '../../../../global/var';
import { accountModel } from '../../models';
import { logger } from '../../../../global/services';

export const writeGoogleOauthStatus = async (email: string) => {
  try {
    const [updatedRowsCount] = await accountModel.update(
      {
        is_email_verified: true,
        email_verification_code: null,
        is_google_oauth_linked: true,
      },
      {
        where: { email },
      },
    );

    if (updatedRowsCount === 0) {
      logger.warn('Google OAuth status update failed: Account not found', { email });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Account not found`,
        payload: null,
      };
    }

    logger.info('Google OAuth status updated successfully', { email });
    return {
      success: true,
      message: `${Var.app.emoji.success} Google OAuth status updated`,
      payload: { email, isGoogleOauthLinked: true },
    };
  } catch (error) {
    logger.error('Failed to update Google OAuth status', { email, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to update Google OAuth status. Please contact ${Var.app.contact.email}`,
      payload: error,
    };
  }
};
