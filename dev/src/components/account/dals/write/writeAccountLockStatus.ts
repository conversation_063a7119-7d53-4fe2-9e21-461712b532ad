import { accountModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const writeAccountLockStatus = async (accountId: string, isLocked: boolean, lockExpiresAt: Date | null) => {
  let isSuccessful: boolean = false;
  let returnData: any;

  try {
    const updateData: any = {
      is_locked: isLocked,
      lock_expires_at: isLocked ? lockExpiresAt : null,
    };

    const [updatedRowsCount] = await accountModel.update(updateData, {
      where: {
        id: accountId,
      },
    });

    if (updatedRowsCount > 0) {
      isSuccessful = true;
      returnData = {
        accountId,
        isLocked,
        lockExpiresAt: lockExpiresAt ? lockExpiresAt.toISOString() : null,
      };

      logger.info(`Account ${isLocked ? 'locked' : 'unlocked'} successfully`, { accountId, lockExpiresAt });
    } else {
      logger.warn(`Failed to update account lock status: Account not found`, { accountId });
      returnData = new Error('Account not found');
    }
  } catch (error) {
    logger.error(`Error updating account lock status`, { accountId, error });
    returnData = error;
  }

  return {
    success: isSuccessful,
    message: isSuccessful ? `${Var.app.emoji.success} Account ${isLocked ? 'locked' : 'unlocked'} successfully` : `${Var.app.emoji.failure} Failed to update account lock status`,
    payload: returnData,
  };
};
