import { accountModel } from '../../models';

/**
 * Data Access Layer function to retrieve an account by email address
 *
 * This function is used primarily for:
 * - User authentication during login
 * - Email verification processes
 * - Password reset operations
 * - Checking if an email is already registered
 *
 * @param email - The email address to search for (case-sensitive)
 * @returns Promise<Account | null> - The account object if found, null otherwise
 *
 * @example
 * const account = await readAccountByEmail('<EMAIL>');
 * if (account) {
 *   console.log('Account found:', account.dataValues.name);
 * }
 */
export const readAccountByEmail = async (email: string) => {
  // Query the database for an account with the specified email
  // Note: Email comparison is case-sensitive in the database
  const account = await accountModel.findOne({
    where: {
      email: email,
    },
  });

  return account;
};
