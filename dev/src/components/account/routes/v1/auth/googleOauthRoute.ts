import { Router } from 'express';
import { BlockLoggedInAccount } from '../../../../../global/middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { googleOauthController } from '../../../controllers';
import { validateGoogleOauthPayload, formatGoogleOauthPayload } from '../../../middlewares';

export const googleOauthRoute = Router();

googleOauthRoute.post(`${GenerateApiVersionPath()}auth/google`, BlockLoggedInAccount, validateGoogleOauthPayload, formatGoogleOauthPayload, googleOauthController);
