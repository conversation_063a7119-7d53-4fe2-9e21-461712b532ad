import { Router } from 'express';

import { BlockLoggedInAccount } from '../../../../../global/middlewares';
import { AuthLimiter, ProgressiveDelayForFailedLogin } from '../../../../security';
import { validateLoginPayload, formatLoginPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { loginController } from '../../../controllers';

export const loginRoute = Router();

loginRoute.post(
  `${GenerateApiVersionPath()}auth/login`,
  AuthLimiter,
  BlockLoggedInAccount,
  validateLoginPayload,
  formatLoginPayload,
  ProgressiveDelayForFailedLogin,
  loginController,
);
