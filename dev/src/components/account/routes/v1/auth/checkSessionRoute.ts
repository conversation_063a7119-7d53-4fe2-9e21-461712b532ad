import { Router } from 'express';
import { BlockLoggedOutAccount } from '../../../../../global/middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { checkSessionController } from '../../../controllers';

export const checkSessionRoute = Router();

checkSessionRoute.get(`${GenerateApiVersionPath()}auth/session-check`, BlockLoggedOutAccount, checkSessionController);
