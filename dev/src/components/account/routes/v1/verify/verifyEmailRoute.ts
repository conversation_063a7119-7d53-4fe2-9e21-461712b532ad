import { Router } from 'express';
import { ExtractOriginFromRequest, BlockRequestByOrigin, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../../global/middlewares';
import { ApiLimiter } from '../../../../security';
import { validateEmailVerificationPayload, formatEmailVerificationPayload } from '../../../middlewares';
import { GenerateApiVersionPath } from '../../../../../global/helpers';
import { verifyEmailController } from '../../../controllers';

export const verifyEmailRoute = Router();

verifyEmailRoute.post(
  `${GenerateApiVersionPath()}account/email/verify`,
  ApiLimiter,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  validateEmailVerificationPayload,
  formatEmailVerificationPayload,
  verifyEmailController,
);
