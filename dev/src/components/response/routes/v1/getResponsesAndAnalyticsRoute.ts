import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateGetSurveyPayload, formatGetSurveyPayload } from '../../../survey/middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getResponsesAndAnalyticsController } from '../../controllers';

export const getResponsesAndAnalyticsRoute = Router();

getResponsesAndAnalyticsRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId/responses/analytics`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateGetSurveyPayload,
  formatGetSurveyPayload,
  getResponsesAndAnalyticsController,
);
