import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateDiscardResponsePayload } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { discardResponseController } from '../../controllers';

export const discardResponseRoute = Router();

discardResponseRoute.put(
  `${GenerateApiVersionPath()}responses/:responseId/discard`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateDiscardResponsePayload,
  discardResponseController,
);
