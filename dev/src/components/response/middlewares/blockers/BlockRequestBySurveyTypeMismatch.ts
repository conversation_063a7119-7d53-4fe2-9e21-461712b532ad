import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

/**
 * Middleware to block requests when the submitted survey type doesn't match the survey's actual type
 * Ensures survey type consistency between payload and database/cache
 */
export const BlockRequestBySurveyTypeMismatch = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { surveyType } = res.locals.validatedPayload;
    const survey = res.locals.survey;

    // Check if the submitted survey type matches the survey's actual type
    if (surveyType !== survey.type) {
      console.log(`${Var.app.emoji.failure} Survey type mismatch: submitted '${surveyType}', expected '${survey.type}' for survey ${survey.public_key}`);
      return res.status(400).json({
        success: false,
        message: `Survey type mismatch. Expected '${survey.type}', but received '${surveyType}'`,
      });
    }

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Survey type validation passed: ${surveyType} for survey ${survey.public_key}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in BlockRequestBySurveyTypeMismatch:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating survey type',
    });
  }
};
