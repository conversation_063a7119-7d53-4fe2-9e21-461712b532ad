import { Parser } from 'json2csv';
import { responseModel } from '../../../response/models';
import { Var } from '../../../../global/var';

// SECURITY HARDENING: CSV injection protection
const sanitizeForCSV = (value: any): string => {
  if (value === null || value === undefined) return '';

  const stringValue = String(value);

  // Prevent CSV injection by escaping dangerous characters
  if (
    stringValue.startsWith('=') ||
    stringValue.startsWith('+') ||
    stringValue.startsWith('-') ||
    stringValue.startsWith('@') ||
    stringValue.startsWith('\t') ||
    stringValue.startsWith('\r')
  ) {
    return `'${stringValue}`;
  }

  return stringValue;
};

export const exportResponsesAsCsv = async (surveyId: string, accountId: string, includeDiscarded: boolean = false) => {
  try {
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId,
    };

    if (!includeDiscarded) {
      whereConditions.is_discarded = false;
    }

    const responses = await responseModel.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']],
    });

    if (responses.length === 0) {
      return {
        success: false,
        message: `${Var.app.emoji.warning} No responses found for this survey`,
        payload: null,
      };
    }

    // SECURITY HARDENING: Apply CSV sanitization to all exported data
    const processedResponses = responses.map((response: any) => {
      const flatResponse: any = {
        id: sanitizeForCSV(response.id),
        created_at: sanitizeForCSV(response.createdAt),
        is_discarded: sanitizeForCSV(response.is_discarded),
        discard_reason: sanitizeForCSV(response.discard_reason),
      };

      Object.entries(response.response_data || {}).forEach(([key, value]) => {
        const sanitizedValue = typeof value === 'object' ? JSON.stringify(value) : value;
        flatResponse[`response_${key}`] = sanitizeForCSV(sanitizedValue);
      });

      Object.entries(response.respondent_info || {}).forEach(([key, value]) => {
        const sanitizedValue = typeof value === 'object' ? JSON.stringify(value) : value;
        flatResponse[`respondent_${key}`] = sanitizeForCSV(sanitizedValue);
      });

      return flatResponse;
    });

    const fields = Object.keys(processedResponses[0]);
    const opts = { fields };
    const parser = new Parser(opts);
    const csv = parser.parse(processedResponses);

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses exported`,
      payload: {
        filename: `responses_${surveyId}_${new Date().toISOString()}.csv`,
        csv,
      },
    };
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error exporting responses:`, error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not export responses`,
      payload: error,
    };
  }
};
