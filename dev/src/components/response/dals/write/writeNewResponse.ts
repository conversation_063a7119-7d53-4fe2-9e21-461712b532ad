import { responseModel } from '../../models';
import { Var } from '../../../../global/var';

export const writeNewResponse = async (
  surveyPublicKey: string,
  surveyId: string,
  accountId: string,
  responseData: Record<string, any>,
  respondentDetails: Record<string, any>,
  meta: Record<string, any>,
) => {
  let isSuccessful: boolean = false;
  let returnData: any;

  await responseModel
    .create({
      survey_public_key: surveyPublicKey,
      survey_id: surveyId,
      account_id: accountId,
      response_data: responseData,
      respondent_details: respondentDetails || {},
      meta: meta || {},
    })
    .then((newResponse: any) => {
      isSuccessful = true;
      returnData = {
        id: newResponse.dataValues.id,
        survey_public_key: newResponse.dataValues.survey_public_key,
        survey_id: newResponse.dataValues.survey_id,
        account_id: newResponse.dataValues.account_id,
        response_data: newResponse.dataValues.response_data,
        respondent_details: newResponse.dataValues.respondent_details,
        meta: newResponse.dataValues.meta,
        created_at: newResponse.dataValues.created_at,
      };
    })
    .catch((err: any) => {
      returnData = err;
    });

  return {
    success: isSuccessful,
    message: isSuccessful ? `${Var.app.emoji.success} Response saved successfully` : `${Var.app.emoji.failure} Failed to save response`,
    payload: returnData,
  };
};
