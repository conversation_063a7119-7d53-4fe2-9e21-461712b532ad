import { Request, Response } from 'express';
import { writeNewResponse } from '../dals';
import { MetaDataInterface } from '../../../global/interfaces';
import { Var } from '../../../global/var';
import { Sequelize } from '../../../global/var';
import { surveyModel } from '../../survey/models';
import { writeSurveyResponseCount } from '../../survey/dals';

export const submitResponseController = async (req: Request, res: Response) => {
  // Start a transaction that will be used for both response creation and survey update
  const transaction = await Sequelize.transaction();

  try {
    let survey = res.locals.survey;
    let fromCache = res.locals.fromCache;

    if (Var.node.env === 'dev' && survey) {
      console.log(`${Var.app.emoji.success} Using survey ${fromCache ? 'from cache' : 'from database'}: ${survey.public_key}`);
    }

    if (!survey) {
      return res.status(404).json({
        success: false,
        message: `${Var.app.emoji.failure} Survey not found`,
      });
    }

    let { responseData, respondentDetails, userAgent, completionTime } = res.locals.validatedPayload;

    let ipAddress = res.locals.clientIPAddress || req.ip || '127.0.0.1';
    let country = res.locals.country || undefined;
    let region = res.locals.region || undefined;
    let origin = res.locals.origin || req.get('origin') || undefined;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Metadata extracted:`, {
        ipAddress,
        country: country || 'unknown',
        region: region || 'unknown',
        origin: origin || 'unknown',
        userAgent: userAgent ? 'present' : 'unknown',
        completionTime: completionTime ? `${completionTime}ms` : 'not provided',
      });
    }

    // Ensure userAgent is an object, not a string
    let userAgentObj = userAgent;
    if (typeof userAgent === 'string') {
      try {
        userAgentObj = JSON.parse(userAgent);
      } catch (e) {
        userAgentObj = { raw: userAgent };
      }
    } else if (!userAgentObj) {
      userAgentObj = {};
    }

    // Create the meta object according to the MetaDataInterface
    let meta: MetaDataInterface = {
      origin: origin || '',
      ipAddress: ipAddress || '',
      country: country || '',
      region: region || '',
      completionTime: completionTime || 0,
      userAgent: userAgentObj,
    };

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Meta object prepared:`, meta);
    }

    // Get the survey public key from the survey object or from the validated payload
    let surveyPublicKey = survey.public_key || res.locals.validatedPayload.surveyPublicKey;

    if (!surveyPublicKey) {
      console.log(`${Var.app.emoji.failure} Survey public key is missing`);
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Survey public key is missing`,
      });
    }

    let dbSurvey = await surveyModel.findOne({
      attributes: ['id', 'account_id', 'public_key', 'title', 'type', 'distribution', 'response_count'],
      where: { public_key: surveyPublicKey },
    });

    if (!dbSurvey) {
      console.log(`${Var.app.emoji.failure} Survey not found in database for public key: ${surveyPublicKey}`);
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: `${Var.app.emoji.failure} Survey not found`,
      });
    }

    const surveyId = dbSurvey.get('id') as string;
    const accountId = dbSurvey.get('account_id') as string;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Found survey ID: ${surveyId} for public key: ${surveyPublicKey}`);
      console.log(`${Var.app.emoji.success} Found account ID: ${accountId} for public key: ${surveyPublicKey}`);
    }

    // Write the response to the database
    let result = await writeNewResponse(surveyPublicKey, surveyId, accountId, responseData || {}, respondentDetails || {}, meta);

    if (!result.success) {
      console.log(`${Var.app.emoji.failure} Failed to write response: ${result.message}`);
      await transaction.rollback();
      return res.status(500).json({
        success: false,
        message: `${Var.app.emoji.failure} Failed to submit response`,
        error: result.payload?.message || result.message,
      });
    }

    let incrementResult = await writeSurveyResponseCount(surveyId, transaction);

    if (!incrementResult.success) {
      console.warn(`${Var.app.emoji.warning} Failed to increment response count: ${incrementResult.message}`);
    }

    await transaction.commit();

    return res.status(201).json({
      success: true,
      message: `${Var.app.emoji.success} Response submitted successfully`,
      payload: {
        responseId: result.payload.id,
      },
    });
  } catch (error) {
    await transaction.rollback();

    console.error(`${Var.app.emoji.failure} Error in submitResponseController:`, error);
    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Failed to submit response`,
      error: Var.node.env === 'dev' ? (error as Error).message : undefined,
    });
  }
};
