import { waitlistModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

/**
 * Data Access Layer function to create a new waitlist entry
 *
 * This function handles the creation of new waitlist entries when users
 * submit their email addresses through the website widget.
 *
 * Security considerations:
 * - Email validation is handled at the model level
 * - Duplicate emails are prevented by unique constraint
 * - Additional metadata can be stored for analytics
 *
 * @param email - The user's email address (must be valid and unique)
 * @param meta - Optional metadata object (source, location, etc.)
 * @returns Promise<{success: boolean, message: string, payload?: object}> - Operation result
 *
 * @example
 * const result = await writeNewWaitlistEntry('<EMAIL>', { source: 'homepage', location: 'above-footer' });
 * if (result.success) {
 *   console.log('Waitlist entry created:', result.payload.id);
 * }
 */
export const writeNewWaitlistEntry = async (email: string, meta: object = {}) => {
  try {
    // Create new waitlist entry in the database
    const newEntry = await waitlistModel.create({
      email,
      meta,
    });

    // Extract the created entry data
    const entryData = {
      id: newEntry.dataValues.id,
      email: newEntry.dataValues.email,
      meta: newEntry.dataValues.meta,
      created_at: newEntry.dataValues.created_at,
    };

    logger.info('New waitlist entry created', {
      id: entryData.id.substring(0, 8) + '...',
      email: email.substring(0, 3) + '***@' + email.split('@')[1],
      hasMetadata: Object.keys(meta).length > 0,
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] New waitlist entry: ${email.substring(0, 3)}***@${email.split('@')[1]}`);
    }

    return {
      success: true,
      message: `${Var.app.emoji.success} Email added to waitlist`,
      payload: entryData,
    };
  } catch (error: any) {
    // Handle duplicate email error specifically
    if (error.name === 'SequelizeUniqueConstraintError') {
      logger.warn('Duplicate waitlist entry attempt', {
        email: email.substring(0, 3) + '***@' + email.split('@')[1],
        error: 'Email already exists',
      });

      // DEBUG: Console output in development only
      if (Var.node.env === 'dev') {
        console.log(`[DEV] Duplicate waitlist entry: ${email.substring(0, 3)}***@${email.split('@')[1]}`);
      }

      return {
        success: false,
        message: `${Var.app.emoji.warning} Email already registered for waitlist`,
      };
    }

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      logger.warn('Waitlist entry validation failed', {
        email: email.substring(0, 3) + '***@' + email.split('@')[1],
        error: error.errors?.[0]?.message || 'Validation failed',
      });

      // DEBUG: Console output in development only
      if (Var.node.env === 'dev') {
        console.log(`[DEV] Waitlist validation error: ${error.errors?.[0]?.message}`);
      }

      return {
        success: false,
        message: `${Var.app.emoji.failure} Invalid email address`,
      };
    }

    // Handle other database errors
    logger.error('Failed to create waitlist entry', {
      email: email.substring(0, 3) + '***@' + email.split('@')[1],
      error: error.message,
      stack: error.stack,
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.error(`[DEV] Waitlist creation error:`, error);
    }

    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to add email to waitlist. Please try again.`,
    };
  }
};
