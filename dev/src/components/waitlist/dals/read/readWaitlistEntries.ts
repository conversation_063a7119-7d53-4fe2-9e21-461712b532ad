import { waitlistModel } from '../../models';
import { logger } from '../../../../global/services';

/**
 * Data Access Layer function to read waitlist entries
 *
 * This function retrieves waitlist entries from the database.
 * Useful for admin purposes to see who has joined the waitlist.
 *
 * @param limit - Maximum number of entries to return (default: 100)
 * @param offset - Number of entries to skip for pagination (default: 0)
 * @returns Promise<Array> - Array of waitlist entries
 *
 * @example
 * const entries = await readWaitlistEntries(50, 0);
 * console.log(`Found ${entries.length} waitlist entries`);
 */
export const readWaitlistEntries = async (limit: number = 100, offset: number = 0) => {
  try {
    const entries = await waitlistModel.findAll({
      attributes: ['id', 'email', 'meta', 'created_at'],
      order: [['created_at', 'DESC']], // Most recent first
      limit,
      offset,
    });

    logger.debug('Waitlist entries retrieved', {
      count: entries.length,
      limit,
      offset,
    });

    return entries;
  } catch (error: any) {
    logger.error('Failed to read waitlist entries', {
      error: error.message,
      stack: error.stack,
    });

    throw error;
  }
};

/**
 * Data Access Layer function to count total waitlist entries
 *
 * This function returns the total count of waitlist entries.
 * Useful for pagination and analytics.
 *
 * @returns Promise<number> - Total count of waitlist entries
 *
 * @example
 * const total = await countWaitlistEntries();
 * console.log(`Total waitlist entries: ${total}`);
 */
export const countWaitlistEntries = async (): Promise<number> => {
  try {
    const count = await waitlistModel.count();

    logger.debug('Waitlist entries counted', { count });

    return count;
  } catch (error: any) {
    logger.error('Failed to count waitlist entries', {
      error: error.message,
      stack: error.stack,
    });

    throw error;
  }
};
