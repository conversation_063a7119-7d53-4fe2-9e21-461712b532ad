import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

/**
 * Joi schema for waitlist payload validation
 *
 * This schema validates the email input for waitlist entries.
 * It ensures the email is valid, properly formatted, and within reasonable limits.
 */
const waitlistPayloadSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } }) // Allow all TLDs for flexibility
    .min(5) // Minimum reasonable email length
    .max(128) // Maximum email length to prevent abuse
    .lowercase() // Convert to lowercase for consistency
    .trim() // Remove whitespace
    .required()
    .messages({
      'string.email': 'Please enter a valid email address',
      'string.min': 'Email address is too short',
      'string.max': 'Email address is too long',
      'any.required': 'Email address is required',
    }),

  // Optional metadata field for tracking source and location
  meta: Joi.object({
    source: Joi.string().optional().allow(''),
    location: Joi.string().optional().allow(''),
  })
    .optional()
    .default({}),
}).options({
  stripUnknown: true, // Remove unknown fields for security
  abortEarly: false, // Validate all fields to provide comprehensive feedback
});

/**
 * Middleware function to validate waitlist payload
 *
 * This middleware validates the incoming request body for waitlist entries.
 * It ensures the email is valid and properly formatted before processing.
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const validateWaitlistPayload = (req: Request, res: Response, next: NextFunction) => {
  const { error, value } = waitlistPayloadSchema.validate(req.body);

  if (error) {
    const errorMessage = error.details[0].message;

    logger.warn('Waitlist payload validation failed', {
      error: errorMessage,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      body: req.body,
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] Waitlist validation failed: ${errorMessage}`);
    }

    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${errorMessage}`,
    });
  }

  // Store validated and sanitized data in req.body
  req.body = value;

  logger.debug('Waitlist payload validation passed', {
    email: value.email.substring(0, 3) + '***@' + value.email.split('@')[1],
    hasMetadata: Object.keys(value.meta || {}).length > 0,
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`[DEV] Waitlist validation passed for: ${value.email.substring(0, 3)}***@${value.email.split('@')[1]}`);
  }

  next();
};
