import { Request, Response, NextFunction } from 'express';

/**
 * Middleware function to format waitlist payload
 *
 * This middleware extracts and formats the validated data from the request body
 * and stores it in res.locals for use by the controller.
 *
 * The validation middleware has already sanitized the data, so this middleware
 * simply extracts the values and stores them in the expected format.
 *
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const formatWaitlistPayload = (req: Request, res: Response, next: NextFunction) => {
  const { email, meta } = req.body;

  // Store formatted data in res.locals for controller access
  res.locals.email = email.trim().toLowerCase();
  res.locals.meta = meta || {};

  // Add request metadata for analytics (preserving user-provided source and location)
  res.locals.meta = {
    source: res.locals.meta.source || 'unknown',
    location: res.locals.meta.location || 'unknown',
    // Server-added metadata
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    origin: res.locals.origin || req.headers.origin,
    country: res.locals.country || 'unknown',
    timestamp: new Date().toISOString(),
  };

  next();
};
