import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../../global/var';

/**
 * Waitlist Model
 * 
 * This model represents the waitlist table for storing email addresses
 * of users interested in the product before launch or during beta.
 * 
 * Features:
 * - UUID primary key for security and non-enumeration
 * - Unique email constraint to prevent duplicates
 * - JSONB meta field for flexible data storage
 * - Automatic timestamps for tracking when users joined
 * - Email validation at database level
 * 
 * Security considerations:
 * - Email addresses are validated using Sequelize's built-in email validator
 * - Unique constraint prevents duplicate entries
 * - Meta field allows storing additional context (referrer, source, etc.)
 */

const modelName: string = 'waitlist';

const modelAttributes: ModelAttributes = {
  // Flexible metadata storage for future extensibility
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Flexible JSON storage for waitlist metadata (source, referrer, etc.)',
  },

  // Primary identifier - UUID for security and non-enumeration
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
    comment: 'Primary waitlist entry identifier - UUID for security',
  },

  // Email - unique identifier for waitlist entries
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: { 
      isEmail: true,
      len: [5, 128], // Reasonable email length limits
    },
    comment: 'User email address - unique identifier for waitlist entry',
  },
};

/**
 * Model configuration options
 *
 * These options configure how Sequelize handles the model:
 * - tableName: Explicit table name to avoid pluralization issues
 * - underscored: Use snake_case for database columns (created_at, updated_at)
 * - freezeTableName: Prevent Sequelize from pluralizing table names
 * - timestamps: Automatically managed by Sequelize (created_at, updated_at)
 * - indexes: Database indexes for performance optimization
 */
const modelOptions: ModelOptions = {
  tableName: 'waitlist',
  underscored: true, // Use snake_case for column names
  freezeTableName: true, // Prevent automatic pluralization
  timestamps: true, // Enable created_at and updated_at
  indexes: [
    {
      fields: ['email'],
      unique: true,
      name: 'waitlist_email_unique_idx',
    },
    {
      fields: ['created_at'],
      name: 'waitlist_created_at_idx',
    },
  ],
};

/**
 * Export the configured waitlist model
 *
 * This model is used for:
 * - Storing email addresses of interested users
 * - Tracking when users joined the waitlist
 * - Preventing duplicate email entries
 * - Storing additional metadata about waitlist entries
 */
export const waitlistModel = Sequelize.define(modelName, modelAttributes, modelOptions);
