import { Router } from 'express';
import { ExtractOriginFromRequest, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../global/middlewares';
import { ApiLimiter } from '../../../security';
import { validateWaitlistPayload, formatWaitlistPayload } from '../../middlewares';
import { addToWaitlistController } from '../../controllers';

export const waitlistRoute = Router();

/**
 * Waitlist Route - POST /waitlist
 *
 * This is a public endpoint that allows website visitors to join the waitlist
 * by submitting their email address. It doesn't require authentication but
 * includes security middleware for protection.
 *
 * Security features:
 * - Rate limiting to prevent abuse
 * - Origin extraction for analytics
 * - IP address and country detection
 * - Input validation and sanitization
 * - CSRF protection is automatically exempted for public endpoints
 *
 * Middleware chain:
 * 1. ExtractOriginFromRequest - Extract and validate request origin
 * 2. ExtractIPAddressFromOrigin - Get real IP address from headers
 * 3. ExtractCountryFromIPAddress - Determine country from IP for analytics
 * 4. ApiLimiter - Rate limiting to prevent spam
 * 5. validateWaitlistPayload - Validate email format and sanitize input
 * 6. formatWaitlistPayload - Format data and add metadata
 * 7. addToWaitlistController - Process the request and save to database
 */
waitlistRoute.post(
  '/waitlist',
  // Security and analytics middleware
  ExtractOriginFromRequest,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,

  // Rate limiting for abuse prevention
  ApiLimiter,

  // Validation and formatting
  validateWaitlistPayload,
  formatWaitlistPayload,

  // Controller
  addToWaitlistController,
);
