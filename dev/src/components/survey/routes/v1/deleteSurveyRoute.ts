import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateDeleteSurveyPayload, formatDeleteSurveyPayload, removeSurveyCache } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { deleteSurveyController } from '../../controllers';

export const deleteSurveyRoute = Router();

deleteSurveyRoute.delete(
  `${GenerateApiVersionPath()}surveys/:surveyId`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateDeleteSurveyPayload,
  formatDeleteSurveyPayload,
  deleteSurveyController,
  removeSurveyCache,
);
