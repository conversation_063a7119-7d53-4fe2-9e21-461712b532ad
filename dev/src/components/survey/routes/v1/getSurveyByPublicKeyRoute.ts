import { Router } from 'express';
import { ExtractOriginFromRequest, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../global/middlewares';
import { validateGetSurveyByPublicKeyPayload, BlockRequestByDistribution, getSurveyFromCache } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getSurveyByPublicKeyController } from '../../controllers';

export const getSurveyByPublicKeyRoute = Router();

getSurveyByPublicKeyRoute.get(
  `${GenerateApiVersionPath()}public-surveys/:publicKey`,
  ExtractOriginFromRequest,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  validateGetSurveyByPublicKeyPayload,
  getSurveyFromCache, // Try cache first for performance
  BlockRequestByDistribution, // Falls back to DB if not in cache
  getSurveyByPublicKeyController,
);
