import { Router } from 'express';

import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateCreateSurveyPayload, formatCreateSurveyPayload, updateSurveyCache } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { createSurveyController } from '../../controllers';

export const createSurveyRoute = Router();

createSurveyRoute.post(
  `${GenerateApiVersionPath()}surveys`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateCreateSurveyPayload,
  formatCreateSurveyPayload,
  createSurveyController,
  updateSurveyCache,
);
