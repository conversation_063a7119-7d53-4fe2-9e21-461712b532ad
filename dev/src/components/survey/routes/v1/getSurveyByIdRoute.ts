import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  ValidateSurveyOwnership,
} from '../../../../global/middlewares';
import { ApiLimiter } from '../../../security';
import { validateGetSurveyPayload } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getSurveyByIdController } from '../../controllers';

export const getSurveyByIdRoute = Router();

getSurveyByIdRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId`,
  // Apply rate limiting first
  ApiLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Authentication
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  // Validation
  validateGetSurveyPayload,
  // Authorization - verify the user owns this survey
  ValidateSurveyOwnership,
  // Process the request
  getSurveyByIdController,
);
