import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateGetShareUrlPayload, formatGetShareUrlPayload } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getShareUrlController } from '../../controllers';

export const getShareUrlRoute = Router();

getShareUrlRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId/share`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateGetShareUrlPayload,
  formatGetShareUrlPayload,
  getShareUrlController,
);
