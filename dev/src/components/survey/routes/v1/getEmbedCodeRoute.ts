import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { validateGetEmbedCodePayload, formatGetEmbedCodePayload } from '../../middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getEmbedCodeController } from '../../controllers';

export const getEmbedCodeRoute = Router();

getEmbedCodeRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId/embed`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateGetEmbedCodePayload,
  formatGetEmbedCodePayload,
  getEmbedCodeController,
);
