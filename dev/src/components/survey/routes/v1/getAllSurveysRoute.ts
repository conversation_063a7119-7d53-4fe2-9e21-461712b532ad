import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { ApiLimiter } from '../../../security';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getAllSurveysController } from '../../controllers';

export const getAllSurveysRoute = Router();

getAllSurveysRoute.get(
  `${GenerateApiVersionPath()}surveys`,
  ApiLimiter,
  // Extract request information
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  // Authentication
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  // Process the request
  getAllSurveysController,
);
