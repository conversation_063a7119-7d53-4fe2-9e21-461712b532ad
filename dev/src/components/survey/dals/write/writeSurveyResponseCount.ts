import { surveyModel } from '../../models';
import { Var } from '../../../../global/var';
import { Transaction } from 'sequelize';

interface WriteSurveyResponseCountResult {
  success: boolean;
  message: string;
  payload?: any;
}

export const writeSurveyResponseCount = async (surveyId: string, transaction: Transaction): Promise<WriteSurveyResponseCountResult> => {
  let isSuccessful: boolean = false;

  try {
    await surveyModel.increment('response_count', {
      where: { id: surveyId },
      transaction,
    });

    isSuccessful = true;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Incremented response count for survey: ${surveyId}`);
    }

    return {
      success: isSuccessful,
      message: `${Var.app.emoji.success} Response count incremented successfully`,
    };
  } catch (error) {
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} Error incrementing response count:`, error);
    }

    return {
      success: false,
      message: `${Var.app.emoji.failure} Failed to increment response count: ${(error as Error).message}`,
      payload: error,
    };
  }
};
