import { surveyModel } from '../../models';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';

export const writeSurveyUpdate = async (surveyId: string, accountId: string, attribute: string, value: any) => {
  try {
    const attributeMap: { [key: string]: string } = {
      title: 'title',
      distribution: 'distribution',
      embedUrl: 'embed_url',
      tags: 'tags',
      config: 'config',
      respondentDetails: 'respondent_details',
    };

    const updateObj: { [key: string]: any } = {};
    updateObj[attributeMap[attribute]] = value;

    // Update the survey
    const [updatedRowsCount] = await surveyModel.update(updateObj, {
      where: {
        id: surveyId,
        account_id: accountId,
        is_deleted: false,
      },
    });

    if (updatedRowsCount === 0) {
      logger.warn('Survey update failed: Survey not found or access denied', {
        surveyId,
        accountId,
        attribute,
      });
      return {
        success: false,
        message: `${Var.app.emoji.failure} Survey not found or access denied`,
        payload: null,
        updatedSurvey: null,
      };
    }

    // Fetch the updated survey
    const updatedSurvey = await surveyModel.findOne({
      where: {
        id: surveyId,
        account_id: accountId,
        is_deleted: false,
      },
    });

    logger.info('Survey updated successfully', {
      surveyId,
      accountId,
      attribute,
      value: typeof value === 'object' ? '[object]' : value,
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Survey ${attribute} updated`,
      payload: updatedSurvey,
      updatedSurvey,
    };
  } catch (error) {
    logger.error('Failed to update survey', { surveyId, accountId, attribute, error });
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not update survey ${attribute}. Please contact ${Var.app.contact.email}`,
      payload: error,
      updatedSurvey: null,
    };
  }
};
