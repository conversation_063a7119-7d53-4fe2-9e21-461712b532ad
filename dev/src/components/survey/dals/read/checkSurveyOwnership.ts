import { surveyModel } from '../../models';
import { logger } from '../../../../global/services';

/**
 * Data Access Layer function to verify survey ownership
 *
 * This is a critical security function that verifies whether a specific account
 * owns a particular survey. Used extensively for authorization checks before
 * allowing operations on surveys.
 *
 * Security features:
 * - Checks both survey ID and account ID match
 * - Excludes soft-deleted surveys from ownership verification
 * - Returns false on any database errors for fail-safe behavior
 * - Logs errors for security monitoring
 *
 * Performance optimizations:
 * - Only selects the 'id' attribute to minimize data transfer
 * - Uses boolean conversion for clean true/false return
 *
 * @param surveyId - The unique UUID of the survey to check
 * @param accountId - The unique UUID of the account claiming ownership
 * @returns Promise<boolean> - true if account owns the survey, false otherwise
 *
 * @example
 * const canEdit = await checkSurveyOwnership(surveyId, req.session.accountId);
 * if (!canEdit) {
 *   return res.status(403).json({ message: 'Access denied' });
 * }
 */
export const checkSurveyOwnership = async (surveyId: string, accountId: string): Promise<boolean> => {
  try {
    // Query for survey with matching ID, account ID, and not deleted
    // Only select 'id' attribute for performance (we just need to know if it exists)
    const survey = await surveyModel.findOne({
      attributes: ['id'],
      where: {
        id: surveyId,
        account_id: accountId,
        is_deleted: false, // Exclude soft-deleted surveys
      },
    });

    // Convert to boolean: true if survey found, false if null
    return !!survey;
  } catch (error) {
    // Log error for security monitoring and debugging
    logger.error('Error checking survey ownership', { surveyId, accountId, error });
    // Return false on error for fail-safe security behavior
    return false;
  }
};
