import { surveyModel } from "../../models";

export const readSurveyByPublicKey = async (publicKey: string) => {
  const survey = await surveyModel.findOne({
    attributes: [
      "id",
      "type",
      "title",
      "distribution",
      "embed_url",
      "config",
      "respondent_details",
      "public_key",
    ],
    where: {
      public_key: publicKey,
      is_deleted: false,
      is_disabled: false,
    },
  });

  return survey;
};
