import { surveyModel } from '../../models';

export const readSurveyById = async (surveyId: string) => {
  const survey = await surveyModel.findOne({
    attributes: [
      'id',
      'type',
      'title',
      'tags',
      'distribution',
      'response_count',
      'created_at',
      'embed_url',
      'share_key',
      'public_key',
      'config',
      'respondent_details',
      'meta',
      'account_id',
    ],
    where: {
      id: surveyId,
      is_deleted: false,
    },
  });

  return survey;
};
