import { Request, Response } from 'express';
import { Var } from '../../../../global/var';

export const getSurveyByPublicKeyController = async (_req: Request, res: Response) => {
  const survey = res.locals.survey;

  let surveyObj: any = {
    config: survey.config,
    respondentDetails: survey.respondent_details,
  };

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Survey fetched by public key`,
    payload: surveyObj,
  });
};
