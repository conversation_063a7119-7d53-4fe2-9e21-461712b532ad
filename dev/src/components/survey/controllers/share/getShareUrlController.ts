import { Request, Response } from "express";
import { Var } from "../../../../global/var";
import { generateShareUrl } from "../../helpers";

export const getShareUrlController = async (_req: Request, res: Response) => {
  const survey = res.locals.survey;
  
  // Generate the share URL
  const shareUrl = generateShareUrl(survey.share_key);

  return res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} Share URL fetched`,
    payload: {
      shareUrl: shareUrl,
    },
  });
};
