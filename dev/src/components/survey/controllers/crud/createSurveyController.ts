import { Request, Response, NextFunction } from 'express';

import { writeNewSurvey } from '../../dals';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

export const createSurveyController = async (req: Request, res: Response, next: NextFunction) => {
  const { type, title, distribution, embedUrl, tags, config, respondentDetails } = res.locals;

  // DEBUG: Log survey creation attempt
  logger.debug('Survey creation attempt', {
    type,
    title: title.substring(0, 20) + (title.length > 20 ? '...' : ''),
    distribution,
    accountId: res.locals.accountId.substring(0, 8) + '...',
    ip: req.ip,
    userAgent: req.headers['user-agent'],
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`[DEV] Creating ${type} survey: "${title.substring(0, 30)}${title.length > 30 ? '...' : ''}"`);
  }

  const newSurvey: any = await writeNewSurvey(type, title, distribution, embedUrl, tags, config, respondentDetails, res.locals.accountId);

  if (!newSurvey.success) {
    logger.warn('Survey creation failed', {
      type,
      title: title.substring(0, 20) + (title.length > 20 ? '...' : ''),
      accountId: res.locals.accountId.substring(0, 8) + '...',
      error: newSurvey.message,
    });

    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] ❌ Survey creation failed: ${newSurvey.message}`);
    }

    return res.status(400).json({
      success: false,
      message: newSurvey.message,
    });
  }

  // DEBUG: Log successful survey creation
  logger.info('Survey created successfully', {
    surveyId: newSurvey.payload.id.substring(0, 8) + '...',
    type: newSurvey.payload.type,
    title: newSurvey.payload.title.substring(0, 20) + (newSurvey.payload.title.length > 20 ? '...' : ''),
    accountId: res.locals.accountId.substring(0, 8) + '...',
  });

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(
      `[DEV] ✅ Survey created successfully: ${newSurvey.payload.type} - "${newSurvey.payload.title.substring(0, 30)}${newSurvey.payload.title.length > 30 ? '...' : ''}"`,
    );
  }

  const responseData = {
    id: newSurvey.payload.id,
    type: newSurvey.payload.type,
    title: newSurvey.payload.title,
    distribution: newSurvey.payload.distribution,
    embed_url: newSurvey.payload.embed_url,
    share_url: newSurvey.payload.share_url,
    tags: newSurvey.payload.tags,
    config: newSurvey.payload.config,
    respondentDetails: newSurvey.payload.respondentDetails,
  };

  // Store created survey for caching
  res.locals.updatedSurvey = newSurvey.payload;

  res.status(200).json({
    success: true,
    message: `${Var.app.emoji.success} New survey created`,
    payload: responseData,
  });

  // Continue to cache middleware
  next();
};
