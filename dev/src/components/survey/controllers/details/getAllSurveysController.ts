import { Request, Response, NextFunction } from 'express';
import { readSurveysByAccountId } from '../../dals';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';

export const getAllSurveysController = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const accountId = res.locals.accountId;

    logger.info(`Fetching all surveys for account`, { accountId });

    const surveys = await readSurveysByAccountId(accountId);
    if (!surveys) {
      logger.warn(`Failed to fetch surveys`, { accountId });
      return next(new AppError('Could not fetch surveys', 500));
    }

    const finalSurveys = surveys.map((survey: any) => {
      let timestamp = '';

      if (survey.dataValues && survey.dataValues.created_at && !isNaN(new Date(survey.dataValues.created_at).getTime())) {
        const createdDate = new Date(survey.dataValues.created_at);
        const date = createdDate.getDate();
        const monthName = createdDate.toLocaleString('default', {
          month: 'short',
        });
        const year = createdDate.getFullYear();
        timestamp = `${date} ${monthName} ${year}`;
      } else {
        timestamp = 'Date not available';
      }

      return {
        id: survey.dataValues.id,
        type: survey.dataValues.type,
        title: survey.dataValues.title,
        tags: survey.dataValues.tags,
        config: survey.dataValues.config,
        responseCount: survey.dataValues.response_count,
        meta: survey.dataValues.meta,
        isDeleted: survey.dataValues.is_deleted,
        timestamp: timestamp,
        publicKey: survey.dataValues.public_key,
        distribution: survey.dataValues.distribution,
      };
    });

    logger.info(`Surveys fetched successfully`, {
      accountId,
      count: finalSurveys.length,
    });

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Surveys fetched successfully`,
      payload: finalSurveys,
    });
  } catch (error) {
    logger.error(`Error fetching surveys`, {
      accountId: res.locals.accountId,
      error,
    });
    return next(new AppError('An error occurred while fetching surveys', 500));
  }
};
