import { Request, Response, NextFunction } from 'express';
import { readSurveyById, checkSurveyOwnership } from '../../dals';
import { verifyResourceOwnership } from '../../../security/helpers';
import { Var } from '../../../../global/var';
import { logger } from '../../../../global/services';
import { AppError } from '../../../../global/middlewares/handlers/HandleErrors';

export const getSurveyByIdController = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const surveyId = req.params.surveyId;
    const accountId = res.locals.accountId; // Use res.locals instead of session

    if (!accountId) {
      logger.warn(`No account ID in request`, { surveyId });
      return next(new AppError('Authentication required', 401));
    }

    logger.info(`Fetching survey by ID`, {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
    });

    // SECURITY FIX: Double authorization check - middleware + controller level
    const isOwnerByMiddleware = await checkSurveyOwnership(surveyId, accountId);
    const isOwnerByHelper = await verifyResourceOwnership('survey', surveyId, accountId);

    if (!isOwnerByMiddleware || !isOwnerByHelper) {
      logger.warn(`Unauthorized survey access attempt`, {
        surveyId: surveyId.substring(0, 8) + '...',
        accountId: accountId.substring(0, 8) + '...',
        middlewareCheck: isOwnerByMiddleware,
        helperCheck: isOwnerByHelper,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
      });
      return next(new AppError('Access denied', 403));
    }

    const survey = await readSurveyById(surveyId);
    if (!survey) {
      logger.warn(`Survey not found`, { surveyId });
      return next(new AppError('Survey not found', 404));
    }

    let timestamp = '';
    if (survey.dataValues && survey.dataValues.created_at && !isNaN(new Date(survey.dataValues.created_at).getTime())) {
      const createdDate = new Date(survey.dataValues.created_at);
      const date = createdDate.getDate();
      const monthName = createdDate.toLocaleString('default', {
        month: 'short',
      });
      const year = createdDate.getFullYear();
      timestamp = `${date} ${monthName} ${year}`;
    } else {
      timestamp = 'Date not available';
    }

    const surveyObj = {
      id: survey.dataValues.id,
      type: survey.dataValues.type,
      title: survey.dataValues.title,
      tags: survey.dataValues.tags,
      config: survey.dataValues.config,
      responseCount: survey.dataValues.response_count,
      distribution: survey.dataValues.distribution,
      embedUrl: survey.dataValues.embed_url,
      meta: survey.dataValues.meta,
      respondentDetails: survey.dataValues.respondent_details,
      isDeleted: survey.dataValues.is_deleted,
      timestamp: timestamp,
    };

    logger.info(`Survey fetched successfully`, { surveyId });

    return res.status(200).json({
      success: true,
      message: `${Var.app.emoji.success} Survey fetched successfully`,
      payload: surveyObj,
    });
  } catch (error) {
    logger.error(`Error fetching survey`, {
      surveyId: req.params.surveyId,
      error,
    });
    return next(new AppError('An error occurred while fetching the survey', 500));
  }
};
