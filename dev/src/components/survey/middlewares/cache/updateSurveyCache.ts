import { Request, Response, NextFunction } from 'express';
import { redisClient, logger } from '../../../../global/services';

/**
 * PERFORMANCE: Update survey cache after successful survey operations
 * Critical for embedded surveys that need fast retrieval via public key
 */
export const updateSurveyCache = async (_req: Request, res: Response, next: NextFunction) => {
  // Only cache on successful operations
  if (res.statusCode !== 200) {
    return next();
  }

  try {
    const updatedSurvey = res.locals.updatedSurvey;
    
    if (!updatedSurvey) {
      logger.debug('No updated survey data to cache');
      return next();
    }

    const publicKey = updatedSurvey.public_key;
    const surveyId = updatedSurvey.id;

    if (!publicKey) {
      logger.warn('Survey missing public_key for caching', { surveyId });
      return next();
    }

    // Cache survey data for embedded widget retrieval
    // Key format: survey:{publicKey} for fast public access
    const cacheKey = `survey:${publicKey}`;
    const cacheData = {
      id: updatedSurvey.id,
      type: updatedSurvey.type,
      title: updatedSurvey.title,
      distribution: updatedSurvey.distribution,
      embed_url: updatedSurvey.embed_url,
      config: updatedSurvey.config,
      respondent_details: updatedSurvey.respondent_details,
      public_key: updatedSurvey.public_key,
      is_deleted: updatedSurvey.is_deleted,
      is_disabled: updatedSurvey.is_disabled,
      cached_at: new Date().toISOString(),
    };

    // Cache for 24 hours (86400 seconds) - embedded surveys need long cache
    await redisClient.setex(cacheKey, 86400, JSON.stringify(cacheData));

    logger.info('Survey cache updated', {
      publicKey: publicKey.substring(0, 8) + '...',
      surveyId: surveyId.substring(0, 8) + '...',
      cacheKey,
    });

  } catch (error) {
    logger.error('Failed to update survey cache', {
      error,
      surveyData: res.locals.updatedSurvey ? {
        id: res.locals.updatedSurvey.id?.substring(0, 8) + '...',
        public_key: res.locals.updatedSurvey.public_key?.substring(0, 8) + '...',
      } : 'none',
    });
    // Don't fail the request if caching fails
  }

  next();
};
