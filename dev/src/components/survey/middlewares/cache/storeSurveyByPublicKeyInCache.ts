import { Request, Response, NextFunction } from 'express';
import { setSurveyInCache } from '../../../../global/services';
import { Var } from '../../../../global/var';

/**
 * Optimized middleware to store survey in cache by public key
 * This should be placed after the database retrieval middleware
 */
export const storeSurveyByPublicKeyInCache = async (req: Request, res: Response, next: NextFunction) => {
  // Use non-blocking approach to avoid delaying the response
  // Call next() immediately and let caching happen in the background
  next();

  // Execute cache operation asynchronously without blocking the response
  (async () => {
    try {
      // Only proceed if we have a survey and it wasn't from cache
      if (res.locals.survey && res.locals.fromCache === false) {
        const publicKey = req.params.publicKey;
        if (!publicKey) return;

        // Store the survey in cache
        await setSurveyInCache(publicKey, res.locals.survey);

        // Only log in development mode
        if (Var.node.env === 'dev') {
          console.log(`${Var.app.emoji.success} Stored survey in cache: ${publicKey}`);
        }
      }
    } catch (error) {
      // Only log errors in development mode
      if (Var.node.env === 'dev') {
        console.error(`${Var.app.emoji.failure} Error storing survey in cache:`, error);
      }
      // Don't fail the request if caching fails
    }
  })();
};
