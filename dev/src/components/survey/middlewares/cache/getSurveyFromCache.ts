import { Request, Response, NextFunction } from 'express';
import { redisClient, logger } from '../../../../global/services';

/**
 * PERFORMANCE: Get survey from cache for embedded widgets
 * Critical middleware for fast public survey retrieval
 */
export const getSurveyFromCache = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const publicKey = req.params.publicKey;
    
    if (!publicKey) {
      logger.debug('No public key provided for cache lookup');
      return next();
    }

    const cacheKey = `survey:${publicKey}`;
    const cachedData = await redisClient.get(cacheKey);

    if (cachedData) {
      try {
        const survey = JSON.parse(cachedData);
        
        // Verify cached data is still valid
        if (survey.is_deleted || survey.is_disabled) {
          logger.info('Cached survey is deleted/disabled, removing from cache', {
            publicKey: publicKey.substring(0, 8) + '...',
            cacheKey,
          });
          await redisClient.del(cacheKey);
          return next(); // Fall through to database
        }

        // Store cached survey in res.locals for controller
        res.locals.survey = survey;
        res.locals.fromCache = true;

        logger.debug('Survey served from cache', {
          publicKey: publicKey.substring(0, 8) + '...',
          cacheKey,
          cachedAt: survey.cached_at,
        });

        return next();
      } catch (parseError) {
        logger.error('Failed to parse cached survey data', {
          error: parseError,
          publicKey: publicKey.substring(0, 8) + '...',
          cacheKey,
        });
        // Remove corrupted cache entry
        await redisClient.del(cacheKey);
      }
    }

    logger.debug('Survey not found in cache, will fetch from database', {
      publicKey: publicKey.substring(0, 8) + '...',
      cacheKey,
    });

    next();
  } catch (error) {
    logger.error('Error checking survey cache', {
      error,
      publicKey: req.params.publicKey?.substring(0, 8) + '...',
    });
    // Continue to database on cache error
    next();
  }
};
