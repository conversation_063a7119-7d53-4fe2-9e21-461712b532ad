import Jo<PERSON> from "joi";
import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";

const getSurveyByPublicKeyPayloadSchema = Joi.object({
  publicKey: Joi.string().uuid().required(),
});

export const validateGetSurveyByPublicKeyPayload = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let { error } = getSurveyByPublicKeyPayloadSchema.validate({
    publicKey: req.params.publicKey,
  });

  if (error) {
    console.log(`${Var.app.emoji.failure} Get survey by public key payload not valid`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  console.log(`${Var.app.emoji.success} Get survey by public key payload valid`);
  next();
};
