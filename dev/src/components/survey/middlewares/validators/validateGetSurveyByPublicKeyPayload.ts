import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

/**
 * Schema for validating the public key and survey type parameters
 * Ensures the public key is a valid UUID and survey type is valid
 */
const getSurveyByPublicKeyPayloadSchema = Joi.object({
  publicKey: Joi.string().uuid().required().messages({
    'string.empty': 'Public key cannot be empty',
    'string.uuid': 'Public key must be a valid UUID',
    'any.required': 'Public key is required',
  }),
  surveyType: Joi.string().valid('sensePrice', 'senseChoice', 'sensePoll', 'senseQuery', 'sensePriority').required().messages({
    'string.empty': 'Survey type cannot be empty',
    'any.only': 'Survey type must be one of: sensePrice, senseChoice, sensePoll, senseQuery, sensePriority',
    'any.required': 'Survey type is required',
  }),
});

/**
 * Middleware to validate the public key and survey type parameters
 * Returns a 400 error if the public key or survey type is invalid
 */
export const validateGetSurveyByPublicKeyPayload = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate the public key and survey type parameters
    const { error } = getSurveyByPublicKeyPayloadSchema.validate({
      publicKey: req.params.publicKey,
      surveyType: req.params.surveyType,
    });

    // Return a 400 error if validation fails
    if (error) {
      console.log(`${Var.app.emoji.failure} Invalid parameters: ${error.details[0].message}`);
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} ${error.details[0].message}`,
      });
    }

    // Continue to the next middleware if validation passes
    console.log(`${Var.app.emoji.success} Valid parameters - Public key: ${req.params.publicKey}, Survey type: ${req.params.surveyType}`);
    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in validateGetSurveyByPublicKeyPayload:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating request',
    });
  }
};
