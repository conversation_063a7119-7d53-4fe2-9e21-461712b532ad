import Jo<PERSON> from "joi";
import { Request, Response, NextFunction } from "express";
import { Var } from "../../../../global/var";

const getEmbedCodePayloadSchema = Joi.object({
  surveyId: Joi.string().uuid().required(),
});

export const validateGetEmbedCodePayload = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let { error } = getEmbedCodePayloadSchema.validate({
    surveyId: req.params.surveyId,
  });

  if (error) {
    console.log(`${Var.app.emoji.failure} Get embed code payload not valid`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  console.log(`${Var.app.emoji.success} Get embed code payload valid`);
  next();
};
