import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

/**
 * Middleware to validate that the requested survey type matches the actual survey type in the database
 * This middleware should be placed after BlockRequestByDistribution to ensure the survey is already fetched
 */
export const BlockRequestBySurveyTypeMismatch = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const surveyType = req.params.surveyType;
    const survey = res.locals.survey;

    // Ensure survey exists (should be set by previous middleware)
    if (!survey) {
      console.log(`${Var.app.emoji.failure} Survey not found in res.locals`);
      return res.status(404).json({
        success: false,
        message: 'Survey not found',
      });
    }

    console.log(`${Var.app.emoji.success} Validating survey type: requested=${surveyType}, actual=${survey.type}`);

    // Validate that the survey type matches the requested survey type
    if (survey.type !== surveyType) {
      console.log(`${Var.app.emoji.failure} Survey type mismatch: expected ${surveyType}, found ${survey.type}`);
      return res.status(400).json({
        success: false,
        message: `Survey type mismatch. The public key is not valid for '${surveyType}'`,
      });
    }

    console.log(`${Var.app.emoji.success} Survey type validation passed`);
    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error in BlockRequestBySurveyTypeMismatch:`, error);
    return res.status(500).json({
      success: false,
      message: 'Error validating survey type',
    });
  }
};
