import { Request, Response, NextFunction } from 'express';
import { readSurveyByPublicKey } from '../../dals';
import { Var } from '../../../../global/var';

export const BlockRequestByDistribution = async (req: Request, res: Response, next: NextFunction) => {
  const publicKey = req.params.publicKey;
  const origin = res.locals.origin || '';

  // Use cached survey if available, otherwise fetch from database
  let survey: any = res.locals.survey;

  if (!survey) {
    survey = await readSurveyByPublicKey(publicKey);
  }

  if (!survey) {
    console.log(`${Var.app.emoji.failure} Survey not found`);
    return res.status(404).json({
      success: false,
      message: `${Var.app.emoji.failure} Survey not found`,
    });
  }

  const distribution = survey.distribution;
  const embedUrl = survey.embed_url || '';

  let isOriginAllowed = false;

  const isOriginMatchingEmbedUrl = () => {
    if (!embedUrl) return false;
    try {
      const embedUrlDomain = new URL(embedUrl).hostname;
      const originDomain = new URL(origin).hostname;

      return embedUrlDomain === originDomain;
    } catch (error) {
      console.error('Error parsing URL:', error);
      return false;
    }
  };

  const isSenseFolksOrigin = origin.includes('sensefolks.com');

  switch (distribution) {
    case 'embed':
      isOriginAllowed = embedUrl ? isOriginMatchingEmbedUrl() : false;
      break;

    case 'link':
      isOriginAllowed = isSenseFolksOrigin;
      break;

    case 'embedlink':
      isOriginAllowed = (embedUrl ? isOriginMatchingEmbedUrl() : false) || isSenseFolksOrigin;
      break;

    default:
      isOriginAllowed = false;
  }

  if (!isOriginAllowed) {
    console.log(`${Var.app.emoji.failure} Origin not allowed for this survey distribution type`);
    return res.status(403).json({
      success: false,
      message: `${Var.app.emoji.failure} Access denied based on origin`,
    });
  }

  res.locals.survey = survey;

  next();
};
