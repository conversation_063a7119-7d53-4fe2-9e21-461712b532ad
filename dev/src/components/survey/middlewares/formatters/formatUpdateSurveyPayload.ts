import { Request, Response, NextFunction } from 'express';

export const formatUpdateSurveyPayload = (req: Request, res: Response, next: NextFunction) => {
  const { attribute, value } = req.body;
  const { surveyId } = req.params;

  res.locals.surveyId = surveyId;
  res.locals.attribute = attribute;

  if (attribute === 'title' || attribute === 'distribution' || attribute === 'embedUrl') {
    res.locals.value = typeof value === 'string' ? value.trim() : value;
  } else {
    res.locals.value = value;
  }

  next();
};
