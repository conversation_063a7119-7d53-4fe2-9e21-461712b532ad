import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../../global/var';

const modelName: string = 'survey';
// The actual table name in the database is 'surveys'
const tableName: string = 'surveys';

const modelAttributes: ModelAttributes = {
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  distribution: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  embed_url: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isHttpsOrLocalhost(value: string) {
        if (value && !/^https:\/\//.test(value) && !/^http:\/\/localhost/.test(value) && !/^http:\/\/127\.0\.0\.1/.test(value)) {
          throw new Error("Embed URL must start with 'https://' or be a localhost/127.0.0.1 URL");
        }
      },
    },
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  config: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
  },
  respondent_details: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
  },
  public_key: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
  },
  share_key: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
  },
  response_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  expiry: {
    type: DataTypes.INTEGER,
    defaultValue: -1,
  },
  is_disabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  is_deleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
};

const modelOptions: ModelOptions = {
  // Explicitly set the table name to match the existing table in the database
  tableName: tableName,
  // Set timestamps to true to include created_at and updated_at fields
  timestamps: true,
  // Use underscored naming convention for all fields (including timestamps)
  underscored: true,
};

export const surveyModel = Sequelize.define(modelName, modelAttributes, modelOptions);
