/**
 * Generates the embed code for a survey based on its type and public key
 * @param type The survey type (sensePrice, senseChoice, sensePoll, senseQuery, sensePriority)
 * @param publicKey The public key of the survey
 * @returns The HTML embed code as a string
 */
export const generateEmbedCode = (type: string, publicKey: string): string => {
  // Map survey type to the corresponding web component name
  const componentMap: { [key: string]: string } = {
    sensePrice: 'sf-senseprice',
    senseChoice: 'sf-sensechoice',
    sensePoll: 'sf-sensepoll',
    senseQuery: 'sf-sensequery',
    sensePriority: 'sf-sensepriority',
  };

  // Get the component name based on the survey type
  const componentName = componentMap[type];

  if (!componentName) {
    throw new Error(`Invalid survey type: ${type}`);
  }

  // Generate the embed code
  return `<${componentName} survey-key="${publicKey}"></${componentName}>`;
};
