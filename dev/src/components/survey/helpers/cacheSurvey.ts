import { redisClient, logger } from '../../../global/services';

/**
 * PERFORMANCE: Helper function to cache survey data
 * Used by DAL operations and controllers to maintain cache consistency
 */
export const cacheSurvey = async (survey: any): Promise<void> => {
  try {
    if (!survey || !survey.public_key) {
      logger.debug('Cannot cache survey: missing survey data or public_key');
      return;
    }

    const cacheKey = `survey:${survey.public_key}`;
    const cacheData = {
      id: survey.id,
      type: survey.type,
      title: survey.title,
      distribution: survey.distribution,
      embed_url: survey.embed_url,
      config: survey.config,
      respondent_details: survey.respondent_details,
      public_key: survey.public_key,
      is_deleted: survey.is_deleted || false,
      is_disabled: survey.is_disabled || false,
      cached_at: new Date().toISOString(),
    };

    // Cache for 24 hours (86400 seconds) - embedded surveys need long cache
    await redisClient.setex(cacheKey, 86400, JSON.stringify(cacheData));

    logger.debug('Survey cached successfully', {
      publicKey: survey.public_key.substring(0, 8) + '...',
      surveyId: survey.id?.substring(0, 8) + '...',
      cacheKey,
    });

  } catch (error) {
    logger.error('Failed to cache survey', {
      error,
      surveyId: survey?.id?.substring(0, 8) + '...',
      publicKey: survey?.public_key?.substring(0, 8) + '...',
    });
    // Don't throw error - caching failure shouldn't break the operation
  }
};

/**
 * PERFORMANCE: Helper function to remove survey from cache
 * Used when surveys are deleted or disabled
 */
export const removeSurveyFromCache = async (publicKey: string): Promise<void> => {
  try {
    if (!publicKey) {
      logger.debug('Cannot remove from cache: missing public_key');
      return;
    }

    const cacheKey = `survey:${publicKey}`;
    const result = await redisClient.del(cacheKey);

    if (result === 1) {
      logger.debug('Survey removed from cache', {
        publicKey: publicKey.substring(0, 8) + '...',
        cacheKey,
      });
    } else {
      logger.debug('Survey cache key did not exist', {
        publicKey: publicKey.substring(0, 8) + '...',
        cacheKey,
      });
    }

  } catch (error) {
    logger.error('Failed to remove survey from cache', {
      error,
      publicKey: publicKey?.substring(0, 8) + '...',
    });
    // Don't throw error - cache removal failure shouldn't break the operation
  }
};
