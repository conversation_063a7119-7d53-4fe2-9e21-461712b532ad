import { Request, Response } from 'express';
import { logger, auditLogger, AuditEventType } from '../../../../global/services';

/**
 * SECURITY FIX: CSP violation reporting endpoint
 * Handles Content Security Policy violation reports for security monitoring
 */

interface CSPViolationReport {
  'csp-report': {
    'document-uri': string;
    'referrer': string;
    'violated-directive': string;
    'effective-directive': string;
    'original-policy': string;
    'disposition': string;
    'blocked-uri': string;
    'line-number': number;
    'column-number': number;
    'source-file': string;
    'status-code': number;
    'script-sample': string;
  };
}

export const cspReportController = async (req: Request, res: Response) => {
  try {
    const report: CSPViolationReport = req.body;

    if (!report || !report['csp-report']) {
      logger.warn('Invalid CSP report received', {
        body: req.body,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
      });
      return res.status(400).json({
        success: false,
        message: 'Invalid CSP report format',
      });
    }

    const cspReport = report['csp-report'];

    // Log CSP violation with security context
    logger.warn('CSP Violation Detected', {
      documentUri: cspReport['document-uri'],
      violatedDirective: cspReport['violated-directive'],
      effectiveDirective: cspReport['effective-directive'],
      blockedUri: cspReport['blocked-uri'],
      sourceFile: cspReport['source-file'],
      lineNumber: cspReport['line-number'],
      columnNumber: cspReport['column-number'],
      scriptSample: cspReport['script-sample']?.substring(0, 100),
      disposition: cspReport.disposition,
      statusCode: cspReport['status-code'],
      referrer: cspReport.referrer,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
    });

    // Determine severity based on violation type
    const severity = determineSeverity(cspReport);

    // Log to audit system
    await auditLogger.logAuditEvent({
      eventType: AuditEventType.SECURITY_VIOLATION,
      ipAddress: req.ip || 'unknown',
      userAgent: req.headers['user-agent'],
      action: 'csp_violation',
      result: 'blocked',
      severity,
      details: {
        violatedDirective: cspReport['violated-directive'],
        effectiveDirective: cspReport['effective-directive'],
        blockedUri: cspReport['blocked-uri'],
        documentUri: cspReport['document-uri'],
        sourceFile: cspReport['source-file'],
        lineNumber: cspReport['line-number'],
        columnNumber: cspReport['column-number'],
        scriptSample: cspReport['script-sample']?.substring(0, 200),
        disposition: cspReport.disposition,
      },
      timestamp: new Date(),
    });

    // Check for potential XSS attempts
    if (isPotentialXSSAttempt(cspReport)) {
      logger.error('SECURITY ALERT: Potential XSS attempt detected via CSP violation', {
        blockedUri: cspReport['blocked-uri'],
        scriptSample: cspReport['script-sample'],
        violatedDirective: cspReport['violated-directive'],
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        documentUri: cspReport['document-uri'],
      });

      // Additional audit log for XSS attempts
      await auditLogger.logAuditEvent({
        eventType: AuditEventType.SECURITY_VIOLATION,
        ipAddress: req.ip || 'unknown',
        userAgent: req.headers['user-agent'],
        action: 'potential_xss_attempt',
        result: 'blocked',
        severity: 'critical',
        details: {
          attackVector: 'csp_violation',
          blockedUri: cspReport['blocked-uri'],
          scriptSample: cspReport['script-sample']?.substring(0, 500),
          violatedDirective: cspReport['violated-directive'],
        },
        timestamp: new Date(),
      });
    }

    // Check for potential data exfiltration attempts
    if (isPotentialDataExfiltration(cspReport)) {
      logger.error('SECURITY ALERT: Potential data exfiltration attempt detected', {
        blockedUri: cspReport['blocked-uri'],
        violatedDirective: cspReport['violated-directive'],
        ip: req.ip,
        userAgent: req.headers['user-agent'],
      });
    }

    // Return 204 No Content as per CSP specification
    return res.status(204).send();
  } catch (error) {
    logger.error('Error processing CSP report', {
      error: error instanceof Error ? error.message : String(error),
      body: req.body,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    return res.status(500).json({
      success: false,
      message: 'Error processing CSP report',
    });
  }
};

/**
 * Determine severity of CSP violation
 */
function determineSeverity(cspReport: CSPViolationReport['csp-report']): 'low' | 'medium' | 'high' | 'critical' {
  const violatedDirective = cspReport['violated-directive'];
  const blockedUri = cspReport['blocked-uri'];

  // Critical: Script injection attempts
  if (violatedDirective.includes('script-src') && (blockedUri.includes('javascript:') || blockedUri.includes('data:') || cspReport['script-sample'])) {
    return 'critical';
  }

  // High: External resource loading to suspicious domains
  if (
    blockedUri &&
    (blockedUri.includes('eval') || blockedUri.includes('inline') || blockedUri.match(/https?:\/\/[^\/]*\.(tk|ml|ga|cf)/)) // Suspicious TLDs
  ) {
    return 'high';
  }

  // Medium: Style or image violations
  if (violatedDirective.includes('style-src') || violatedDirective.includes('img-src')) {
    return 'medium';
  }

  // Low: Other violations
  return 'low';
}

/**
 * Check if CSP violation indicates potential XSS attempt
 */
function isPotentialXSSAttempt(cspReport: CSPViolationReport['csp-report']): boolean {
  const indicators = [
    // Script-related violations
    cspReport['violated-directive'].includes('script-src'),

    // Inline script attempts
    cspReport['blocked-uri'] === 'inline',
    cspReport['blocked-uri'] === 'eval',

    // JavaScript protocol
    cspReport['blocked-uri'].includes('javascript:'),

    // Data URLs with scripts
    cspReport['blocked-uri'].includes('data:') && cspReport['blocked-uri'].includes('script'),

    // Script samples containing suspicious patterns
    cspReport['script-sample'] &&
      (cspReport['script-sample'].includes('alert(') ||
        cspReport['script-sample'].includes('document.cookie') ||
        cspReport['script-sample'].includes('window.location') ||
        cspReport['script-sample'].includes('eval(') ||
        cspReport['script-sample'].includes('setTimeout(') ||
        cspReport['script-sample'].includes('setInterval(')),
  ];

  return indicators.some(indicator => indicator);
}

/**
 * Check if CSP violation indicates potential data exfiltration
 */
function isPotentialDataExfiltration(cspReport: CSPViolationReport['csp-report']): boolean {
  const blockedUri = cspReport['blocked-uri'];

  // Check for connections to external domains
  if (blockedUri && blockedUri.startsWith('http')) {
    const suspiciousPatterns = [
      // Common data exfiltration domains
      /webhook\.site/i,
      /requestbin\.com/i,
      /ngrok\.io/i,
      /burpcollaborator\.net/i,

      // Suspicious TLDs
      /\.(tk|ml|ga|cf|bit\.ly|tinyurl\.com)/i,

      // Base64 encoded data in URL
      /[A-Za-z0-9+\/]{20,}={0,2}/,

      // Long query strings (potential data exfiltration)
      /\?.{100,}/,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(blockedUri));
  }

  return false;
}
