import { Router } from 'express';
import { cspReportController } from '../../controllers';
import { GenerateApiVersionPath } from '../../../../global/helpers';

export const cspReportRoute = Router();

/**
 * CSP Violation Reporting Route
 * Handles Content Security Policy violation reports
 * No authentication required as this is called by browsers automatically
 */
cspReportRoute.post(`${GenerateApiVersionPath()}csp-report`, cspReportController);
