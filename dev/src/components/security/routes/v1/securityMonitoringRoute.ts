import { Router } from 'express';
import { getSecurityMetricsController, getSecurityAlertsController } from '../../controllers';
import { SensitiveOperationLimiter } from '../../middlewares';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
} from '../../../../global/middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';

export const securityMonitoringRoute = Router();

/**
 * Security Monitoring Routes
 * Provides security metrics and alerts for administrators
 * Requires authentication and admin privileges
 */

// Get security metrics
securityMonitoringRoute.get(
  `${GenerateApiVersionPath()}security/metrics`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  SensitiveOperationLimiter,
  getSecurityMetricsController,
);

// Get security alerts
securityMonitoringRoute.get(
  `${GenerateApiVersionPath()}security/alerts`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  SensitiveOperationLimiter,
  getSecurityAlertsController,
);
