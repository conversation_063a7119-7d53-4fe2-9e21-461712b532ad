import { doubleCsrf } from 'csrf-csrf';
import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

import { Var } from '../../../global/var';
import { logger } from '../../../global/services';

const csrfInstance = doubleCsrf({
  getSecret: () => {
    const baseSecret = Var.node.express.session.secret;
    return crypto.createHmac('sha256', baseSecret).update('csrf-secret-v2').digest('hex');
  },
  getSessionIdentifier: req => {
    if (!req.session?.id) {
      const ip = req.ip || 'unknown-ip';
      const userAgent = req.headers['user-agent'] || 'unknown-agent';
      const fallbackId = ip + userAgent + Date.now().toString();
      logger.debug('Using fallback session identifier for CSRF (no session ID)', {
        path: req.path,
        method: req.method,
        hasSession: !!req.session,
      });
      return crypto.createHash('sha256').update(fallbackId).digest('hex').substring(0, 16);
    }

    const sessionIdentifier = crypto.createHash('sha256').update(req.session.id).digest('hex').substring(0, 16);
    logger.debug('Generated session identifier for CSRF', {
      path: req.path,
      method: req.method,
      sessionId: req.session.id.substring(0, 5) + '...',
      sessionIdentifier: sessionIdentifier.substring(0, 10) + '...',
      fullSessionId: req.session.id, // Temporary for debugging
      sessionIdLength: req.session.id.length,
      hashInput: req.session.id,
    });
    return sessionIdentifier;
  },
  cookieName: Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token',
  cookieOptions: {
    httpOnly: true,
    sameSite: 'lax',
    secure: Var.node.env === 'prod',
    maxAge: 14 * 24 * 60 * 60 * 1000, // 2 weeks
    path: '/',
  },
  size: 128,
  ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
  getCsrfTokenFromRequest: req => {
    const headerToken = (req.headers['x-csrf-token'] || req.headers['X-CSRF-Token']) as string;
    const bodyToken = req.body?._csrf;
    const token = headerToken || bodyToken;

    logger.debug('CSRF token from request:', {
      hasHeaderToken: !!headerToken,
      hasBodyToken: !!bodyToken,
      headerTokenPrefix: headerToken ? headerToken.substring(0, 10) + '...' : 'none',
      path: req.path,
    });

    return token;
  },
});

export const GenerateCsrfToken = csrfInstance.generateCsrfToken;

export function DoubleCsrfProtection(req: Request, res: Response, next: NextFunction) {
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Check if response has already been sent to prevent double responses
  if (res.headersSent) {
    logger.warn('CSRF protection called after headers sent:', {
      path: req.path,
      method: req.method,
    });
    return;
  }

  const receivedToken = (req.headers['x-csrf-token'] || req.headers['X-CSRF-Token'] || req.body?._csrf) as string;
  const cookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';
  const cookieSecret = req.cookies[cookieName];
  const isLogoutRequest = req.path.includes('/auth/logout');

  logger.debug('CSRF validation check:', {
    path: req.path,
    method: req.method,
    sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
    receivedToken: receivedToken ? receivedToken.substring(0, 10) + '...' : 'none',
    cookieSecret: cookieSecret ? cookieSecret.substring(0, 10) + '...' : 'none',
    isLogoutRequest,
    hasSession: !!req.session,
    sessionCreatedAt: req.session?.createdAt,
    cookieName,
    allCookies: Object.keys(req.cookies || {}),
  });

  try {
    const doubleCsrfProtection = csrfInstance.doubleCsrfProtection;
    doubleCsrfProtection(req, res, (error?: any) => {
      // Check if response has already been sent before proceeding
      if (res.headersSent) {
        logger.warn('CSRF callback called after headers sent:', {
          path: req.path,
          method: req.method,
          hasError: !!error,
        });
        return;
      }

      if (error) {
        const logLevel = isLogoutRequest ? 'error' : 'warn';
        logger[logLevel]('CSRF validation failed:', {
          error: error.message,
          path: req.path,
          method: req.method,
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
          hasHeaderToken: !!receivedToken,
          hasBodyToken: !!req.body?._csrf,
          isLogoutRequest,
          sessionExists: !!req.session,
          receivedTokenPrefix: receivedToken ? receivedToken.substring(0, 10) + '...' : 'none',
          cookieSecretPrefix: cookieSecret ? cookieSecret.substring(0, 10) + '...' : 'none',
          tokenMatch: receivedToken === cookieSecret,
          sessionCreatedAt: req.session?.createdAt,
          allCookies: Object.keys(req.cookies || {}),
        });

        const csrfCookieName = Var.node.env === 'prod' ? '__Host-psifi.x-csrf-token' : 'psifi.x-csrf-token';
        res.clearCookie(csrfCookieName, {
          path: '/',
          domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
          secure: Var.node.env === 'prod',
          sameSite: 'lax',
          httpOnly: true,
        });

        logger.debug('Cleared invalid CSRF cookie:', {
          cookieName: csrfCookieName,
          path: req.path,
        });

        // DEBUG: Console output in development only
        if (Var.node.env === 'dev') {
          console.log(`[DEV] ❌ CSRF validation failed for ${req.method} ${req.path}: ${error.message}`);
        }

        // Send response and ensure no further middleware execution
        res.status(403).json({
          success: false,
          message: 'Invalid security token. Please refresh the page and try again.',
          code: 'CSRF_TOKEN_INVALID',
        });
        return; // Explicitly return to prevent further execution
      }

      logger.debug('CSRF validation successful:', {
        path: req.path,
        method: req.method,
        sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
      });

      // DEBUG: Console output in development only
      if (Var.node.env === 'dev') {
        console.log(`[DEV] ✅ CSRF validation successful for ${req.method} ${req.path}`);
      }

      next();
    });
  } catch (error) {
    // Check if response has already been sent before sending error response
    if (res.headersSent) {
      logger.warn('CSRF protection error after headers sent:', {
        path: req.path,
        method: req.method,
        error: error instanceof Error ? error.message : String(error),
      });
      return;
    }

    logger.error('CSRF protection error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      path: req.path,
      method: req.method,
      sessionExists: !!req.session,
      sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
    });

    res.status(500).json({
      success: false,
      message: 'Security validation error. Please try again.',
      details: Var.node.env === 'dev' ? (error instanceof Error ? error.message : String(error)) : undefined,
    });
    return; // Explicitly return to prevent further execution
  }
}
