import crypto from 'crypto';
import { logger } from '../../../../global/services';

/**
 * SECURITY IMPLEMENTATION: 6-digit code generation for user-friendly verification
 * Uses direct hash approach for secure verification while maintaining simplicity
 * Provides cryptographically secure 6-digit codes with configurable expiration
 */
export const generate6DigitCode = (expiresInMinutes: number) => {
  // Generate cryptographically secure 6-digit code
  const code = crypto.randomInt(100000, 999999).toString().padStart(6, '0');

  // Simple but secure hash for storage
  const hashedCode = crypto.createHash('sha256').update(code).digest('hex');
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);

  logger.info('6-digit verification code generated', {
    codeLength: code.length,
    expiresAt,
    expiresInMinutes,
  });

  return {
    code,
    hashedCode,
    expiresAt,
  };
};
