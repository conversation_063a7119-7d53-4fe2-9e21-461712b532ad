import express, { Request, Response } from 'express';
import cors from 'cors';
import { Var } from './global/var';
import { HandleErrors } from './global/middlewares';
import { submitResponseRoute } from './components/response/routes/v1';

const app = express();

app.use(cors());

app.use(express.json());

if (Var.node.env === 'prod') {
  app.set('trust proxy', 1);
}

app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    service: 'response-api',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

app.use(submitResponseRoute);

app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.path,
  });
});

app.use(HandleErrors);

export default app;
