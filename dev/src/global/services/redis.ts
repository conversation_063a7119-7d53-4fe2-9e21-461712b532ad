import Redis from 'ioredis';
import { Var } from '../var';
import { logger } from './logger';

export const redisClient = new Redis({
  host: Var.redis.host,
  port: +Var.redis.port,
  password: Var.redis.password,
  db: 0,
  connectTimeout: 10000,
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  tls: Var.node.env === 'prod' ? { rejectUnauthorized: true } : undefined, // Use TLS in production
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 1000, 30000);
    return delay;
  },
});

redisClient.on('error', err => {
  logger.error('Redis connection error', err);
});

redisClient.on('connect', () => {
  logger.info('Connected to Redis');
});

redisClient.on('ready', () => {
  logger.info('Redis client ready');
});

process.on('SIGTERM', () => {
  logger.info('SIGTERM received, closing Redis connection');
  redisClient.quit();
});
