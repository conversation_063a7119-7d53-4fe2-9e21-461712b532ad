import { Var } from '../../var';
import { getRedisClient } from './redisClient';
import { generateSurveyCacheKey } from './cacheKeys';

export const setSurveyInCache = async (publicKey: string, data: any): Promise<boolean> => {
  if (!publicKey || !data) {
    return false;
  }

  try {
    const redisClient = getRedisClient();

    const cacheKey = generateSurveyCacheKey(publicKey);
    let serializedSurvey: string;

    try {
      // Ensure public_key is set in the data being cached
      const optimizedSurvey = {
        ...data,
        public_key: data.public_key || publicKey,
      };

      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Optimized survey for cache:`, optimizedSurvey);
      }

      serializedSurvey = JSON.stringify(optimizedSurvey);
    } catch (serializeError) {
      console.error(`${Var.app.emoji.failure} Error serializing data for cache:`, serializeError);
      return false;
    }

    await redisClient.set(cacheKey, serializedSurvey, 'EX', Var.redis.cacheTTL);

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Cached survey: ${publicKey} (TTL: ${Var.redis.cacheTTL}s)`);
    }

    return true;
  } catch (error) {
    if (Var.node.env === 'dev' || (error instanceof Error && error.message.includes('ECONNREFUSED'))) {
      console.error(`${Var.app.emoji.failure} Redis error:`, error);
    }
    return false;
  }
};
