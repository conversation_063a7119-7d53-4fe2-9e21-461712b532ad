import { Var } from '../../var';
import { getRedisClient } from './redisClient';

// Cache key prefix for surveys
const SURVEY_CACHE_PREFIX = 'survey:';

/**
 * Generate cache key for a survey
 * @param publicKey - The public key of the survey
 * @returns The cache key
 */
const generateSurveyCacheKey = (publicKey: string): string => {
  return `${SURVEY_CACHE_PREFIX}${publicKey}`;
};

/**
 * Set survey data in Redis cache with optimized performance
 * @param publicKey - The public key of the survey
 * @param data - The survey data to cache
 * @returns True if successful, false otherwise
 */
export const setSurveyInCache = async (publicKey: string, data: any): Promise<boolean> => {
  if (!publicKey || !data) {
    return false;
  }

  try {
    const redisClient = getRedisClient();

    // Prepare data for caching
    const cacheKey = generateSurveyCacheKey(publicKey);
    let serializedData: string;

    try {
      // Optimize the data before caching by removing unnecessary fields
      const optimizedData = {
        ...data,
        // Add any data transformations here if needed
      };

      serializedData = JSON.stringify(optimizedData);
    } catch (serializeError) {
      console.error(`${Var.app.emoji.failure} Error serializing data for cache:`, serializeError);
      return false;
    }

    // Set data in Redis with TTL
    await redisClient.set(cacheKey, serializedData, 'EX', Var.redis.cacheTTL);

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Cached survey: ${publicKey} (TTL: ${Var.redis.cacheTTL}s)`);
    }

    return true;
  } catch (error) {
    // Only log in development or if it's a critical error
    if (Var.node.env === 'dev' || (error instanceof Error && error.message.includes('ECONNREFUSED'))) {
      console.error(`${Var.app.emoji.failure} Redis error:`, error);
    }
    // Fail gracefully - don't let Redis errors affect the application
    return false;
  }
};
