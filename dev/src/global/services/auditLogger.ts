import { logger } from './logger';
import { redisClient } from './redis';
import crypto from 'crypto';

/**
 * SECURITY FIX: Comprehensive audit logging system
 * Provides immutable audit trails for security events and compliance
 */

export interface AuditEvent {
  eventType: AuditEventType;
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent?: string;
  resource?: string;
  resourceId?: string;
  action: string;
  result: 'success' | 'failure' | 'blocked';
  details?: Record<string, any>;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export enum AuditEventType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  SECURITY_VIOLATION = 'security_violation',
  SYSTEM_EVENT = 'system_event',
  COMPLIANCE_EVENT = 'compliance_event',
}

class AuditLogger {
  private readonly hashSecret: string;

  constructor() {
    this.hashSecret = process.env.AUDIT_HASH_SECRET || 'default-audit-secret';
  }

  /**
   * Log a security audit event with integrity protection
   */
  async logAuditEvent(event: AuditEvent): Promise<void> {
    try {
      // Create immutable audit record
      const auditRecord = this.createAuditRecord(event);

      // Log to primary logger
      logger.info('AUDIT_EVENT', auditRecord);

      // Store in Redis for real-time monitoring
      await this.storeInRedis(auditRecord);

      // Check for security alerts
      await this.checkSecurityAlerts(event);
    } catch (error) {
      // Critical: Audit logging failure should be logged but not fail the operation
      logger.error('Audit logging failed', {
        error: error instanceof Error ? error.message : String(error),
        eventType: event.eventType,
        action: event.action,
      });
    }
  }

  /**
   * Create an immutable audit record with integrity hash
   */
  private createAuditRecord(event: AuditEvent): any {
    const record = {
      id: crypto.randomUUID(),
      timestamp: event.timestamp.toISOString(),
      eventType: event.eventType,
      userId: event.userId ? this.hashPII(event.userId) : undefined,
      sessionId: event.sessionId ? this.hashPII(event.sessionId) : undefined,
      ipAddress: this.hashPII(event.ipAddress),
      userAgent: event.userAgent ? this.hashPII(event.userAgent) : undefined,
      resource: event.resource,
      resourceId: event.resourceId ? this.hashPII(event.resourceId) : undefined,
      action: event.action,
      result: event.result,
      severity: event.severity,
      details: event.details ? this.sanitizeDetails(event.details) : undefined,
    };

    // Create integrity hash
    const recordString = JSON.stringify(record);
    const hash = crypto.createHmac('sha256', this.hashSecret).update(recordString).digest('hex');

    return {
      ...record,
      integrity_hash: hash,
    };
  }

  /**
   * Hash PII data for privacy protection
   */
  private hashPII(data: string): string {
    return crypto
      .createHash('sha256')
      .update(data + this.hashSecret)
      .digest('hex')
      .substring(0, 16);
  }

  /**
   * Sanitize audit details to remove sensitive information
   */
  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(details)) {
      // Skip sensitive fields
      if (['password', 'token', 'secret', 'key', 'hash'].some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'string' && value.length > 1000) {
        sanitized[key] = value.substring(0, 1000) + '...[TRUNCATED]';
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Store audit event in Redis for real-time monitoring
   */
  private async storeInRedis(record: any): Promise<void> {
    try {
      const key = `audit:${record.eventType}:${Date.now()}:${record.id}`;
      await redisClient.setex(key, 86400, JSON.stringify(record)); // 24 hour retention

      // Add to event type index
      const indexKey = `audit-index:${record.eventType}`;
      await redisClient.lpush(indexKey, key);
      await redisClient.ltrim(indexKey, 0, 999); // Keep last 1000 events per type
    } catch (error) {
      logger.error('Failed to store audit event in Redis', error);
    }
  }

  /**
   * Check for security alerts based on audit events
   */
  private async checkSecurityAlerts(event: AuditEvent): Promise<void> {
    try {
      // Check for multiple failed login attempts
      if (event.eventType === AuditEventType.AUTHENTICATION && event.result === 'failure') {
        await this.checkFailedLoginPattern(event);
      }

      // Check for suspicious data access patterns
      if (event.eventType === AuditEventType.DATA_ACCESS && event.severity === 'high') {
        await this.checkSuspiciousDataAccess(event);
      }

      // Check for security violations
      if (event.eventType === AuditEventType.SECURITY_VIOLATION) {
        await this.alertSecurityViolation(event);
      }
    } catch (error) {
      logger.error('Security alert check failed', error);
    }
  }

  /**
   * Check for failed login patterns
   */
  private async checkFailedLoginPattern(event: AuditEvent): Promise<void> {
    const key = `failed-logins:${this.hashPII(event.ipAddress)}`;
    const count = await redisClient.incr(key);

    if (count === 1) {
      await redisClient.expire(key, 3600); // 1 hour window
    }

    if (count >= 10) {
      logger.warn('SECURITY_ALERT: Multiple failed login attempts detected', {
        ipHash: this.hashPII(event.ipAddress),
        attempts: count,
        timeWindow: '1 hour',
      });
    }
  }

  /**
   * Check for suspicious data access patterns
   */
  private async checkSuspiciousDataAccess(event: AuditEvent): Promise<void> {
    const key = `data-access:${event.userId}:${new Date().getHours()}`;
    const count = await redisClient.incr(key);

    if (count === 1) {
      await redisClient.expire(key, 3600); // 1 hour window
    }

    if (count >= 100) {
      logger.warn('SECURITY_ALERT: Suspicious data access pattern detected', {
        userHash: event.userId,
        accessCount: count,
        resource: event.resource,
      });
    }
  }

  /**
   * Alert on security violations
   */
  private async alertSecurityViolation(event: AuditEvent): Promise<void> {
    logger.error('SECURITY_ALERT: Security violation detected', {
      eventType: event.eventType,
      action: event.action,
      severity: event.severity,
      ipHash: this.hashPII(event.ipAddress),
      details: event.details,
    });

    // Store in high-priority alert queue
    const alertKey = `security-alerts:${Date.now()}:${crypto.randomUUID()}`;
    await redisClient.setex(alertKey, 604800, JSON.stringify(event)); // 7 days retention
  }

  /**
   * Retrieve audit events for compliance reporting
   */
  async getAuditEvents(eventType?: AuditEventType, startDate?: Date, endDate?: Date, limit: number = 100): Promise<any[]> {
    try {
      const pattern = eventType ? `audit-index:${eventType}` : 'audit-index:*';
      const keys = await redisClient.keys(pattern);
      const events: any[] = [];

      for (const indexKey of keys) {
        const eventKeys = await redisClient.lrange(indexKey, 0, limit - 1);

        for (const eventKey of eventKeys) {
          const eventData = await redisClient.get(eventKey);
          if (eventData) {
            const event = JSON.parse(eventData);

            // Filter by date range if specified
            if (startDate && new Date(event.timestamp) < startDate) continue;
            if (endDate && new Date(event.timestamp) > endDate) continue;

            events.push(event);
          }
        }
      }

      return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      logger.error('Failed to retrieve audit events', error);
      return [];
    }
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();

/**
 * Convenience functions for common audit events
 */
export const AuditHelpers = {
  logAuthentication: (userId: string, ipAddress: string, userAgent: string, success: boolean, details?: any) => {
    return auditLogger.logAuditEvent({
      eventType: AuditEventType.AUTHENTICATION,
      userId,
      ipAddress,
      userAgent,
      action: 'login',
      result: success ? 'success' : 'failure',
      severity: success ? 'low' : 'medium',
      details,
      timestamp: new Date(),
    });
  },

  logDataAccess: (userId: string, resource: string, resourceId: string, ipAddress: string, details?: any) => {
    return auditLogger.logAuditEvent({
      eventType: AuditEventType.DATA_ACCESS,
      userId,
      resource,
      resourceId,
      ipAddress,
      action: 'read',
      result: 'success',
      severity: 'low',
      details,
      timestamp: new Date(),
    });
  },

  logSecurityViolation: (ipAddress: string, userAgent: string, violation: string, details?: any) => {
    return auditLogger.logAuditEvent({
      eventType: AuditEventType.SECURITY_VIOLATION,
      ipAddress,
      userAgent,
      action: violation,
      result: 'blocked',
      severity: 'high',
      details,
      timestamp: new Date(),
    });
  },
};
