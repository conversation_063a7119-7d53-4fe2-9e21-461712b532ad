import { Var } from '../var';
import fs from 'fs';
import path from 'path';

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

enum LogLevel {
  ERROR = 'ERROR',
  WARN = 'WARN',
  INFO = 'INFO',
  DEBUG = 'DEBUG',
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
}

const sensitivePatterns = [
  {
    regex: /(password["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
  {
    regex: /(token["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
  {
    regex: /(secret["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
  {
    regex: /(key["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
  {
    regex: /(authorization["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
  {
    regex: /(cookie["']?\s*:\s*["']?)([^"']+)(["']?)/gi,
    replacement: '$1[REDACTED]$3',
  },
];

function maskSensitiveData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'string') {
    let maskedData = data;
    sensitivePatterns.forEach(pattern => {
      maskedData = maskedData.replace(pattern.regex, pattern.replacement);
    });
    return maskedData;
  }

  if (typeof data === 'object') {
    if (Array.isArray(data)) {
      return data.map(item => maskSensitiveData(item));
    }

    const maskedObj: any = {};
    for (const key in data) {
      if (key === 'stack' || key === 'message') {
        maskedObj[key] = data[key];
      } else {
        maskedObj[key] = maskSensitiveData(data[key]);
      }
    }
    return maskedObj;
  }

  return data;
}

function formatLogEntry(entry: LogEntry): string {
  const { timestamp, level, message, data } = entry;
  let logString = `[${timestamp}] ${level}: ${message}`;

  if (data) {
    const maskedData = maskSensitiveData(data);
    logString += `\n${JSON.stringify(maskedData, null, 2)}`;
  }

  return logString;
}

function writeToFile(entry: LogEntry): void {
  const date = new Date().toISOString().split('T')[0];
  const logFile = path.join(logsDir, `${date}.log`);

  const logString = formatLogEntry(entry) + '\n';

  fs.appendFile(logFile, logString, err => {
    if (err) {
      // Only output to console in development environment for file write errors
      if (Var.node.env === 'dev') {
        console.error(`[DEV] Failed to write to log file: ${err.message}`);
      }
    }
  });
}

function createLogEntry(level: LogLevel, message: string, data?: any): LogEntry {
  return {
    timestamp: new Date().toISOString(),
    level,
    message,
    data,
  };
}

export const logger = {
  /**
   * Log error messages - always written to file, console output only in development
   */
  error: (message: string, data?: any) => {
    const entry = createLogEntry(LogLevel.ERROR, message, data);
    // Always write to file for errors
    writeToFile(entry);
    // Console output only in development environment
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} [DEV] ${formatLogEntry(entry)}`);
    }
  },

  /**
   * Log warning messages - always written to file, console output only in development
   */
  warn: (message: string, data?: any) => {
    const entry = createLogEntry(LogLevel.WARN, message, data);
    // Always write to file for warnings
    writeToFile(entry);
    // Console output only in development environment
    if (Var.node.env === 'dev') {
      console.warn(`${Var.app.emoji.warning} [DEV] ${formatLogEntry(entry)}`);
    }
  },

  /**
   * Log info messages - always written to file, console output only in development
   */
  info: (message: string, data?: any) => {
    const entry = createLogEntry(LogLevel.INFO, message, data);
    // Always write to file for info
    writeToFile(entry);
    // Console output only in development environment
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.info} [DEV] ${formatLogEntry(entry)}`);
    }
  },

  /**
   * Log debug messages - only in development environment
   */
  debug: (message: string, data?: any) => {
    if (Var.node.env === 'dev') {
      const entry = createLogEntry(LogLevel.DEBUG, message, data);
      console.log(`${Var.app.emoji.info} [DEV-DEBUG] ${formatLogEntry(entry)}`);
      writeToFile(entry);
    }
  },

  request: (req: any, res: any) => {
    if (req.path === '/health') return;

    const data = {
      method: req.method,
      path: req.path,
      params: req.params,
      query: req.query,
      body: req.body,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
        'accept': req.headers['accept'],
        'origin': req.headers['origin'],
        'referer': req.headers['referer'],
      },
      ip: req.ip,
      statusCode: res.statusCode,
      responseTime: res.locals?.responseTime || 0,
    };

    logger.info(`HTTP ${req.method} ${req.path}`, data);
  },
};
