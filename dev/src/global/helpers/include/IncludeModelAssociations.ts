import { accountModel } from '../../../components/account/models';
import { surveyModel } from '../../../components/survey/models';
import { responseModel } from '../../../components/response/models';
import { waitlistModel } from '../../../components/waitlist/models';

export const IncludeModelAssociations = () => {
  // Import waitlist model to ensure it's included in database synchronization
  // Note: waitlistModel doesn't require associations as it's a standalone table
  waitlistModel;

  // AccountModel & SurveyModel Associations
  accountModel.hasMany(surveyModel, {
    foreignKey: 'account_id',
  });
  surveyModel.belongsTo(accountModel, {
    foreignKey: 'account_id',
  });

  // SurveyModel & ResponseModel Associations
  surveyModel.hasMany(responseModel, {
    foreignKey: 'survey_id',
  });
  responseModel.belongsTo(surveyModel, {
    foreignKey: 'survey_id',
  });

  // AccountModel & ResponseModel Associations
  accountModel.hasMany(responseModel, {
    foreignKey: 'account_id',
  });
  responseModel.belongsTo(accountModel, {
    foreignKey: 'account_id',
  });
};
