import { Request, Response, NextFunction } from 'express';
import { Var } from '../../var';
import { logger } from '../../services/logger';

export const ExtractIPAddressFromOrigin = (req: Request, res: Response, next: NextFunction) => {
  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;

  if (!ip) {
    logger.warn('Invalid IP address');
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Invalid IP`,
    });
  }

  res.locals.clientIPAddress = ip;
  logger.debug('IP address extracted', { ip: res.locals.clientIPAddress });
  next();
};
