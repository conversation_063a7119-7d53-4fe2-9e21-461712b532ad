import { Request, Response, NextFunction } from 'express';
import { Var } from '../../var';

/**
 * Middleware to extract the origin from the request headers
 * Optimized to reduce logging overhead in production
 */
export const ExtractOriginFromRequest = (req: Request, res: Response, next: NextFunction) => {
  // Extract origin from request headers
  res.locals.origin = req.header('Origin') || '';

  // Only log in development mode to reduce overhead in production
  if (Var.node.env === 'dev') {
    console.log(`${Var.app.emoji.success} Request origin: ${res.locals.origin}`);
  }

  next();
};
