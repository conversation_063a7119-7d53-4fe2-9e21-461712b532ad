import { Request, Response, NextFunction } from 'express';
import { readAccountById } from '../../../../components/account/dals';
import { Var } from '../../../var';

export const BlockNonExistentAccountById = async (req: Request, res: Response, next: NextFunction) => {
  let account = await readAccountById(res.locals.accountId);

  if (!account) {
    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.failure} [DEV] ${res.locals.accountId} is not registered or invalid`);
    }

    // Clear the session cookie immediately
    res.clearCookie(Var.node.express.session.name, {
      path: '/',
      httpOnly: true,
      secure: Var.node.env === 'prod',
      sameSite: 'lax',
      domain: Var.node.env === 'prod' ? Var.app.domain : undefined,
    });

    // Clear the invalid session (async operation, but we don't wait for it)
    if (req.session) {
      req.session.destroy((error: Error) => {
        if (error) {
          // DEBUG: Console output in development only
          if (Var.node.env === 'dev') {
            console.error(`${Var.app.emoji.failure} [DEV] Error destroying invalid session:`, error);
          }
        } else {
          // DEBUG: Console output in development only
          if (Var.node.env === 'dev') {
            console.log(`${Var.app.emoji.success} [DEV] Invalid session destroyed`);
          }
        }
      });
    }

    return res.status(200).json({
      success: false,
      message: `${Var.app.emoji.failure} Invalid session. Please log in again.`,
    });
  }

  console.log(`${Var.app.emoji.success} ${res.locals.accountId} is registered`);
  res.locals.account = account;
  next();
};
