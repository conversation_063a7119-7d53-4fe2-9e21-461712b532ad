import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../global/var';
import { logger } from '../../services';
import { checkSurveyOwnership } from '../../../components/survey/dals';

export const ValidateSurveyOwnership = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const surveyId = req.params.surveyId;
    const accountId = req.session?.accountId;

    if (!surveyId) {
      logger.warn(`No survey ID provided in request`, {
        path: req.path,
        params: req.params,
      });

      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} No survey ID provided`,
      });
    }

    if (!accountId) {
      logger.warn(`No account ID in session for survey ownership check`, {
        surveyId,
        path: req.path,
      });

      return res.status(401).json({
        success: false,
        message: `${Var.app.emoji.failure} Authentication required`,
      });
    }

    const isOwner = await checkSurveyOwnership(surveyId, accountId);

    if (!isOwner) {
      logger.warn(`Unauthorized survey access attempt`, {
        surveyId,
        accountId,
        path: req.path,
      });

      return res.status(403).json({
        success: false,
        message: `${Var.app.emoji.failure} You do not have permission to access this survey`,
      });
    }

    logger.debug(`Survey ownership verified`, {
      surveyId,
      accountId,
    });

    next();
  } catch (error) {
    logger.error(`Error validating survey ownership`, {
      error,
      path: req.path,
    });

    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Server error while validating resource ownership`,
    });
  }
};
