export interface VarInterface {
  app: {
    name: string;
    emoji: {
      success: string;
      failure: string;
      warning: string;
    };
    url: {
      dev: string;
      prod: string;
    };
    api: {
      version: string;
    };
  };
  postgres: {
    host: string;
    database: string;
    user: string;
    password: string;
  };
  redis: {
    host: string;
    port: number;
    password: string | undefined;
    cacheTTL: number;
  };
  node: {
    env: string;
    port: number;
  };
}
