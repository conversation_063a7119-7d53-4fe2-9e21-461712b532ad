/**
 * Global respondent details options for survey components
 * Used in both create survey and edit survey components
 */

export interface RespondentDetailOption {
  value: string;
  label: string;
  inputType?: string;
}

export const RespondentDetailsOptions: RespondentDetailOption[] = [
  { value: '-', label: 'Choose Respondent Details' },
  { value: 'fullName', label: 'Full Name', inputType: 'text' },
  { value: 'email', label: 'Email', inputType: 'email' },
  { value: 'age', label: 'Age', inputType: 'select' },
  { value: 'gender', label: 'Gender', inputType: 'select' },
  { value: 'jobTitle', label: 'Job Title / Role', inputType: 'text' },
  { value: 'seniority', label: 'Seniority Level', inputType: 'select' },
  { value: 'department', label: 'Department', inputType: 'select' },
  { value: 'employmentType', label: 'Employment Type', inputType: 'select' },
  { value: 'organisationName', label: 'Organisation Name', inputType: 'text' },
  { value: 'organisationSize', label: 'Organisation Size', inputType: 'select' },
  { value: 'industry', label: 'Industry', inputType: 'select' },
];
