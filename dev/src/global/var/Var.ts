import dotenv from 'dotenv';
import { VarInterface } from '../interfaces';
dotenv.config();

export const Var: VarInterface = {
  app: {
    name: process.env.APP_NAME!,
    contact: {
      email: process.env.APP_EMAIL!,
    },
    domain: process.env.APP_DOMAIN!,
    emoji: {
      success: '✅',
      failure: '❌',
      warning: '⚠️',
      info: 'ℹ️',
    },
    url: {
      dev: process.env.APP_URL_DEV!,
      prod: process.env.APP_URL_PROD!,
    },
    website: {
      url: process.env.APP_WEBSITE_URL!,
    },
    owner: {
      name: process.env.OWNER_NAME!,
      website: {
        url: process.env.OWNER_WEBSITE_URL!,
      },
      contact: {
        address: process.env.OWNER_ADDRESS!,
        email: process.env.OWNER_EMAIL!,
      },
    },
    api: {
      version: process.env.API_VERSION!,
    },
  },
  redis: {
    host: process.env.REDIS_HOST!,
    port: process.env.REDIS_PORT!,
    password: process.env.REDIS_PASSWORD || '',
  },
  postgres: {
    host: process.env.POSTGRES_HOST!,
    database: process.env.POSTGRES_DATABASE!,
    user: process.env.POSTGRES_USER!,
    password: process.env.POSTGRES_PASSWORD!,
  },
  postmark: {
    token: process.env.POSTMARK_TOKEN!,
    template: {
      accountChangeNotification: {
        id: parseInt(process.env.POSTMARK_TEMPLATE_ACCOUNT_CHANGE_NOTIFICATION!),
      },
      emailVerificationCode: {
        id: parseInt(process.env.POSTMARK_TEMPLATE_EMAIL_VERIFICATION_CODE!),
      },
      passwordResetCode: {
        id: parseInt(process.env.POSTMARK_TEMPLATE_PASSWORD_RESET_CODE!),
      },
    },
  },
  node: {
    env: process.env.NODE_ENV!,
    port: Number(process.env.NODE_PORT),
    express: {
      session: {
        secret: process.env.EXPRESS_SESSION_SECRET!,
        name: process.env.EXPRESS_SESSION_NAME!,
        maxAge: process.env.EXPRESS_SESSION_TIMEOUT!,
      },
    },
    db: {
      reset: process.env.RESET_DB === 'true',
      alter: process.env.ALTER_DB === 'true',
    },
  },
  google: {
    oauth: {
      clientId: process.env.GOOGLE_OAUTH_CLIENT_ID!,
    },
  },
};
