import { newSpecPage } from '@stencil/core/testing';
import { PModal } from '../p-modal';

describe('p-modal', () => {
  it('renders when isOpen is true', async () => {
    const page = await newSpecPage({
      components: [PModal],
      html: `<p-modal is-open="true" modal-title="Test Modal"></p-modal>`,
    });
    expect(page.root).toEqualHtml(`
      <p-modal is-open="true" modal-title="Test Modal">
        <mock:shadow-root>
          <div class="modal-backdrop">
            <div class="modal-container">
              <div class="modal-header">
                <e-text variant="heading">Test Modal</e-text>
                <e-button variant="link"></e-button>
              </div>
              <div class="modal-content">
                <slot></slot>
              </div>
              <div class="modal-footer">
                <slot name="footer"></slot>
              </div>
            </div>
          </div>
        </mock:shadow-root>
      </p-modal>
    `);
  });

  it('does not render when isOpen is false', async () => {
    const page = await newSpecPage({
      components: [PModal],
      html: `<p-modal is-open="false" modal-title="Test Modal"></p-modal>`,
    });
    expect(page.root).toEqualHtml(`
      <p-modal is-open="false" modal-title="Test Modal">
        <mock:shadow-root></mock:shadow-root>
      </p-modal>
    `);
  });
});
