import { Component, Prop, State, h, Element } from '@stencil/core';

@Component({
  tag: 'p-notification',
  styleUrl: 'p-notification.css',
  shadow: true,
})
export class PNotification {
  @Element() el: HTMLElement;

  @Prop() message: string;

  @Prop() theme: 'success' | 'danger' | 'info' = 'info';

  @Prop() duration: number = 3000;

  @State() visible: boolean = true;

  private timeoutId: number;

  componentDidLoad() {
    this.timeoutId = window.setTimeout(() => {
      this.visible = false;
    }, this.duration);
  }

  disconnectedCallback() {
    if (this.timeoutId) {
      window.clearTimeout(this.timeoutId);
    }
  }

  render() {
    if (!this.visible) {
      return null;
    }

    return (
      <div class={`notification notification--${this.theme}`}>
        <e-text>{this.message}</e-text>
      </div>
    );
  }
}
