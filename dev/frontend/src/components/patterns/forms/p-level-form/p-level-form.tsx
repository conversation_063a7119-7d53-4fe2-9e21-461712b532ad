import { Component, Element, Event, EventEmitter, Listen, State, h } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

@Component({
  tag: 'p-variant-form',
  styleUrl: '../p-priority-item-form/p-priority-item-form.css',
  shadow: true,
})
export class PVariantForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addCustomSenseChoiceVariant',
    bubbles: true,
  })
  addCustomSenseChoiceVariantEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() variantValue: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the variant input when modal opens
    const variantInput = this.el.shadowRoot?.querySelector(
      'e-input[name="variantValue"]',
    ) as HTMLElement;
    if (variantInput) {
      setTimeout(() => variantInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'variantValue') {
      this.variantValue = value;
      // Clear variant error when user starts typing
      if (this.formErrors.variant) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.variant;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveVariant') {
      this.saveVariant();
    } else if (event.detail.action === 'cancelVariant') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    if (!this.variantValue.trim()) {
      errors.variant = 'Variant value is required';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveVariant() {
    if (!this.validateForm()) {
      return;
    }

    const variant = this.variantValue.trim();

    FrontendLogger.debug('Emitting addCustomSenseChoiceVariant with:', variant);

    this.addCustomSenseChoiceVariantEventEmitter.emit({
      variant: variant,
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="priority-item-form">
        <e-text>
          <strong>
            Variant value <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="variantValue"
          placeholder="e.g. Low, Medium, High"
          value={this.variantValue}
        ></e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-text variant="footnote">
          Keep the variant value short and clear. Use descriptive terms that respondents will easily
          understand.
        </e-text>
        {this.formErrors.variant && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.variant}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <l-row justifyContent="space-between">
          <e-button variant="light" action="cancelVariant">
            Cancel
          </e-button>
          <e-button action="saveVariant" disabled={!this.variantValue.trim()}>
            Add
          </e-button>
        </l-row>
      </div>
    );
  }
}
