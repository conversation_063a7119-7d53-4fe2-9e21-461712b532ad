import { Component, Element, Event, EventEmitter, Listen, State, h } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

interface AttributeItem {
  label: string;
  value: string;
}

@Component({
  tag: 'p-attribute-form',
  styleUrl: '../p-priority-item-form/p-priority-item-form.css',
  shadow: true,
})
export class PAttributeForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addCustomSenseChoiceAttribute',
    bubbles: true,
  })
  addCustomSenseChoiceAttributeEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() attributeLabel: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the label input when modal opens
    const labelInput = this.el.shadowRoot?.querySelector(
      'e-input[name="attributeLabel"]',
    ) as HTMLElement;
    if (labelInput) {
      setTimeout(() => labelInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'attributeLabel') {
      this.attributeLabel = value;
      // Clear label error when user starts typing
      if (this.formErrors.label) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.label;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveAttribute') {
      this.saveAttribute();
    } else if (event.detail.action === 'cancelAttribute') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    if (!this.attributeLabel.trim()) {
      errors.label = 'Attribute name is required';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveAttribute() {
    if (!this.validateForm()) {
      return;
    }

    // Generate value from label
    const attributeValue = this.attributeLabel
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    const attribute: AttributeItem = {
      label: this.attributeLabel.trim(),
      value: attributeValue,
    };

    FrontendLogger.debug('Emitting addCustomSenseChoiceAttribute with:', attribute);

    this.addCustomSenseChoiceAttributeEventEmitter.emit({
      attribute: attribute,
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="priority-item-form">
        <e-text>
          <strong>
            Attribute name <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="attributeLabel"
          placeholder="e.g. Price, Quality, Design"
          value={this.attributeLabel}
        ></e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-text variant="footnote">
          Keep the attribute name short and clear. Examples: Price, Quality, Speed, Design, Features
        </e-text>
        {this.formErrors.label && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.label}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <l-row justifyContent="space-between">
          <e-button variant="light" action="cancelAttribute">
            Cancel
          </e-button>
          <e-button action="saveAttribute" disabled={!this.attributeLabel.trim()}>
            Add
          </e-button>
        </l-row>
      </div>
    );
  }
}
