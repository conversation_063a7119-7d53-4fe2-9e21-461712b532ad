import { Component, Event, EventEmitter, State, Listen, Prop, h, Element } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

export interface PriorityItem {
  title: string;
  value: string;
}

/**
 * Component for creating and editing priority items
 */
@Component({
  tag: 'p-priority-item-form',
  styleUrl: 'p-priority-item-form.css',
  shadow: true,
})
export class PPriorityItemForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addCustomPriorityItem',
    bubbles: true,
  })
  addCustomPriorityItemEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @Prop() editingItem: string = '';
  @Prop() isEditMode: boolean = false;

  @State() itemTitle: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentWillLoad() {
    if (this.isEditMode && this.editingItem) {
      this.populateFormForEditing(this.editingItem);
    } else {
      this.resetForm();
    }
  }

  componentDidLoad() {
    // Focus on the title input when modal opens
    const titleInput = this.el.shadowRoot?.querySelector(
      'e-input[name="itemTitle"]',
    ) as HTMLElement;
    if (titleInput) {
      setTimeout(() => titleInput.focus(), 100);
    }
  }

  private populateFormForEditing(itemJson: string) {
    try {
      const item: PriorityItem = JSON.parse(itemJson);

      this.itemTitle = item.title || '';
      this.formErrors = {};
    } catch (error) {
      FrontendLogger.error('Error parsing editing item:', error);
    }
  }

  private resetForm() {
    this.itemTitle = '';
    this.formErrors = {};
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'itemTitle') {
      this.itemTitle = value;
      // Clear title error when user starts typing
      if (this.formErrors.title) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.title;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'savePriorityItem') {
      this.savePriorityItem();
    } else if (event.detail.action === 'cancelPriorityItem') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    // Validate title (required)
    if (!this.itemTitle.trim()) {
      errors.title = 'Title is required';
    } else if (this.itemTitle.trim().length < 2) {
      errors.title = 'Title must be at least 2 characters long';
    } else if (this.itemTitle.trim().length > 100) {
      errors.title = 'Title must be less than 100 characters';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private savePriorityItem() {
    if (!this.validateForm()) {
      return;
    }

    // Generate value from title
    const itemValue = this.itemTitle
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    const priorityItem: PriorityItem = {
      title: this.itemTitle.trim(),
      value: itemValue,
    };

    FrontendLogger.debug('Emitting addCustomPriorityItem with:', priorityItem);

    this.addCustomPriorityItemEventEmitter.emit({
      priorityItem: priorityItem,
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="priority-item-form">
        <e-text>
          <strong>
            Describe the option <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="itemTitle"
          placeholder="e.g. Multi-lingual docs, dark mode"
          value={this.itemTitle}
        ></e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-text variant="footnote">Keep the description short and clear in just one line</e-text>
        {this.formErrors.title && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.title}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <l-row justifyContent="space-between">
          <e-button variant="light" action="cancelPriorityItem">
            Cancel
          </e-button>
          <e-button action="savePriorityItem" disabled={!this.itemTitle.trim()}>
            {this.isEditMode ? 'Update' : 'Add'}
          </e-button>
        </l-row>
      </div>
    );
  }
}
