import { Component, Event, EventEmitter, State, Listen, h, Element } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

/**
 * Component for creating SensePoll answers
 */
@Component({
  tag: 'p-poll-answer-form',
  styleUrl: 'p-poll-answer-form.css',
  shadow: true,
})
export class PPollAnswerForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addSensePollChoiceFromModal',
    bubbles: true,
  })
  addSensePollChoiceFromModalEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() choiceValue: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the choice input when modal opens
    const choiceInput = this.el.shadowRoot?.querySelector(
      'e-input[name="choiceValue"]',
    ) as HTMLElement;
    if (choiceInput) {
      setTimeout(() => choiceInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'choiceValue') {
      this.choiceValue = value;
      // Clear choice error when user starts typing
      if (this.formErrors.choice) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.choice;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveChoice') {
      this.saveChoice();
    } else if (event.detail.action === 'cancelChoice') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    // Validate choice (required)
    if (!this.choiceValue.trim()) {
      errors.choice = 'Choice is required';
    } else if (this.choiceValue.trim().length < 1) {
      errors.choice = 'Choice must be at least 1 character long';
    } else if (this.choiceValue.trim().length > 100) {
      errors.choice = 'Choice must be less than 100 characters';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveChoice() {
    if (!this.validateForm()) {
      return;
    }

    FrontendLogger.debug('Emitting addSensePollChoiceFromModal with:', this.choiceValue.trim());

    this.addSensePollChoiceFromModalEventEmitter.emit({
      choice: this.choiceValue.trim(),
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="poll-answer-form">
        <e-text>
          <strong>
            Enter Answer <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.25}></l-spacer>
        <e-input
          type="text"
          name="choiceValue"
          placeholder="e.g. Yes, No, Maybe"
          value={this.choiceValue}
        ></e-input>
        {this.formErrors.choice && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.choice}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>

        <l-row justifyContent="flex-end">
          <e-button variant="light" action="cancelChoice">
            Cancel
          </e-button>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-button action="saveChoice" disabled={!this.choiceValue.trim()}>
            Add
          </e-button>
        </l-row>
      </div>
    );
  }
}
