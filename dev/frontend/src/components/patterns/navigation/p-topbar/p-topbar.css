:host {
  width: 1024px;
  position: fixed;
  background: var(--color__bg);
  display: block;
  padding: 1em 0 0.75em 0;
  z-index: 9;
  box-sizing: border-box;
  border-bottom: var(--border);
}

.hide-on-mobile {
  display: block;
}

.show-on-mobile {
  display: none;
}

@media only screen and (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
  .show-on-mobile {
    display: block;
  }

  #mobile-menu {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: var(--color__bg);
    box-sizing: border-box;
    padding: 0.5em 1em 2em 2em;
  }

  #mobile-menu__inner e-image {
    margin-top: 5px;
  }

  e-list {
    font-size: 1.2em;
  }
}
