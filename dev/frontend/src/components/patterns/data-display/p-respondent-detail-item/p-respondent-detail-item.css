.detail-item {
  display: flex;
  flex-direction: column;
  padding: calc(var(--padding) * 2) 0;
  border-bottom: var(--border);
  list-style-type: none;
  margin-left: 0;
}

.detail-item:last-child {
  border-bottom: none;
}

/* Removed detail-header class as we're using l-row instead */

.optional-star {
  color: var(--color__red--600);
  margin-left: 0.25rem;
  font-weight: bold;
}

.required-badge {
  color: var(--color__red--600);
  margin-left: 0.25rem;
  font-weight: bold;
}

.detail-actions {
  display: flex;
  gap: 0.5rem;
}

.detail-content {
  margin-top: 0.5rem;
}

.detail-info {
  display: flex;
  flex-direction: column;
}

.detail-info-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.required-text {
  color: var(--color__red--600);
  font-weight: 500;
}

.optional-text {
  color: var(--color__grey--600);
}

.detail-label {
  color: var(--color__grey--700);
  font-weight: 600;
  display: block;
}

.detail-section {
  display: flex;
  flex-direction: column;
}

.options-list {
  list-style-type: disc;
  margin: 0.25rem 0 0 1.5rem;
  padding: 0;
}

.option-item {
  margin-bottom: 0.25rem;
}

.option-item:last-child {
  margin-bottom: 0;
}
