import { Component, Event, EventEmitter, Prop, State, Watch, h } from '@stencil/core';

export interface PriorityItem {
  title: string;
  value: string;
}

@Component({
  tag: 'p-priority-item',
  styleUrl: 'p-priority-item.css',
  shadow: true,
})
export class PPriorityItem {
  @Event({
    eventName: 'priorityItemDeleteEvent',
    bubbles: true,
  })
  priorityItemDeleteEventEmitter: EventEmitter;

  @Event({
    eventName: 'priorityItemEditEvent',
    bubbles: true,
  })
  priorityItemEditEventEmitter: EventEmitter;

  @Prop() item: string;
  @Prop() index: number;

  @State() itemObj: PriorityItem;

  @Watch('item')
  watchItemProp(newValue: string) {
    if (newValue) {
      this.itemObj = JSON.parse(newValue);
    }
  }

  componentWillLoad() {
    if (this.item) {
      this.itemObj = JSON.parse(this.item);
    }
  }

  private handleDelete() {
    this.priorityItemDeleteEventEmitter.emit({
      value: this.itemObj.value,
      index: this.index,
    });
  }

  private handleEdit() {
    this.priorityItemEditEventEmitter.emit({
      item: this.itemObj,
      index: this.index,
    });
  }

  render() {
    if (!this.itemObj) {
      return null;
    }

    return (
      <div class="priority-item">
        <div class="item-content">
          <div class="item-header">
            <e-text variant="body">{this.itemObj.title}</e-text>
            <div class="item-actions">
              <e-button variant="link" onClick={() => this.handleEdit()}>
                <e-image src="../../../assets/icon/dark/edit-dark.svg" width="1em"></e-image>
              </e-button>
              <e-button variant="link" onClick={() => this.handleDelete()}>
                <e-image src="../../../assets/icon/red/x-red.svg" width="1em"></e-image>
              </e-button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
