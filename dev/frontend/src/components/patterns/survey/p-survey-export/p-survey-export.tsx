import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'p-survey-export',
  styleUrl: 'p-survey-export.css',
  shadow: true,
})
export class PSurveyExport {
  @Prop() surveyId: string;
  @Prop() survey: any;

  render() {
    return (
      <div>
        <e-text variant="heading">Export</e-text>
        <l-spacer value={1}></l-spacer>
        <c-card>
          <l-row justifyContent="space-between">
            <div class="export-option">
              <e-text variant="subheading">CSV Export</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="footnote">Raw response data in spreadsheet format</e-text>
              <l-spacer value={1}></l-spacer>
              <e-button variant="outline" disabled>
                Download CSV
              </e-button>
            </div>

            <div class="export-option">
              <e-text variant="subheading">PDF Report</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="footnote">Formatted summary report with charts</e-text>
              <l-spacer value={1}></l-spacer>
              <e-button variant="outline" disabled>
                Download PDF
              </e-button>
            </div>

            <div class="export-option">
              <e-text variant="subheading">JSON Data</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="footnote">Raw data in JSON format for developers</e-text>
              <l-spacer value={1}></l-spacer>
              <e-button variant="outline" disabled>
                Download JSON
              </e-button>
            </div>
          </l-row>

          <l-spacer value={2}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1}></l-spacer>

          <e-text variant="footnote">
            Export options will be enabled once you have survey responses. All exports include only
            the data you have permission to access.
          </e-text>
        </c-card>
      </div>
    );
  }
}
