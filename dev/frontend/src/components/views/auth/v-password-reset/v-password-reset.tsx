import { Component, State, Host, Listen, FunctionalComponent, h } from '@stencil/core';
import { MailPayloadInterface } from '../../../../global/script/interfaces';
import {
  GenerateMailPayload,
  ValidateMailPayload,
  MailApi,
} from '../../../../global/script/helpers';
import { passwordResetPayloadInterface } from './interfaces';
import {
  generatePasswordResetPayload,
  passwordResetApi,
  validatePasswordResetPayload,
} from './helpers';
import { Store } from '../../../../global/script/store';
import { Var } from '../../../../global/script/var';

@Component({
  tag: 'v-password-reset',
  styleUrl: 'v-password-reset.css',
  shadow: true,
})
export class VPasswordReset {
  BannerEl: HTMLCBannerElement;

  @State() isMailingPasswordResetCode: boolean = false;
  @State() isSavingNewPassword: boolean = false;

  @State() compState: string = 'sendPasswordResetCode';

  private email: string = '';
  private passwordResetCode: string = '';
  private newPassword: string = '';
  private newPasswordRepeat: string = '';

  @Listen('inputEvent') handleInputEvent(e) {
    if (e.detail.name === 'email') {
      this.email = e.detail.value;
    } else if (e.detail.name === 'passwordResetCode') {
      this.passwordResetCode = e.detail.value;
    } else if (e.detail.name === 'newPassword') {
      this.newPassword = e.detail.value;
    } else if (e.detail.name === 'newPasswordRepeat') {
      this.newPasswordRepeat = e.detail.value;
    }
  }

  @Listen('buttonClickEvent') handleButtonClickEvent(e) {
    if (e.detail.action === 'mailPasswordResetCode') {
      this.mailPasswordResetCode();
    } else if (e.detail.action === 'backToSendPasswordResetCode') {
      this.compState = 'sendPasswordResetCode';
    } else if (e.detail.action === 'resetPassword') {
      this.resetPassword();
    }
  }

  async mailPasswordResetCode() {
    let mailPasswordResetCodePayload: MailPayloadInterface = GenerateMailPayload(
      this.email,
      'passwordResetCode',
    );
    let { isValid, validationMessage } = ValidateMailPayload(mailPasswordResetCodePayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isMailingPasswordResetCode = true;
    await MailApi(mailPasswordResetCodePayload);
    this.isMailingPasswordResetCode = false;
    this.compState = 'confirmPassword';
  }

  async resetPassword() {
    let passwordResetPayload: passwordResetPayloadInterface = generatePasswordResetPayload(
      this.email,
      this.newPassword,
      this.newPasswordRepeat,
      this.passwordResetCode,
    );
    let { isValid, validationMessage } = validatePasswordResetPayload(passwordResetPayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isSavingNewPassword = true;
    let { success, message } = await passwordResetApi(passwordResetPayload);
    this.isSavingNewPassword = false;
    if (!success) {
      return alert(message);
    }
    this.compState = 'passwordSaved';
  }

  componentWillLoad() {
    Store.activeView = 'resetPassword';
    document.title = 'Reset Password | Sensefolks';
  }

  SendPasswordResetCode: FunctionalComponent = () => [
    <e-text variant="display">Password Reset</e-text>,
    <l-spacer value={0.5}></l-spacer>,
    <e-text variant="footnote">STEP 1 OF 2 — ENTER YOUR EMAIL</e-text>,
    <l-spacer value={2}></l-spacer>,
    <e-input type="email" name="email" placeholder="Email"></e-input>,
    <l-spacer value={2}></l-spacer>,
    <l-row justifyContent="space-between">
      <e-link url="../">Back</e-link>
      <e-button action="mailPasswordResetCode" active={this.isMailingPasswordResetCode}>
        Send reset code
      </e-button>
    </l-row>,
  ];

  ConfirmPassword: FunctionalComponent = () => [
    <e-text variant="display">Password Reset</e-text>,
    <l-spacer value={0.5}></l-spacer>,
    <e-text variant="footnote">STEP 2 OF 2 — CONFIRM YOUR PASSWORD</e-text>,
    <l-spacer value={2}></l-spacer>,
    <c-banner theme="success">
      <e-text>
        Password reset code sent to <u>{this.email}</u>
      </e-text>
    </c-banner>,
    <l-spacer value={2}></l-spacer>,
    <e-input
      type="password"
      name="newPassword"
      placeholder="New password (min 8 chars)"
      value=""
    ></e-input>,
    <l-spacer value={2}></l-spacer>,
    <e-input
      type="password"
      name="newPasswordRepeat"
      placeholder="Repeat new password"
      value=""
    ></e-input>,
    <l-spacer value={2}></l-spacer>,
    <e-input
      type="text"
      name="passwordResetCode"
      placeholder="6-digit reset code"
      value=""
    ></e-input>,
    <l-spacer value={2}></l-spacer>,
    <l-row justifyContent="space-between">
      <e-button action="backToSendPasswordResetCode" variant="light">
        Back
      </e-button>
      <e-button action="resetPassword" active={this.isSavingNewPassword}>
        Confirm
      </e-button>
    </l-row>,
  ];

  Success: FunctionalComponent = () => (
    <c-banner theme="success">
      <e-text>
        <strong>Password Changed</strong>
      </e-text>
      <e-link url="/login">Proceed to login →</e-link>
    </c-banner>
  );

  render() {
    return (
      <Host>
        <div class="form">
          <e-image src={Var.app.branding.logo.logotype.withoutBg} width="180px"></e-image>
          <l-spacer value={1}></l-spacer>
          <p-dotgrid height="50px" width="100%"></p-dotgrid>
          <l-spacer value={2}></l-spacer>
          {this.compState === 'sendPasswordResetCode' && (
            <this.SendPasswordResetCode></this.SendPasswordResetCode>
          )}
          {this.compState === 'confirmPassword' && <this.ConfirmPassword></this.ConfirmPassword>}
          {this.compState === 'passwordSaved' && <this.Success></this.Success>}
        </div>
      </Host>
    );
  }
}
