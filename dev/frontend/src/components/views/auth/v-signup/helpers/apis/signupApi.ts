import { signupPayloadInterface } from '../../interfaces';
import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const signupApi = async (payload: signupPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.signup, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error during signup request:', error);
    return {
      success: false,
      message: 'Error during signup request',
      payload: null,
    };
  }
};
