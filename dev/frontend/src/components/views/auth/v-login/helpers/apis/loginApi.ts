import { loginPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

/**
 * Login API helper function
 *
 * This function handles the authentication API call with comprehensive
 * error handling and logging. It wraps the ApiWrapper to provide
 * login-specific functionality.
 *
 * Features:
 * - Standardized error handling
 * - Environment-aware debug logging
 * - Consistent response format
 * - Network error recovery
 *
 * @param payload - The validated login payload containing email and password
 * @returns Promise resolving to standardized API response
 *
 * @example
 * const result = await loginApi({ email: '<EMAIL>', password: 'password123' });
 * if (result.success) {
 *   // Handle successful login
 * }
 */
export const loginApi = async (payload: loginPayloadInterface) => {
  try {
    // DEBUG: Log API call initiation (without sensitive data)
    FrontendLogger.debug('Login API call initiated', {
      endpoint: Var.api.endpoint.account.auth.login,
      hasEmail: !!payload.email,
      hasPassword: !!payload.password,
    });

    // Make API call through ApiWrapper for consistent handling
    const result = await ApiWrapper(Var.api.endpoint.account.auth.login, {
      method: 'POST',
      body: payload,
    });

    // DEBUG: Log API response details
    FrontendLogger.debug('Login API call completed', {
      success: result.success,
      hasMessage: !!result.message,
      hasPayload: !!result.payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log network/unexpected errors
    FrontendLogger.error('Error during login request', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Error during login request',
      payload: null,
    };
  }
};
