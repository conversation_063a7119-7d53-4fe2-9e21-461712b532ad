import { Component, Prop, Host, h } from "@stencil/core";
import { Var } from "../../../../global/script/var";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-payment-failed",
  styleUrl: "v-payment-failed.css",
  shadow: true,
})
export class VPaymentFailed {
  @Prop() sessionId: string;

  componentWillLoad() {
    Store.activeView = "paymentFailed";
    document.title = "Payment Failed | Sensefolks";
  }

  render() {
    return (
      <Host>
        <c-card>
          <e-text variant="display" theme="danger">
            Payment Failed
          </e-text>
          {/* <h1 class="text--danger">Payment Failed</h1> */}
          <l-spacer value={1}></l-spacer>
          <e-text>
            Please try purchasing again. If money was deducted <br />
            from your account/card, kindly write a mail to us at:
          </e-text>
          <e-link url={`mailto:${Var.app.contact.email}`}>
            {Var.app.contact.email}
          </e-link>
          <l-spacer value={1}></l-spacer>
          <e-link url="/">Go to account</e-link>
        </c-card>
      </Host>
    );
  }
}
