import { Component, h } from "@stencil/core";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-billing",
  styleUrl: "v-billing.css",
  shadow: true,
})
export class VBilling {
  componentWillLoad() {
    Store.activeView = "billing";
    document.title = "Billing | Sensefolks";
  }

  render() {
    return (
      <c-main>
        <div class="container">
          <l-row justifyContent="flex-start">
            <e-image
              src="../../../assets/icon/dark/money-dark.svg"
              width="2em"
            ></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Billing</e-text>
          </l-row>{" "}
          <l-spacer value={2}></l-spacer>
          <e-link url="/checkout">Checkout</e-link>
          <br />
          <e-link url="/checkout/bmvbmw">Checkout (with value)</e-link>
        </div>
      </c-main>
    );
  }
}
