import { Component, h } from "@stencil/core";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-support",
  styleUrl: "v-support.css",
  shadow: true,
})
export class VSupport {
  componentWillLoad() {
    Store.activeView = "support";
    document.title = "Support | Sensefolks";
  }

  render() {
    return (
      <c-main>
        <div class="container">
          <l-row justifyContent="flex-start">
            <e-image
              src="../../../assets/icon/dark/headset-dark.svg"
              width="2em"
            ></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Support</e-text>
          </l-row>{" "}
          <l-spacer value={2}></l-spacer>
        </div>
      </c-main>
    );
  }
}
