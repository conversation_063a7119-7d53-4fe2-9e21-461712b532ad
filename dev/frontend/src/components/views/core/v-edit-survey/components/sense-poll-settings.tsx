import { Component, Prop, State, h, Listen } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
} from '../../../../../global/script/helpers';
import { Var, FrontendLogger } from '../../../../../global/script/var';

interface Choice {
  value: string;
  label: string;
}

@Component({
  tag: 'sense-poll-settings',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class SensePollSettings {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() updatingField: string = '';

  // Question field status
  @State() questionMessage: string = '';
  @State() questionSuccess: boolean = false;

  // Choice type field status
  @State() choiceTypeMessage: string = '';
  @State() choiceTypeSuccess: boolean = false;

  // Choices field status
  @State() choicesMessage: string = '';
  @State() choicesSuccess: boolean = false;

  // Follow-up choices field status
  @State() followUpChoicesMessage: string = '';
  @State() followUpChoicesSuccess: boolean = false;

  // Follow-up question field status
  @State() followUpQuestionMessage: string = '';
  @State() followUpQuestionSuccess: boolean = false;

  // Thank you message field status
  @State() thankYouMessageMessage: string = '';
  @State() thankYouMessageSuccess: boolean = false;

  @State() newChoice: string = '';
  @State() newQuestion: string = '';
  @State() newThankYou: string = '';
  @State() choiceType: string = '';
  @State() followUpChoices: Choice[] = [];
  @State() newFollowUpQuestion: string = '';
  @State() isEditingQuestion: boolean = false;
  @State() isEditingThankYou: boolean = false;
  @State() isEditingFollowUpQuestion: boolean = false;
  @State() showFollowUpQuestion: boolean = false;

  componentWillLoad() {
    if (this.survey && this.survey.config) {
      this.newQuestion = this.survey.config.question || '';
      this.newThankYou = this.survey.config.thankYouMessage || 'Thank you for your response!';
      this.choiceType = this.survey.config.choiceType || 'singleChoice';
      this.followUpChoices = this.survey.config.followUpChoices || [];
      this.newFollowUpQuestion = this.survey.config.followUpQuestion || '';
      // Only show follow-up question if there are follow-up choices selected AND more than one answer choice
      const choices = this.survey.config.choices || [];
      this.showFollowUpQuestion = this.followUpChoices.length > 0 && choices.length > 1;
    }
  }

  @Listen('editableTextEvent')
  async handleEditableTextEvent(event: CustomEvent) {
    if (event.detail.attribute === 'question') {
      await this.updateQuestion(event.detail.value);
    } else if (event.detail.attribute === 'thankYouMessage') {
      await this.updateThankYouMessage(event.detail.value);
    } else if (event.detail.attribute === 'followUpQuestion') {
      await this.updateFollowUpQuestion(event.detail.value);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    if (event.detail.name === 'newChoice') {
      this.newChoice = event.detail.value;
    } else if (event.detail.name === 'choiceType') {
      this.choiceType = event.detail.value;
      this.updateChoiceType(this.choiceType);
    } else if (event.detail.name === 'followUpChoice') {
      this.handleFollowUpChoiceChange(
        event.detail.value,
        event.detail.isChecked,
        event.detail.label,
      );
    }
  }

  @Listen('listWithDeleteEvent')
  async handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteChoice') {
      await this.removeChoice(event.detail.value);
    }
  }

  @Listen('buttonClickEvent')
  async handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addChoice') {
      await this.addChoice();
    }
  }

  // Update the question
  private async updateQuestion(newQuestion: string) {
    this.updatingField = 'question';

    if (!newQuestion || newQuestion.trim() === '') {
      this.questionMessage = 'Question cannot be empty';
      this.questionSuccess = false;
      this.updatingField = '';
      return;
    }

    const updatedConfig = { ...this.survey.config, question: newQuestion };
    await this.updateConfig(updatedConfig, { attribute: 'question', value: newQuestion });
  }

  // Update choice type
  private async updateChoiceType(newChoiceType: string) {
    this.updatingField = 'choiceType';

    const updatedConfig = { ...this.survey.config, choiceType: newChoiceType };
    await this.updateConfig(updatedConfig, { attribute: 'choiceType', value: newChoiceType });
  }

  // Add a new choice
  private async addChoice() {
    this.updatingField = 'choices';

    if (!this.newChoice || this.newChoice.trim() === '') {
      this.choicesMessage = 'Choice cannot be empty';
      this.choicesSuccess = false;
      this.updatingField = '';
      return;
    }

    const currentChoices = [...(this.survey.config.choices || [])] as Choice[];

    // Check for duplicates using for loop
    let isDuplicate = false;
    for (let i = 0; i < currentChoices.length; i++) {
      if (
        currentChoices[i].value === this.newChoice.toLowerCase().replace(/\s+/g, '-') ||
        currentChoices[i].label === this.newChoice
      ) {
        isDuplicate = true;
        break;
      }
    }

    if (isDuplicate) {
      this.choicesMessage = 'Choice already exists';
      this.choicesSuccess = false;
      this.updatingField = '';
      return;
    }

    const newChoiceObj: Choice = {
      value: this.newChoice.toLowerCase().replace(/\s+/g, '-'),
      label: this.newChoice,
    };

    const updatedChoices: Choice[] = [...currentChoices, newChoiceObj];
    const updatedConfig = { ...this.survey.config, choices: updatedChoices };

    const success = await this.updateConfig(updatedConfig, {
      attribute: 'choices',
      value: updatedChoices,
    });
    if (success) {
      this.newChoice = '';
    }
  }

  // Remove a choice
  private async removeChoice(choiceToRemove: string) {
    this.updatingField = 'choices';

    const currentChoices = [...(this.survey.config.choices || [])] as Choice[];

    // Remove choice using for loop
    let updatedChoices: Choice[] = [];
    for (let i = 0; i < currentChoices.length; i++) {
      if (currentChoices[i].value !== choiceToRemove) {
        updatedChoices.push(currentChoices[i]);
      }
    }

    // Also remove from followUpChoices if present
    let updatedFollowUpChoices: Choice[] = [];
    for (let i = 0; i < this.followUpChoices.length; i++) {
      if (this.followUpChoices[i].value !== choiceToRemove) {
        updatedFollowUpChoices.push(this.followUpChoices[i]);
      }
    }

    this.followUpChoices = updatedFollowUpChoices;
    // Only show follow-up question if there are follow-up choices selected AND more than one answer choice
    this.showFollowUpQuestion =
      updatedFollowUpChoices.length > 0 && this.survey.config.choices.length > 0;

    const updatedConfig = {
      ...this.survey.config,
      choices: updatedChoices,
      followUpChoices: updatedFollowUpChoices,
    };

    if (updatedFollowUpChoices.length === 0) {
      updatedConfig.followUpQuestion = '';
      this.newFollowUpQuestion = '';
    }

    await this.updateConfig(updatedConfig, { attribute: 'choices', value: updatedChoices });
  }

  // Handle follow-up choice change
  private async handleFollowUpChoiceChange(value: string, isChecked: boolean, label: string) {
    this.updatingField = 'followUpChoices';

    let updatedFollowUpChoices = [...this.followUpChoices];

    if (isChecked) {
      // Add to follow-up choices
      const newFollowUpChoice: Choice = {
        value: value,
        label: label,
      };

      // Check if already exists
      let exists = false;
      for (let i = 0; i < updatedFollowUpChoices.length; i++) {
        if (updatedFollowUpChoices[i].value === value) {
          exists = true;
          break;
        }
      }

      if (!exists) {
        updatedFollowUpChoices.push(newFollowUpChoice);
      }
    } else {
      // Remove from follow-up choices
      updatedFollowUpChoices = updatedFollowUpChoices.filter(choice => choice.value !== value);
    }

    this.followUpChoices = updatedFollowUpChoices;
    // Only show follow-up question if there are follow-up choices selected AND more than one answer choice
    this.showFollowUpQuestion =
      updatedFollowUpChoices.length > 0 && this.survey.config.choices.length > 0;

    const updatedConfig = {
      ...this.survey.config,
      followUpChoices: updatedFollowUpChoices,
    };

    if (updatedFollowUpChoices.length === 0) {
      updatedConfig.followUpQuestion = '';
      this.newFollowUpQuestion = '';
    }

    await this.updateConfig(updatedConfig, {
      attribute: 'followUpChoices',
      value: updatedFollowUpChoices,
    });
  }

  // Update follow-up question
  private async updateFollowUpQuestion(newQuestion: string) {
    this.updatingField = 'followUpQuestion';

    const updatedConfig = { ...this.survey.config, followUpQuestion: newQuestion };
    await this.updateConfig(updatedConfig, {
      attribute: 'followUpQuestion',
      value: newQuestion,
    });
  }

  // Update thank you message
  private async updateThankYouMessage(newMessage: string) {
    this.updatingField = 'thankYouMessage';

    const updatedConfig = { ...this.survey.config, thankYouMessage: newMessage };
    await this.updateConfig(updatedConfig, { attribute: 'thankYouMessage', value: newMessage });
  }

  // Helper method to update config
  private async updateConfig(updatedConfig: any, obj: any) {
    // Set field-specific editing states
    if (obj.attribute === 'question') {
      this.isEditingQuestion = true;
    } else if (obj.attribute === 'thankYouMessage') {
      this.isEditingThankYou = true;
    } else if (obj.attribute === 'followUpQuestion') {
      this.isEditingFollowUpQuestion = true;
    }

    const updatePayload = GenerateUpdateByAttributePayload('config', updatedConfig);

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      // Update field-specific messages based on the attribute
      if (obj.attribute === 'question') {
        this.questionMessage = message;
        this.questionSuccess = success;
      } else if (obj.attribute === 'choiceType') {
        this.choiceTypeMessage = message;
        this.choiceTypeSuccess = success;
      } else if (obj.attribute === 'choices') {
        this.choicesMessage = message;
        this.choicesSuccess = success;
      } else if (obj.attribute === 'followUpChoices') {
        this.followUpChoicesMessage = message;
        this.followUpChoicesSuccess = success;
      } else if (obj.attribute === 'followUpQuestion') {
        this.followUpQuestionMessage = message;
        this.followUpQuestionSuccess = success;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = message;
        this.thankYouMessageSuccess = success;
      }

      if (success) {
        this.survey.config = updatedConfig;
        return true;
      }
      return false;
    } catch (error) {
      // Set field-specific error messages
      const errorMessage = `An error occurred while updating ${obj.attribute}`;

      if (obj.attribute === 'question') {
        this.questionMessage = errorMessage;
        this.questionSuccess = false;
      } else if (obj.attribute === 'choiceType') {
        this.choiceTypeMessage = errorMessage;
        this.choiceTypeSuccess = false;
      } else if (obj.attribute === 'choices') {
        this.choicesMessage = errorMessage;
        this.choicesSuccess = false;
      } else if (obj.attribute === 'followUpChoices') {
        this.followUpChoicesMessage = errorMessage;
        this.followUpChoicesSuccess = false;
      } else if (obj.attribute === 'followUpQuestion') {
        this.followUpQuestionMessage = errorMessage;
        this.followUpQuestionSuccess = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = errorMessage;
        this.thankYouMessageSuccess = false;
      }

      // DEBUG: Log configuration update error
      FrontendLogger.error('Error updating poll configuration', {
        attribute: obj.attribute,
        surveyId: this.surveyId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });
      return false;
    } finally {
      // Reset field-specific editing states
      if (obj.attribute === 'question') {
        this.newQuestion = obj.value;
        this.isEditingQuestion = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.newThankYou = obj.value;
        this.isEditingThankYou = false;
      } else if (obj.attribute === 'followUpQuestion') {
        this.newFollowUpQuestion = obj.value;
        this.isEditingFollowUpQuestion = false;
      }

      // Reset the updating field
      this.updatingField = '';
    }
  }

  // Format choices for p-list-with-delete
  private formatChoicesForList(): string {
    if (!this.survey?.config?.choices?.length) return '[]';

    const choices = this.survey.config.choices as Choice[];
    return JSON.stringify(choices);
  }

  // Check if a choice is selected for follow-up
  private isChoiceSelectedForFollowUp(value: string): boolean {
    for (let i = 0; i < this.followUpChoices.length; i++) {
      if (this.followUpChoices[i].value === value) {
        return true;
      }
    }
    return false;
  }

  render() {
    const hasChoices = this.survey?.config?.choices?.length > 0;

    return (
      <div class="settings-section">
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>What poll question would you like to ask?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Question"
          type="text"
          value={this.newQuestion}
          entity="survey"
          attribute="question"
          bypass={true}
          active={this.isEditingQuestion}
        ></e-text-editable>
        {this.questionMessage && this.updatingField !== 'question' && (
          <p-notification
            message={this.questionMessage}
            theme={this.questionSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>How many answers can respondents select?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <l-row justifyContent="flex-start">
          <e-input
            type="radio"
            name="choiceType"
            value="singleChoice"
            checked={this.choiceType === 'singleChoice'}
          >
            Single Answer
          </e-input>
          <l-spacer value={1} variant="horizontal"></l-spacer>
          <e-input
            type="radio"
            name="choiceType"
            value="multiChoice"
            checked={this.choiceType === 'multiChoice'}
          >
            Multiple Answers
          </e-input>
        </l-row>
        {this.updatingField === 'choiceType' && (
          <div class="loading-indicator">
            <e-spinner theme="dark"></e-spinner>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">Updating...</e-text>
          </div>
        )}
        {this.choiceTypeMessage && this.updatingField !== 'choiceType' && (
          <p-notification
            message={this.choiceTypeMessage}
            theme={this.choiceTypeSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>What answer choices would you like to offer?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <l-row justifyContent="flex-start">
          <e-input
            type="text"
            name="newChoice"
            placeholder="e.g. Yes, No, Maybe"
            value={this.newChoice}
          ></e-input>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-button variant="ghost" disabled={!this.newChoice} action="addChoice">
            Add
          </e-button>
        </l-row>
        <l-spacer value={1}></l-spacer>
        <p-list-with-delete
          name="deleteChoice"
          items={this.formatChoicesForList()}
          emptyMessage="No answer choices added yet"
        ></p-list-with-delete>
        {this.updatingField === 'choices' && (
          <div class="loading-indicator">
            <e-spinner theme="dark"></e-spinner>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">Updating...</e-text>
          </div>
        )}
        {this.choicesMessage && this.updatingField !== 'choices' && (
          <p-notification
            message={this.choicesMessage}
            theme={this.choicesSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
        <l-spacer value={3}></l-spacer>
        {hasChoices && this.survey.config.choices.length > 1 && (
          <div>
            <e-text>
              <strong>Ask follow-up question for the following answer choices</strong> (Optional)
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            <l-row justifyContent="flex-start">
              {this.survey.config.choices.map((choice: Choice) => [
                <e-input
                  type="checkbox"
                  value={choice.value}
                  name="followUpChoice"
                  checked={this.isChoiceSelectedForFollowUp(choice.value)}
                  label={choice.label}
                >
                  {choice.label}
                </e-input>,
                <l-spacer value={1} variant="horizontal"></l-spacer>,
              ])}
            </l-row>

            {this.updatingField === 'followUpChoices' && (
              <div class="loading-indicator">
                <e-spinner theme="dark"></e-spinner>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text variant="footnote">Updating...</e-text>
              </div>
            )}

            {this.followUpChoicesMessage && this.updatingField !== 'followUpChoices' && (
              <p-notification
                message={this.followUpChoicesMessage}
                theme={this.followUpChoicesSuccess ? 'success' : 'danger'}
              ></p-notification>
            )}
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        {this.showFollowUpQuestion && this.survey.config.choices.length > 1 && (
          <div>
            <e-text>
              <strong>Enter follow-up question</strong>
            </e-text>
            <l-spacer value={0.5}></l-spacer>
            <e-text-editable
              label="Follow-up Question"
              type="text"
              value={this.newFollowUpQuestion}
              entity="survey"
              attribute="followUpQuestion"
              bypass={true}
              active={this.isEditingFollowUpQuestion}
            ></e-text-editable>

            {this.followUpQuestionMessage && this.updatingField !== 'followUpQuestion' && (
              <p-notification
                message={this.followUpQuestionMessage}
                theme={this.followUpQuestionSuccess ? 'success' : 'danger'}
              ></p-notification>
            )}

            <l-spacer value={2}></l-spacer>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>What message would you like to show after submission?</strong>
        </e-text>
        <e-text variant="footnote">
          This message will be shown to respondents after they submit their response
        </e-text>
        <l-spacer value={0.5}></l-spacer>{' '}
        <e-text-editable
          label="Thank You Message"
          type="text"
          value={this.newThankYou}
          entity="survey"
          attribute="thankYouMessage"
          bypass={true}
          active={this.isEditingThankYou}
        ></e-text-editable>
        {this.thankYouMessageMessage && this.updatingField !== 'thankYouMessage' && (
          <p-notification
            message={this.thankYouMessageMessage}
            theme={this.thankYouMessageSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
      </div>
    );
  }
}
