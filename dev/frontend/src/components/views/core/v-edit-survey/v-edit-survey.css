.container {
  width: 90%;
  max-width: 480px;
  margin: 0 auto;
}

.container__spinner {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50vh;
}

.error-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 50vh;
}

/* Tab Navigation moved to p-tabnav component */

/* Survey Type Display */
.survey-type-display {
  display: flex;
  align-items: center;
  margin-bottom: 1em;
}

/* Settings Section Styles */
.settings-section {
  padding-bottom: 6em;
}

.settings-content {
  padding: 1em;
  border: var(--border);
  border-radius: var(--border-radius);
}

/* Tab Content Container */
.tab-content {
  min-height: 300px;
}

/* Loading Indicator */
.loading-indicator {
  margin-top: 1em;
  display: flex;
  align-items: center;
}

.embed-url-section {
  margin-top: 1em;
  padding: 1em;
  border: var(--border);
  border-radius: var(--border-radius);
  background-color: var(--color__grey--50);
}

.respondent-detail-container {
  /* Removed margin-bottom as we're using l-spacer instead */
}

.respondent-detail-container:last-child {
  /* Removed margin-bottom as we're using l-spacer instead */
}

/* Removed detail-header and detail-title classes as we're using l-row instead */

.detail-actions {
  display: flex;
  gap: 0.5rem;
}

.optional-star {
  color: var(--color__red--600);
  margin-left: 0.25rem;
  font-weight: bold;
}

.required-badge {
  color: var(--color__red--600);
  margin-left: 0.25rem;
  font-weight: bold;
}

.detail-section {
  margin-bottom: 2em;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: var(--color__grey--700);
  font-weight: 600;
  display: block;
}

.options-list {
  list-style-type: disc;
  margin: 0.25rem 0 0 1.5rem;
  padding: 0;
}

.option-item {
  margin-bottom: 0.25rem;
}

.option-item:last-child {
  margin-bottom: 0;
}
