import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const getSurveyEmbedCodeApi = async (surveyId: string) => {
  try {
    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}/embed`, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log embed code fetch error
    FrontendLogger.error('Error fetching survey embed code', {
      surveyId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });
    return {
      success: false,
      message: 'Error fetching survey embed code',
      payload: null,
    };
  }
};
