import { newSpecPage } from '@stencil/core/testing';
import { VEmbedSurvey } from '../v-embed-survey';

describe('v-embed-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VEmbedSurvey],
      html: `<v-embed-survey></v-embed-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-embed-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-embed-survey>
    `);
  });
});
