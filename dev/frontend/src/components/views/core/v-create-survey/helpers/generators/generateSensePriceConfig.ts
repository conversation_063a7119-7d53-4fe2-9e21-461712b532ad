import { sensePriceConfigInterface } from '../../interfaces';

export const generateSensePriceConfig = (
  currency: string,
  priceType: string,
  recurringBasis: string,
  thankYouMessage: string,
) => {
  let payload: sensePriceConfigInterface = {
    currency: currency,
    priceType: priceType,
    recurringBasis: priceType === 'recurring' ? recurringBasis : undefined,
    thankYouMessage: thankYouMessage || 'Thank you for your response!',
  };

  return payload;
};
