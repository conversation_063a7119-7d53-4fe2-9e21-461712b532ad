import { senseChoiceConfigInterface, ConjointConcept, ConjointChoiceTask } from '../../interfaces';

export const generateSenseChoiceConfig = (
  type: string,
  attributes: { label: string; value: string }[],
  attributeVariants: { [key: string]: { label: string; value: string }[] },
  selectedConcepts: ConjointConcept[],
  choiceTasks: ConjointChoiceTask[],
  thankYouMessage: string,
) => {
  let payload: senseChoiceConfigInterface = {
    type: type,
    attributes: attributes,
    attributeVariants: attributeVariants,
    selectedConcepts: selectedConcepts,
    choiceTasks: choiceTasks,
    thankYouMessage: thankYouMessage || 'Thank you for your response!',
  };

  return payload;
};
