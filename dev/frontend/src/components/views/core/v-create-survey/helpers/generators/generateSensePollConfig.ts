import { sensePollConfigInterface } from "../../interfaces";

export const generateSensePollConfig = (
  question: string,
  choiceType: any,
  choices: any,
  followUpChoices: any,
  followUpQuestion: string,
  thankYouMessage: string
) => {
  let payload: sensePollConfigInterface = {
    question: question,
    choiceType: choiceType,
    choices: choices,
    followUpChoices: followUpChoices,
    followUpQuestion: followUpQuestion,
    thankYouMessage: thankYouMessage || "Thank you for your response!",
  };

  return payload;
};
