import { newSpecPage } from '@stencil/core/testing';
import { VCreateSurvey } from '../v-create-survey';

describe('v-create-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VCreateSurvey],
      html: `<v-create-survey></v-create-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-create-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-create-survey>
    `);
  });
});
