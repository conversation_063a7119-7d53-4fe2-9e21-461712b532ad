export interface createSurveyPayloadInterface {
  type: string;
  title: string;
  distribution: string;
  embedUrl: string;
  tags: string[];
  config: {
    [key: string]: any;
  };
  respondentDetails: Array<{
    label: string;
    value: string;
    inputType: string;
    required?: boolean;
    placeholder?: string;
    options?: Array<{
      value: string;
      label: string;
    }>;
    defaultValue?: any;
  }>;
}
