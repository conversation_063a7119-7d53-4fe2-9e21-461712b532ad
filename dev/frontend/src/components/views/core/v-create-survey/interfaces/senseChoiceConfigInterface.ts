export interface ConjointConcept {
  conceptId: string;
  attributes: { [key: string]: string }; // attributeValue -> selected variant value
  selected?: boolean;
}

export interface ConjointChoiceTask {
  taskId: string;
  taskNumber: number;
  alternatives: ConjointConcept[];
  includeNoneOption: boolean;
  selected?: boolean;
}

export interface senseChoiceConfigInterface {
  type: string; // 'lite' or 'full'
  attributes: { label: string; value: string }[];
  attributeVariants: { [key: string]: { label: string; value: string }[] }; // attributeValue -> variants array
  selectedConcepts: ConjointConcept[]; // User-curated concepts
  choiceTasks: ConjointChoiceTask[]; // User-curated tasks
  thankYouMessage: string;
}
