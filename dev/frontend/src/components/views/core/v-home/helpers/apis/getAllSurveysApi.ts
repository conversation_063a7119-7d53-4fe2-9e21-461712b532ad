import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const getAllSurveysApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.surveys, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error fetching surveys:', error);
    return {
      success: false,
      message: 'Error fetching surveys',
      payload: null,
    };
  }
};
