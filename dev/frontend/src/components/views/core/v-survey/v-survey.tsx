import { Component, h, Prop, State, FunctionalComponent, Listen } from '@stencil/core';
import { GetSurveyApi } from '../../../../global/script/helpers';
import { Store } from '../../../../global/script/store';

@Component({
  tag: 'v-survey',
  styleUrl: 'v-survey.css',
  shadow: true,
})
export class VSurvey {
  @Prop() surveyId: string;
  @State() compState: string = 'fetching';
  @State() survey: any;
  @State() activeSection: 'overview' | 'analytics' | 'responses' | 'export' = 'overview';

  componentWillLoad() {
    Store.activeView = 'survey';
    document.title = 'Survey | Sensefolks';
    this.getSurvey();
  }

  async getSurvey() {
    let { success, message, payload } = await GetSurveyApi(this.surveyId);
    if (!success) {
      this.compState = 'error';
      return alert(message);
    }
    if (!payload) {
      return alert(message);
    }
    this.survey = payload;
    this.compState = 'ready';
  }

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseChoice') {
      return <e-pill color="blue">SenseChoice</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="turquoise">SenseQuery</e-pill>;
    } else if (value === 'sensePriority') {
      return <e-pill color="teal">SensePriority</e-pill>;
    }
  }

  handleSectionChange = (section: 'overview' | 'analytics' | 'responses' | 'export') => {
    this.activeSection = section;
  };

  @Listen('sectionChange')
  handleSectionChangeEvent(event: CustomEvent<'overview' | 'analytics' | 'responses' | 'export'>) {
    this.activeSection = event.detail;
  }

  Fetching: FunctionalComponent = () => (
    <div class="centered">
      <e-spinner theme="dark"></e-spinner>
    </div>
  );

  Ready: FunctionalComponent = () => (
    <div id="ready-container">
      {/* Fixed survey header bar */}
      <div id="survey-header-bar">
        <l-row justifyContent="space-between" align="center">
          <l-row align="center">
            <e-link url="/home">←</e-link>
            <l-spacer variant="horizontal" value={0.3}></l-spacer>
            <e-text>
              <strong>{this.survey.title}</strong>
            </e-text>
            <l-spacer variant="horizontal" value={0.3}></l-spacer>
            <e-text variant="footnote">{this.generateSurveyPill(this.survey.type)}</e-text>
          </l-row>
          <l-row align="center">
            <e-text variant="footnote">
              <e-link url={`/surveys/${this.surveyId}/edit`}>Edit</e-link>
            </e-text>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            {(this.survey.distribution === 'embed' || this.survey.distribution === 'embedlink') && (
              <l-row>
                <e-text variant="footnote">/</e-text>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text variant="footnote">
                  <e-link url={`/surveys/${this.surveyId}/embed`}>Embed</e-link>
                </e-text>
              </l-row>
            )}
            {(this.survey.distribution === 'link' || this.survey.distribution === 'embedlink') && (
              <l-row>
                {this.survey.distribution === 'embedlink' && (
                  <l-spacer variant="horizontal" value={0.5}></l-spacer>
                )}
                <e-text variant="footnote">/</e-text>
                <l-spacer variant="horizontal" value={0.5}></l-spacer>
                <e-text variant="footnote">
                  <e-link url={`/surveys/${this.surveyId}/share`}>Share</e-link>
                </e-text>
              </l-row>
            )}
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">/</e-text>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">
              <e-link theme="danger" url={`/surveys/${this.surveyId}/delete`}>
                Delete
              </e-link>
            </e-text>
          </l-row>
        </l-row>
      </div>

      <l-row align="flex-start">
        <p-survey-sidebar
          surveyId={this.surveyId}
          activePage={this.activeSection}
          useClickHandlers={true}
        ></p-survey-sidebar>
        <div id="ready-container__main-content">
          <div class="content">
            {this.activeSection === 'overview' && (
              <p-survey-overview surveyId={this.surveyId} survey={this.survey} />
            )}
            {this.activeSection === 'analytics' && (
              <p-survey-analytics surveyId={this.surveyId} survey={this.survey} />
            )}
            {this.activeSection === 'responses' && (
              <p-survey-responses surveyId={this.surveyId} survey={this.survey} />
            )}
            {this.activeSection === 'export' && (
              <p-survey-export surveyId={this.surveyId} survey={this.survey} />
            )}
          </div>
        </div>
      </l-row>
    </div>
  );

  Error: FunctionalComponent = () => (
    <div id="error-container">
      <article>
        <e-text variant="display">Could not fetch survey details :(</e-text>
        <l-spacer value={1}></l-spacer>
        <e-text>
          If the issue persists, kindly visit the <e-link url="/support">Support page</e-link> and
          report the issue
        </e-text>
      </article>
    </div>
  );

  render() {
    return (
      <c-main variant="shallow">
        {this.compState === 'fetching' && <this.Fetching></this.Fetching>}
        {this.compState === 'ready' && <this.Ready></this.Ready>}
        {this.compState === 'error' && <this.Error></this.Error>}
      </c-main>
    );
  }
}
