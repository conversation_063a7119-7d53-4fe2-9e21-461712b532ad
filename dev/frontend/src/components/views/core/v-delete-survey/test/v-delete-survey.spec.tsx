import { newSpecPage } from '@stencil/core/testing';
import { VDeleteSurvey } from '../v-delete-survey';

describe('v-delete-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VDeleteSurvey],
      html: `<v-delete-survey></v-delete-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-delete-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-delete-survey>
    `);
  });
});
