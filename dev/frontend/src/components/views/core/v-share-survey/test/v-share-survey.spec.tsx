import { newSpecPage } from '@stencil/core/testing';
import { VShareSurvey } from '../v-share-survey';

describe('v-share-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VShareSurvey],
      html: `<v-share-survey></v-share-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-share-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-share-survey>
    `);
  });
});
