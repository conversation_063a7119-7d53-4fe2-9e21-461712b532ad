import { Component, Host, h } from "@stencil/core";
import { Store } from "../../../../global/script/store";
import { Router } from "../../../../";

@Component({
  tag: "v-catch-all",
  styleUrl: "v-catch-all.css",
  shadow: true,
})
export class VCatchAll {
  componentDidLoad() {
    if (!Store.isSessionActive) {
      Router.push("/login");
    } else {
      Router.push("/home");
    }
  }

  render() {
    return (
      <Host>
        <slot></slot>
      </Host>
    );
  }
}
