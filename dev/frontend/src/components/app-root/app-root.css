.hide-on-mobile {
  display: block;
}
.show-on-mobile {
  display: none;
}

#init-loader {
  width: 100vw;
  height: 100vh;
  background: var(--color__bg);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

@media only screen and (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
  .show-on-mobile {
    display: block;
  }
}
