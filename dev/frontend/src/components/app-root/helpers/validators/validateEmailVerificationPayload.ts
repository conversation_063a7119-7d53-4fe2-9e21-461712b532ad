import Joi from 'joi';
import { emailVerificationPayloadInterface } from '../../interfaces';

const validateEmailVerificationSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .min(5)
    .max(128)
    .lowercase()
    .trim()
    .required(),
  emailVerificationCode: Joi.string().trim().length(6).required(),
});

export const validateEmailVerificationPayload = (
  emailVerificationPayload: emailVerificationPayloadInterface,
) => {
  let { error } = validateEmailVerificationSchema.validate(emailVerificationPayload);
  if (error) {
    return { isValid: false, validationMessage: error.details[0].message };
  } else {
    return { isValid: true, validationMessage: '' };
  }
};
