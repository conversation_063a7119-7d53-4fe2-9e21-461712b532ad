import { emailVerificationPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../../../global/script/var';
import { ApiWrapper } from '../../../../global/script/helpers/api/ApiWrapper';

export const emailVerificationApi = async (payload: emailVerificationPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.email.verify, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log email verification error
    FrontendLogger.error('Error in emailVerificationApi', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Error verifying email',
      payload: null,
    };
  }
};
