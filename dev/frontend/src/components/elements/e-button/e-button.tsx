import { Component, Event, EventEmitter, Watch, Prop, State, h } from '@stencil/core';

@Component({
  tag: 'e-button',
  styleUrl: 'e-button.css',
  shadow: true,
})
export class EButton {
  buttonEl!: HTMLButtonElement;

  @Prop() action: string;
  @Prop() value: any;
  @Prop() variant: string = 'primary';
  @Prop() size: string = 'default';
  @Prop() disabled: boolean = false;
  @Prop() active: boolean = false;
  @Prop() theme: string = 'default';

  @State() isDisabled: boolean = false;
  @State() isActive: boolean = false;
  @State() classList: string = '';

  @Event({
    eventName: 'buttonClickEvent',
    bubbles: true,
  })
  buttonClickEventEmitter: EventEmitter;

  @Watch('theme') themePropWatcher(newVal: string, oldVal: string) {
    if (newVal != oldVal) {
      this.theme = newVal;
      this.generateClassList();
    }
  }

  @Watch('active') activePropWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.isActive = newVal;
      this.generateClassList();
    }
  }

  @Watch('disabled') disabledPropWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.isDisabled = newVal;
      this.generateClassList();
    }
  }

  handleButtonClick(e) {
    e.preventDefault();
    this.buttonClickEventEmitter.emit({
      action: this.action,
      value: this.value,
    });
  }

  componentWillLoad() {
    this.isDisabled = this.disabled;
    this.isActive = this.active;
    this.generateClassList();
  }

  generateClassList() {
    this.classList = `button__${this.variant}--${this.theme} button__size--${this.size}`;
    if (this.isActive) {
      this.classList = `${this.classList} button__status--active`;
    }
    if (this.isDisabled) {
      this.classList = `${this.classList} button__status--disabled`;
    }
  }

  render() {
    return (
      <button
        class={this.classList}
        onClick={e => this.handleButtonClick(e)}
        disabled={this.isDisabled || this.isActive}
        ref={el => (this.buttonEl = el as HTMLButtonElement)}
      >
        {this.isActive ? <e-spinner></e-spinner> : <slot />}
      </button>
    );
  }
}
