import { newSpecPage } from '@stencil/core/testing';
import { ESpinner } from '../e-spinner';

describe('e-spinner', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [ESpinner],
      html: `<e-spinner></e-spinner>`,
    });
    expect(page.root).toEqualHtml(`
      <e-spinner>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-spinner>
    `);
  });
});
