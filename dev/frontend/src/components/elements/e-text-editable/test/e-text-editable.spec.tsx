import { newSpecPage } from '@stencil/core/testing';
import { ETextEditable } from '../e-text-editable';

describe('e-text-editable', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [ETextEditable],
      html: `<e-text-editable></e-text-editable>`,
    });
    expect(page.root).toEqualHtml(`
      <e-text-editable>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-text-editable>
    `);
  });
});
