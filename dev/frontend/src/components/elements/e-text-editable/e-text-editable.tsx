import {
  Component,
  Event,
  EventEmitter,
  FunctionalComponent,
  Prop,
  State,
  Listen,
  Watch,
  h,
} from '@stencil/core';
import {
  GenerateUpdateByAttributePayload,
  ValidateUpdateByAttributePayload,
} from '../../../global/script/helpers';
import { UpdateByAttributePayloadInterface } from '../../../global/script/interfaces';

@Component({
  tag: 'e-text-editable',
  styleUrl: 'e-text-editable.css',
  shadow: true,
})
export class ETextEditable {
  @Event({
    eventName: 'editableTextEvent',
    bubbles: true,
  })
  editableTextEventEmitter: EventEmitter;

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'startEditMode') {
      this.isEditModeOn = true;
    } else if (e.detail.action === 'cancelEditMode') {
      this.isEditModeOn = false;
      this.isSaveButtonDisabled = true;
      this.isSaveButtonActive = false;
    } else if (e.detail.action === 'saveEdit') {
      if (this.bypass) {
        this.isSaveButtonActive = true;
        return this.editableTextEventEmitter.emit({
          attribute: this.attribute,
          value: this.newValue,
        });
      }
      this.handleSaveEdit();
    }
  }

  @Listen('inputEvent') handleInputEvent(e) {
    if (e.detail.name === 'newValue') {
      this.newValue = e.detail.value;
    } else if (e.detail.name === 'newValueRepeat') {
      this.newValueRepeat = e.detail.value;
    }
    this.setSaveButtonState();
  }

  @Prop() type: string;
  @Prop() entity: string;
  @Prop() attribute: string;
  @Prop() value: string;
  @Prop() label: string;
  @Prop() active: boolean = false;
  @Prop() bypass: boolean = false;

  @State() val: string;
  @State() isEditModeOn: boolean = false;
  @State() isSaveButtonDisabled: boolean = true;
  @State() isSaveButtonActive: boolean = false;

  componentWillLoad() {
    this.val = this.value;
  }

  @Watch('active') activePropWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      if (!newVal) {
        this.isSaveButtonActive = false;
        this.isSaveButtonDisabled = false;
        this.isEditModeOn = false;
      }
    }
  }

  @Watch('value') valuePropWatcher(newVal: string, oldVal: string) {
    if (newVal != oldVal) {
      this.value = newVal;
      this.val = this.value;
    }
  }

  setSaveButtonState() {
    if (!this.newValue) {
      this.isSaveButtonDisabled = true;
      return;
    }
    if (this.attribute === 'password') {
      if (this.newValue != this.newValueRepeat) {
        this.isSaveButtonDisabled = true;
      } else {
        this.isSaveButtonDisabled = false;
      }
    } else {
      if (this.newValue != this.value) {
        this.isSaveButtonDisabled = false;
      } else {
        this.isSaveButtonDisabled = true;
      }
    }
  }

  async handleSaveEdit() {
    this.isSaveButtonActive = true;
    let updatePayload: UpdateByAttributePayloadInterface = GenerateUpdateByAttributePayload(
      this.attribute,
      this.newValue,
    );

    let { isValid, validationMessage } = ValidateUpdateByAttributePayload(updatePayload);
    if (!isValid) {
      this.isSaveButtonActive = false;
      this.isSaveButtonDisabled = false;
      return alert(validationMessage);
    }

    this.editableTextEventEmitter.emit({
      entity: this.entity,
      payload: updatePayload,
    });
  }

  private newValue: string;
  private newValueRepeat: string;

  EditModeForPassword: FunctionalComponent = () => [
    <e-input type="password" name="newValue" placeholder="New password"></e-input>,
    <l-spacer value={0.5}></l-spacer>,
    <e-input type="password" name="newValueRepeat" placeholder="Re-type new password"></e-input>,
  ];

  EditModeOn: FunctionalComponent = () => (
    <div class="edit-container edit-container--on">
      <e-text variant="footnote">{this.label.toUpperCase()}</e-text>
      <l-spacer value={0.5}></l-spacer>
      {this.attribute === 'password' ? (
        <this.EditModeForPassword></this.EditModeForPassword>
      ) : (
        <e-input type="text" name="newValue" value={this.val}></e-input>
      )}
      <l-spacer value={1}></l-spacer>
      <l-row direction="row-reverse">
        <div></div>
        <l-row>
          <e-button variant="light" action="cancelEditMode">
            Cancel
          </e-button>
          &nbsp;&nbsp;
          <e-button
            disabled={this.isSaveButtonDisabled}
            action="saveEdit"
            active={this.isSaveButtonActive}
          >
            Save
          </e-button>
        </l-row>
      </l-row>
    </div>
  );

  EditModeOff: FunctionalComponent = () => (
    <div class="edit-container edit-container--off" onClick={() => (this.isEditModeOn = true)}>
      <e-text variant="footnote">{this.label.toUpperCase()}</e-text>
      <l-row justifyContent="space-between">
        {this.type === 'link' ? (
          <e-link variant="email" url={`mailto:${this.val}`}>
            {this.val}
          </e-link>
        ) : (
          <e-text>{this.val}</e-text>
        )}
      </l-row>
    </div>
  );

  render() {
    if (this.isEditModeOn) {
      return <this.EditModeOn></this.EditModeOn>;
    } else {
      return <this.EditModeOff></this.EditModeOff>;
    }
  }
}
