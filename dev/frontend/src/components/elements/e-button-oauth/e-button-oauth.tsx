import { Component, Event, EventEmitter, FunctionalComponent, Prop, h } from '@stencil/core';
import { Var, FrontendLogger } from '../../../global/script/var';
import { Store } from '../../../global/script/store';

@Component({
  tag: 'e-button-oauth',
  styleUrl: 'e-button-oauth.css',
  shadow: true,
})
export class EButtonOauth {
  @Event({
    eventName: 'routeToEvent',
    bubbles: true,
  })
  routeToEvent: EventEmitter;

  @Prop() variant: string = 'google';

  googleOauthButtonEl!: HTMLDivElement;
  private window: any;

  componentDidLoad() {
    this.window = window;
    if (this.variant === 'google') {
      this.initGoogleOauth();
    }
  }

  loadScript(src) {
    return new Promise(resolve => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  }

  /**
   * Initializes Google OAuth integration
   * Loads the Google Sign-In client and sets up the OAuth button
   */
  async initGoogleOauth() {
    // DEBUG: Log Google OAuth initialization
    FrontendLogger.debug('Initializing Google OAuth', {
      variant: this.variant,
      clientId: Var.keys.oauth.google.clientId ? 'present' : 'missing',
    });

    const isScriptLoaded = await this.loadScript('https://accounts.google.com/gsi/client');
    if (!isScriptLoaded) {
      // DEBUG: Log script loading failure
      FrontendLogger.error('Failed to load Google Sign-In client script', {
        scriptUrl: 'https://accounts.google.com/gsi/client',
      });
      return;
    }

    // DEBUG: Log Google OAuth client initialization
    FrontendLogger.debug('Google Sign-In client script loaded successfully, initializing OAuth');

    await this.window.google.accounts.id.initialize({
      client_id: Var.keys.oauth.google.clientId,
      callback: response => {
        // DEBUG: Log OAuth callback (without sensitive data)
        FrontendLogger.debug('Google OAuth callback received', {
          hasCredential: !!response.credential,
          credentialLength: response.credential?.length || 0,
        });

        Store.oauthToken = response.credential;
        this.routeToEvent.emit({
          route: '/auth/callback/google',
        });
      },
    });

    // DEBUG: Log button rendering
    FrontendLogger.debug('Rendering Google OAuth button');

    await this.window.google.accounts.id.renderButton(this.googleOauthButtonEl, {
      type: 'standard',
      theme: 'outline',
      size: 'large',
      width: 250,
    });

    FrontendLogger.debug('Google OAuth initialization completed successfully');
  }

  GoogleOauthButton: FunctionalComponent = () => (
    <div
      id="button__oauth--google"
      ref={el => (this.googleOauthButtonEl = el as HTMLDivElement)}
    ></div>
  );

  render() {
    if (this.variant === 'google') {
      return <this.GoogleOauthButton></this.GoogleOauthButton>;
    }
  }
}
