import { newSpecPage } from '@stencil/core/testing';
import { EButtonOauth } from '../e-button-oauth';

describe('e-button-oauth', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [EButtonOauth],
      html: `<e-button-oauth></e-button-oauth>`,
    });
    expect(page.root).toEqualHtml(`
      <e-button-oauth>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-button-oauth>
    `);
  });
});
