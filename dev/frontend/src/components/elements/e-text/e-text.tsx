import { Component, Prop, h } from '@stencil/core';

@Component({
  tag: 'e-text',
  styleUrl: 'e-text.css',
  shadow: true,
})
export class EText {
  @Prop() variant: string = 'body';
  @Prop() theme: string = 'default';
  @Prop() weight: string = 'regular';

  private classList: string;

  componentWillLoad() {
    this.generateClassList();
  }

  generateClassList() {
    this.classList = `${this.variant} ${this.theme} ${this.weight}`;
  }

  render() {
    if (this.variant === 'display') {
      return (
        <h1 class={this.classList}>
          <slot></slot>
        </h1>
      );
    } else if (this.variant === 'heading') {
      return (
        <h2 class={this.classList}>
          <slot />
        </h2>
      );
    } else if (this.variant === 'subHeading') {
      return (
        <h3 class={this.classList}>
          <slot />
        </h3>
      );
    } else {
      return (
        <p class={this.classList}>
          <slot />
        </p>
      );
    }
  }
}
