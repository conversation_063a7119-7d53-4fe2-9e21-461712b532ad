import { Component, Prop, State, Watch, h } from "@stencil/core";
import { Router } from "../../..";

@Component({
  tag: "e-link",
  styleUrl: "e-link.css",
  shadow: true,
})
export class ELink {
  @Prop() variant: string = "default";
  @Prop() theme: string = "default";
  @Prop() url: string = "";
  @Prop() active: boolean = false;

  @State() isActive: boolean = false;

  @Watch("active") activePropWatcher(newValue: string, oldValue: string) {
    if (newValue != oldValue) {
      this.generateClassList();
    }
  }

  private classList: string = "";

  componentWillLoad() {
    this.generateClassList();
  }

  generateClassList() {
    if (this.active) {
      this.classList = `link__${this.variant}--${this.theme}-active`;
    } else {
      this.classList = `link__${this.variant}--${this.theme}`;
    }
  }

  render() {
    if (this.variant === "email") {
      return (
        <a class={this.classList} href={this.url} target="_self">
          <slot />{" "}
        </a>
      );
    } else if (this.variant === "externalLink") {
      return (
        <l-row justifyContent="flex-start">
          <a class={this.classList} href={this.url} target="_blank">
            <slot />{" "}
          </a>
          &nbsp;
          <e-image
            src="../../../assets/icon/blue/open-in-new-blue.svg"
            width="1em"
          ></e-image>
        </l-row>
      );
    } else {
      return (
        <a class={this.classList} onClick={() => Router.push(this.url)}>
          <slot />
        </a>
      );
    }
  }
}
