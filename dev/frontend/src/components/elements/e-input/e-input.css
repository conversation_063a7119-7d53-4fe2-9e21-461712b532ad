input[type="email"],
input[type="number"],
input[type="password"],
input[type="text"] {
  width: 100%;
  padding: calc(var(--padding) / 1.5) var(--padding);
  border: var(--border__input);
  border-radius: var(--border-radius);
  font-size: 1em;
  box-sizing: border-box;
  height: 50px;
}

input[type="email"]:focus,
input[type="number"]:focus,
input[type="password"]:focus,
input[type="text"]:focus {
  outline: none;
  border-color: var(--color__grey--800);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 0.5em;
  padding: calc(var(--padding) * 0.75) var(--padding);
  border: var(--border__input);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  position: relative;
  padding-left: calc(
    var(--padding) * 2.5
  ); /* Increased padding to accommodate the gap */
}

.radio-label:hover {
  cursor: pointer;
  color: var(--color__grey--800);
  border: 1px solid var(--color__grey--800);
}

.radio-label::before {
  content: "";
  position: absolute;
  left: var(--padding);
  top: 50%;
  transform: translateY(-50%);
  width: 14px; /* Smaller circle */
  height: 14px; /* Smaller circle */
  border: 1px solid var(--color__grey--500);
  border-radius: 50%;
  background-color: white;
  transition: all 0.25s ease-in-out;
}

.radio-label::after {
  content: "";
  position: absolute;
  /* Center the dot within the circle both horizontally and vertically */
  left: calc(
    var(--padding) + 7px - 2px
  ); /* Calculate center of outer circle then offset by half inner circle width */
  top: 50%;
  transform: translateY(-50%);
  width: 6px; /* Smaller inner circle */
  height: 6px; /* Smaller inner circle */
  border-radius: 50%;
  background-color: var(--color__grey--800);
  opacity: 0;
  transition: all 0.25s ease-in-out;
}

/* Add a gap between the circle and the content */
.radio-label slot,
.radio-label ::slotted(*) {
  margin-left: 1em;
}

.radio-input {
  display: none;
}

.radio-input:checked + .radio-label::after {
  opacity: 1;
}

.radio-input:checked + .radio-label {
  color: var(--color__grey--800);
  border: 1px solid var(--color__grey--800);
  background: var(--color__grey--100);
}

.radio-input:checked + .radio-label::before {
  border-color: var(--color__grey--800);
}

/* WebKit, Blink, Edge */
::-webkit-input-placeholder {
  color: var(--color__grey--400);
}

/* Mozilla Firefox 4 to 18 */
:-moz-placeholder {
  color: var(--color__grey--400);
  opacity: 1;
}

/* Mozilla Firefox 19+ */
::-moz-placeholder {
  color: var(--color__grey--400);
  opacity: 1;
}

/* Internet Explorer 10-11 */
:-ms-input-placeholder {
  color: var(--color__grey--400);
}

/* Microsoft Edge */
::-ms-input-placeholder {
  color: var(--color__grey--400);
}

/* Most modern browsers support this now. */
::placeholder {
  color: var(--color__grey--400);
}

.checkbox-container {
  display: flex;
  gap: 1em;
  font-family: sans-serif;
}

.input-label__container {
  position: relative;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-label {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  padding-left: 2.75rem; /* Increased left padding for more space */
  border: var(--border__input);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.25s;
  position: relative;
}

.checkbox-label:hover {
  color: var(--color__grey--800);
  border-color: var(--color__grey--800);
}

.checkbox-label:before {
  content: "";
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.8em; /* Square for checkbox */
  height: 0.8em;
  border: 1px solid var(--color__grey--400);
  border-radius: 2px; /* Slightly rounded corners for checkbox */
  transition: all 0.25s;
}

/* Add hover state for the inner checkbox */
.checkbox-label:hover:before {
  border-color: var(--color__grey--800);
}

.checkbox-input:checked + .checkbox-label {
  border-color: var(--color__grey--800);
  background-color: var(--color__grey--100);
  color: var(--color__grey--800);
}

.checkbox-input:checked + .checkbox-label:before {
  background-color: var(--color__grey--800);
  border-color: var(--color__grey--800);
}

/* Better centered checkmark for checked state */
.checkbox-input:checked + .checkbox-label:after {
  content: "";
  position: absolute;
  left: calc(1rem + 0.27em); /* Adjusted for better centering */
  top: 50%;
  transform: translateY(-50%) rotate(45deg); /* Adjusted transform for centering */
  width: 0.25em;
  height: 0.4em; /* Slightly adjusted height */
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  margin-top: -0.1em; /* Slight adjustment to account for the rotation */
}
