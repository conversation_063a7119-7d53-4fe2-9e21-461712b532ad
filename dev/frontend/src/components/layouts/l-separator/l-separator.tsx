import { Component, Prop, h } from '@stencil/core';

@Component({
  tag: 'l-separator',
  styleUrl: 'l-separator.css',
  shadow: true,
})
export class LSeparator {
  @Prop() variant: string = 'default';

  render() {
    if (this.variant === 'default') {
      return <div class="separator--default"></div>;
    } else if (this.variant === 'oauth') {
      return (
        <div id="separator__oauth">
          <div class="separator__oauth--lines"></div>
          <c-text>OR</c-text>
          <div class="separator__oauth--lines"></div>
        </div>
      );
    }
  }
}
