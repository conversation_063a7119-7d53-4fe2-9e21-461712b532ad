import { Component, Prop, Host, h } from '@stencil/core';
import { LooseObjectInterface } from '../../../global/script/interfaces';

@Component({
  tag: 'l-row',
  styleUrl: 'l-row.css',
  shadow: true,
})
export class LRow {
  @Prop() variant: string = '';
  @Prop() justifyContent: string = 'space-between';
  @Prop() align: string = 'center';
  @Prop() direction: string = 'row';

  private classMap: LooseObjectInterface = {};

  componentWillLoad() {
    this.generateClassMap();
  }

  generateClassMap() {
    this.classMap.display = 'flex';
    this.classMap.alignItems = this.align;
    this.classMap.justifyContent = this.justifyContent;
    this.classMap.flexDirection = this.direction;
  }

  render() {
    return (
      <Host style={this.classMap}>
        <slot></slot>
      </Host>
    );
  }
}
