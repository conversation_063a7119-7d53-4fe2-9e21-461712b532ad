:host {
  display: block;
}

/* Default */
:host(.banner) {
  display: block;
  padding: var(--padding);
  border-radius: var(--border-radius);
  box-sizing: border-box;
}

/* Position */
:host(.banner__position--bottom) {
  width: 100%;
  left: 0;
  bottom: 0;
  position: fixed;
  -webkit-box-shadow: 0px -2px 33px 3px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0px -2px 33px 3px rgba(0, 0, 0, 0.15);
  box-shadow: 0px -2px 33px 3px rgba(0, 0, 0, 0.15);
}

/* Theme */
:host(.banner__theme--danger) {
  background: var(--color__red--50);
  color: var(--color__red--900);
  border: 1px solid var(--color__red--100);
}

:host(.banner__theme--warning) {
  background: var(--color__orange--50);
  color: var(--color__orange--900);
  border: 1px solid var(--color__orange--200);
}

:host(.banner__theme--success) {
  background: var(--color__green--50);
  color: var(--color__green--800);
  border: 1px solid var(--color__green--200);
}
