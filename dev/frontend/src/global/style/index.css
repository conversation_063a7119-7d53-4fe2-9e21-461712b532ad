@import "reset.css";
@import "var--light.css";
/* @import 'var-dark.css'; */

@font-face {
  font-family: "Work Sans";
  /* src: url('/WorkSans-Regular.woff2'); */
  src: url("../../assets/typefaces/WorkSans-Regular.woff2");
  font-display: swap;
}

@font-face {
  font-family: "Space Grotesk";
  src: url("../../assets/typefaces/SpaceGrotesk.woff2");
  font-display: swap;
}

body {
  max-width: 1024px;
  margin: 0 auto;
  background: var(--color__bg);
  background: white;
  font-size: var(--font-size__body);
  color: var(--color__grey--700);
  font-family: var(--font-family__sans-serif);
  line-height: var(--line-height);
}

@media only screen and (max-width: 768px) {
  ::-webkit-scrollbar {
    -webkit-appearance: none;
  }
}
