import <PERSON><PERSON> from 'joi';
import { MailPayloadInterface } from '../../interfaces';

/**
 * Joi validation schema for mail payload validation
 *
 * This schema defines the validation rules for email payloads sent to the mail API.
 * It ensures data integrity and security before sending requests to the server.
 *
 * Validation Rules:
 * - Email: Must be valid email format, 5-128 characters, automatically trimmed and lowercased
 * - Variant: Must be a non-empty string, automatically trimmed
 *
 * Security Features:
 * - TLD validation disabled for flexibility with internal/test domains
 * - Length limits prevent potential DoS attacks
 * - Automatic normalization (trim, lowercase) for consistency
 */
const ValidateMailPayloadSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } }) // Allow any TLD for flexibility
    .min(5) // Minimum reasonable email length
    .max(128) // Prevent excessively long emails
    .lowercase() // Normalize to lowercase
    .trim() // Remove whitespace
    .required(), // Email is mandatory
  variant: Joi.string()
    .trim() // Remove whitespace
    .required(), // Variant is mandatory
});

/**
 * Validates a mail payload against the defined schema
 *
 * This function performs comprehensive validation of mail payloads before
 * they are sent to the server. It ensures data integrity and provides
 * user-friendly error messages for validation failures.
 *
 * Validation Process:
 * 1. Checks email format and length constraints
 * 2. Validates variant field presence and format
 * 3. Returns detailed error messages for user feedback
 *
 * @param payload - The mail payload to validate
 * @returns Validation result with success status and error message
 *
 * @example
 * // Valid payload
 * const result = ValidateMailPayload({
 *   email: '<EMAIL>',
 *   variant: 'emailVerificationCode'
 * });
 * // Returns: { isValid: true, validationMessage: '' }
 *
 * @example
 * // Invalid payload
 * const result = ValidateMailPayload({
 *   email: 'invalid-email',
 *   variant: ''
 * });
 * // Returns: { isValid: false, validationMessage: '❌ "email" must be a valid email' }
 */
export const ValidateMailPayload = (payload: MailPayloadInterface) => {
  // Perform Joi validation against the schema
  const { error } = ValidateMailPayloadSchema.validate(payload);

  if (error) {
    // Return user-friendly error message with emoji for visual clarity
    return {
      isValid: false,
      validationMessage: `❌ ${error.details[0].message}`,
    };
  } else {
    // Validation successful
    return {
      isValid: true,
      validationMessage: '',
    };
  }
};
