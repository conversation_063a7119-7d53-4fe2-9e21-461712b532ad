import { Var, FrontendLogger } from '../../var';

/**
 * Constructs a complete API URL from an endpoint path
 *
 * This function combines the base API URL, version, and endpoint path
 * to create a fully qualified URL for API requests.
 *
 * @param endpoint - The API endpoint path (e.g., '/auth/login')
 * @returns Complete API URL (e.g., 'http://localhost:4444/v1/auth/login')
 *
 * @example
 * const url = ConstructApiUrl('/auth/login');
 * // Returns: 'http://localhost:4444/v1/auth/login' (in dev)
 * // Returns: 'https://api.sensefolks.com/v1/auth/login' (in prod)
 */
export const ConstructApiUrl = (endpoint: string): string => {
  const baseUrl = Var.api.url;
  const version = Var.api.version;

  // Ensure endpoint starts with a forward slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  // Construct the complete URL
  const fullUrl = `${baseUrl}/${version}${formattedEndpoint}`;

  // DEBUG: Log URL construction details
  FrontendLogger.debug('API URL constructed', {
    endpoint,
    formattedEndpoint,
    baseUrl,
    version,
    fullUrl,
  });

  return fullUrl;
};
