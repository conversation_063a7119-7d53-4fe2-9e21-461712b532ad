import { <PERSON>pi<PERSON>rapper } from './ApiWrapper';
import { FrontendLogger } from '../../var';

/**
 * Generic API helper for updating data by attribute
 *
 * This function provides a standardized way to update data through PUT requests.
 * It includes CSRF token handling and comprehensive error management.
 *
 * @param endpoint - The API endpoint to send the update request to
 * @param payload - The data payload to send in the request body
 * @returns Promise resolving to standardized API response
 *
 * @example
 * const result = await UpdateByAttributeApi('/surveys/123', {
 *   attribute: 'title',
 *   value: 'New Survey Title'
 * });
 */
export const UpdateByAttributeApi = async (endpoint: string, payload: any) => {
  try {
    // DEBUG: Log update API call (without sensitive data)
    FrontendLogger.debug('Update by attribute API call initiated', {
      endpoint,
      hasPayload: !!payload,
      payloadKeys: payload ? Object.keys(payload) : [],
    });

    const result = await ApiWrapper(endpoint, {
      method: 'PUT',
      body: payload,
      includeCsrf: true, // Include CSRF token for data modification requests
    });

    // DEBUG: Log update API response
    FrontendLogger.debug('Update by attribute API call completed', {
      endpoint,
      success: result.success,
      hasMessage: !!result.message,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    // DEBUG: Log update API error
    FrontendLogger.error('Error in UpdateByAttributeApi', {
      endpoint,
      error: errorMessage,
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Failed to update data',
      payload: null,
    };
  }
};
