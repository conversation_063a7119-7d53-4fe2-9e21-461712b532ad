import { Var, FrontendLogger } from '../../var';
import { Store } from '../../store';
import { ApiWrapper } from './ApiWrapper';

/**
 * Fetches a CSRF token from the server
 *
 * This function requests a new CSRF token from the server and stores it
 * in the global store for use in subsequent API requests. The token can
 * be received either in response headers or response body.
 *
 * @returns Promise resolving to operation result with success status and message
 *
 * @example
 * const result = await GetCsrfTokenApi();
 * if (result.success) {
 *   console.log('CSRF token fetched successfully');
 * }
 */
export const GetCsrfTokenApi = async (): Promise<{ success: boolean; message: string }> => {
  try {
    // DEBUG: Log CSRF token fetch attempt
    FrontendLogger.debug('Fetching CSRF token from server', {
      endpoint: Var.api.endpoint.csrf,
      currentToken: Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
    });

    const result = await ApiWrapper(Var.api.endpoint.csrf, {
      method: 'GET',
      includeCsrf: false, // Don't include CSRF token when fetching it
    });

    // DEBUG: Log API response details
    FrontendLogger.debug('CSRF token API response received', {
      success: result.success,
      hasPayload: !!result.payload,
      hasTokenInPayload: !!result.payload?.token,
      currentStoreToken: Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
    });

    // Fallback: check response payload for token (backward compatibility)
    if (result.success && result.payload?.token && !Store.csrfToken) {
      Store.csrfToken = result.payload.token;
      FrontendLogger.debug('CSRF token fetched from response body', {
        tokenPreview: result.payload.token.substring(0, 10) + '...',
        tokenLength: result.payload.token.length,
      });
    }

    // Check if we have a token (from either ApiWrapper headers or response payload)
    if (Store.csrfToken) {
      FrontendLogger.debug('CSRF token successfully obtained and stored', {
        tokenPreview: Store.csrfToken.substring(0, 10) + '...',
        tokenLength: Store.csrfToken.length,
        source: result.payload?.token ? 'response body' : 'response headers',
      });

      return {
        success: true,
        message: 'CSRF token fetched successfully',
      };
    }

    // No token found in either location
    const errorMessage = result.message || 'No CSRF token found in response headers or body';
    FrontendLogger.error('Failed to fetch CSRF token', {
      errorMessage,
      apiSuccess: result.success,
      hasPayload: !!result.payload,
      responseMessage: result.message,
    });

    return {
      success: false,
      message: errorMessage,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    FrontendLogger.error('Error fetching CSRF token', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Failed to fetch CSRF token',
    };
  }
};
