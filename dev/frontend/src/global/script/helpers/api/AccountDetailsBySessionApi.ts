import { Var } from '../../var';
import { ApiWrapper } from './ApiWrapper';

export const AccountDetailsBySessionApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.details, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error fetching account details:', error);
    return {
      success: false,
      message: 'Error fetching account details',
      payload: null,
    };
  }
};
