import { Store } from '../../store';
import { ConstructApiUrl } from './ConstructApiUrl';
import { CsrfValidator } from '../security/CsrfValidator';
import { RateLimitHandler } from './RateLimitHandler';
import { FrontendLogger } from '../../var';

/**
 * Extract rate limiting information from HTTP response headers
 * Used for implementing client-side rate limiting and retry logic
 *
 * @param response - The HTTP response object
 * @returns Object containing rate limit information
 */
function extractRateLimitInfo(response: Response) {
  const limit = response.headers.get('RateLimit-Limit');
  const remaining = response.headers.get('RateLimit-Remaining');
  const reset = response.headers.get('RateLimit-Reset');
  const retryAfter = response.headers.get('Retry-After');

  const rateLimitInfo = {
    limit: limit ? parseInt(limit, 10) : undefined,
    remaining: remaining ? parseInt(remaining, 10) : undefined,
    resetTime: reset ? parseInt(reset, 10) : undefined,
    retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined,
  };

  // DEBUG: Log rate limit information in development
  FrontendLogger.debug('Rate limit info extracted', {
    headers: { limit, remaining, reset, retryAfter },
    parsed: rateLimitInfo,
  });

  return rateLimitInfo;
}

/**
 * Utility function to pause execution for a specified duration
 * Used for implementing retry delays and rate limiting
 *
 * @param ms - Number of milliseconds to wait
 * @returns Promise that resolves after the specified delay
 */
function sleep(ms: number): Promise<void> {
  FrontendLogger.debug(`Sleeping for ${ms}ms`);
  return new Promise(resolve => setTimeout(resolve, ms));
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload?: any;
  rateLimitInfo?: {
    isRateLimited: boolean;
    retryAfter?: number;
    limit?: number;
    remaining?: number;
    resetTime?: number;
  };
}

interface ApiOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  includeCsrf?: boolean;
  retryOnRateLimit?: boolean;
  retryOnCsrfError?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Main API wrapper function with comprehensive error handling and retry logic
 *
 * Features:
 * - Automatic CSRF token management
 * - Rate limiting detection and handling
 * - Retry logic for transient failures
 * - Environment-aware debug logging
 * - Comprehensive error handling
 *
 * @param endpoint - API endpoint path (e.g., '/auth/login')
 * @param options - Request configuration options
 * @returns Promise resolving to standardized API response
 */
export const ApiWrapper = async (
  endpoint: string,
  options: ApiOptions = { method: 'GET' },
): Promise<ApiResponse> => {
  const maxRetries = options.maxRetries ?? 2;
  const retryDelay = options.retryDelay ?? 1000;
  const shouldRetryOnRateLimit = options.retryOnRateLimit ?? false;
  const shouldRetryOnCsrfError = options.retryOnCsrfError ?? true;

  // DEBUG: Log API request initiation
  FrontendLogger.debug('API request initiated', {
    endpoint,
    method: options.method,
    maxRetries,
    retryDelay,
    shouldRetryOnRateLimit,
    shouldRetryOnCsrfError,
    hasBody: !!options.body,
    includeCsrf: options.includeCsrf,
  });

  // Check if endpoint is currently rate limited
  if (RateLimitHandler.isEndpointRateLimited(endpoint)) {
    const timeRemaining = RateLimitHandler.getTimeUntilReset(endpoint);
    const message = RateLimitHandler.getRateLimitMessage(endpoint);

    // DEBUG: Log rate limit detection
    FrontendLogger.warn('Endpoint is rate limited', {
      endpoint,
      timeRemaining,
      message,
    });

    return {
      success: false,
      message,
      payload: null,
      rateLimitInfo: {
        isRateLimited: true,
        retryAfter: timeRemaining,
      },
    };
  }

  // Main retry loop with comprehensive error handling
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    // DEBUG: Log retry attempt
    FrontendLogger.debug('API request attempt', {
      endpoint,
      attempt: attempt + 1,
      maxAttempts: maxRetries + 1,
    });

    const result = await performApiRequest(endpoint, options);

    // Handle rate limiting information
    if (result.rateLimitInfo) {
      if (result.rateLimitInfo.isRateLimited) {
        // Record rate limit for future requests
        RateLimitHandler.recordRateLimit(endpoint, result.rateLimitInfo);
        FrontendLogger.warn('Rate limit detected and recorded', {
          endpoint,
          rateLimitInfo: result.rateLimitInfo,
        });
      } else if (RateLimitHandler.shouldWarnAboutRateLimit(result.rateLimitInfo)) {
        // Warn about approaching rate limit
        const warning = RateLimitHandler.getRateLimitWarning(result.rateLimitInfo);
        FrontendLogger.warn('Approaching rate limit', {
          endpoint,
          warning,
          rateLimitInfo: result.rateLimitInfo,
        });
      }
    }

    // Handle rate limit retry logic
    if (result.rateLimitInfo?.isRateLimited && shouldRetryOnRateLimit && attempt < maxRetries) {
      const waitTime = result.rateLimitInfo.retryAfter
        ? result.rateLimitInfo.retryAfter * 1000
        : RateLimitHandler.calculateRetryDelay(attempt, retryDelay);

      // DEBUG: Log rate limit retry
      FrontendLogger.warn('Rate limited, retrying after delay', {
        endpoint,
        waitTime,
        attempt: attempt + 1,
        maxAttempts: maxRetries + 1,
        retryAfter: result.rateLimitInfo.retryAfter,
      });

      await sleep(waitTime);
      continue;
    }

    // Handle CSRF error retry logic
    if (
      !result.success &&
      result.message?.includes('Invalid security token') &&
      shouldRetryOnCsrfError &&
      attempt < maxRetries &&
      !endpoint.includes('/auth/logout')
    ) {
      // DEBUG: Log CSRF error retry attempt
      FrontendLogger.warn('CSRF error detected, attempting token refresh and retry', {
        endpoint,
        attempt: attempt + 1,
        maxAttempts: maxRetries + 1,
        message: result.message,
      });

      try {
        const { GetCsrfTokenApi } = await import('./GetCsrfTokenApi');
        const refreshResult = await GetCsrfTokenApi();

        if (refreshResult.success) {
          FrontendLogger.debug('CSRF token refreshed successfully, retrying request', {
            endpoint,
            attempt: attempt + 1,
          });
          await sleep(500);
          continue;
        } else {
          FrontendLogger.warn('Failed to refresh CSRF token, not retrying', {
            endpoint,
            refreshError: refreshResult.message,
          });
        }
      } catch (refreshError) {
        FrontendLogger.error('Error refreshing CSRF token for retry', {
          endpoint,
          error: refreshError instanceof Error ? refreshError.message : String(refreshError),
        });
      }
    }

    // DEBUG: Log successful API response
    FrontendLogger.debug('API request completed', {
      endpoint,
      success: result.success,
      attempt: attempt + 1,
      statusCode: result.payload?.statusCode,
      hasPayload: !!result.payload,
    });

    return result;
  }

  // This should never be reached, but included for safety
  const error = new Error('Unexpected error in ApiWrapper retry logic');
  FrontendLogger.error('Unexpected error in ApiWrapper retry logic', {
    endpoint,
    maxRetries,
    error: error.message,
  });
  throw error;
};

/**
 * Perform the actual HTTP request with comprehensive error handling
 *
 * @param endpoint - API endpoint path
 * @param options - Request configuration options
 * @returns Promise resolving to standardized API response
 */
async function performApiRequest(endpoint: string, options: ApiOptions): Promise<ApiResponse> {
  const url = ConstructApiUrl(endpoint);

  // DEBUG: Log request details
  FrontendLogger.debug('Performing HTTP request', {
    endpoint,
    url,
    method: options.method,
    hasBody: !!options.body,
    credentials: options.credentials,
  });

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Handle CSRF token inclusion
  if (options.includeCsrf !== false && (options.method !== 'GET' || options.includeCsrf === true)) {
    const tokenInfo = CsrfValidator.getTokenInfo();

    if (!CsrfValidator.isTokenValid()) {
      FrontendLogger.warn('CSRF token is invalid when creating request headers', {
        endpoint,
        tokenInfo,
        currentToken: Store.csrfToken?.substring(0, 10) + '...',
      });
    } else {
      FrontendLogger.debug('Including valid CSRF token in request', {
        endpoint,
        tokenInfo,
      });
    }

    defaultHeaders['X-CSRF-Token'] = Store.csrfToken || '';
  }

  const headers = { ...defaultHeaders, ...options.headers };

  const fetchOptions: RequestInit = {
    method: options.method,
    headers,
    credentials: options.credentials || 'include',
  };

  // Add request body for non-GET requests
  if (options.body && options.method !== 'GET') {
    fetchOptions.body =
      typeof options.body === 'string' ? options.body : JSON.stringify(options.body);

    // DEBUG: Log request body details (without sensitive data)
    FrontendLogger.debug('Request body prepared', {
      endpoint,
      bodyType: typeof options.body,
      bodySize: fetchOptions.body.length,
      isStringBody: typeof options.body === 'string',
    });
  }

  try {
    // DEBUG: Log request execution
    FrontendLogger.debug('Executing fetch request', {
      endpoint,
      url,
      method: options.method,
      headersCount: Object.keys(headers).length,
    });

    const response = await fetch(url, fetchOptions);

    // DEBUG: Log response received
    FrontendLogger.debug('Response received', {
      endpoint,
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: {
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length'),
        csrfToken: response.headers.get('X-CSRF-Token') ? 'present' : 'absent',
      },
    });

    // Always check for updated CSRF token in response headers
    const csrfToken = response.headers.get('X-CSRF-Token');
    if (csrfToken) {
      const oldToken = Store.csrfToken?.substring(0, 10) + '...';
      Store.csrfToken = csrfToken;
      FrontendLogger.debug('CSRF token updated from response header', {
        endpoint,
        oldToken,
        newToken: csrfToken.substring(0, 10) + '...',
      });
    }

    // Extract rate limiting information from headers
    const rateLimitInfo = extractRateLimitInfo(response);

    // Handle non-JSON responses
    let data: any;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      FrontendLogger.debug('Parsing JSON response', { endpoint, contentType });
      data = await response.json();
    } else {
      FrontendLogger.debug('Non-JSON response received', { endpoint, contentType });
      data = {
        success: response.ok,
        message: response.ok ? 'Request successful' : 'Request failed',
        payload: null,
      };
    }

    // Handle HTTP error status codes
    if (!response.ok) {
      FrontendLogger.error('API request failed', {
        endpoint,
        url,
        status: response.status,
        statusText: response.statusText,
        dataPreview: data ? JSON.stringify(data).substring(0, 200) + '...' : 'no data',
      });

      // Handle rate limiting (429 status)
      if (response.status === 429) {
        FrontendLogger.warn('Rate limit exceeded', {
          endpoint,
          url,
          rateLimitInfo,
          message: data?.message,
        });

        // Check for CSRF token in rate limit response
        if (data?.csrfToken) {
          Store.csrfToken = data.csrfToken;
          FrontendLogger.debug('CSRF token updated from rate limit response', {
            endpoint,
            newToken: data.csrfToken.substring(0, 10) + '...',
          });
        }

        return {
          success: false,
          message: data?.message || 'Rate limit exceeded. Please try again later.',
          payload: data?.payload || null,
          rateLimitInfo: {
            isRateLimited: true,
            ...rateLimitInfo,
          },
        };
      }

      // Handle CSRF token errors specifically
      if (response.status === 403 && data?.code === 'CSRF_TOKEN_INVALID') {
        FrontendLogger.warn('CSRF token invalid, clearing stored token and attempting refresh', {
          endpoint,
          code: data.code,
          currentToken: Store.csrfToken?.substring(0, 10) + '...',
        });
        CsrfValidator.clearToken();

        // Special handling for logout endpoint - CSRF errors during logout are expected
        if (endpoint.includes('/auth/logout')) {
          FrontendLogger.debug('CSRF error during logout is expected due to session destruction', {
            endpoint,
          });
        } else {
          // For other endpoints, try to refresh the CSRF token automatically
          FrontendLogger.debug('Attempting to refresh CSRF token after validation failure', {
            endpoint,
          });
          try {
            // Import GetCsrfTokenApi dynamically to avoid circular dependency
            const { GetCsrfTokenApi } = await import('./GetCsrfTokenApi');
            const refreshResult = await GetCsrfTokenApi();
            if (refreshResult.success) {
              FrontendLogger.debug('CSRF token refreshed successfully after validation failure', {
                endpoint,
                newToken: Store.csrfToken?.substring(0, 10) + '...',
              });
            } else {
              FrontendLogger.warn('Failed to refresh CSRF token after validation failure', {
                endpoint,
                error: refreshResult.message,
              });
            }
          } catch (refreshError) {
            FrontendLogger.error('Error refreshing CSRF token after validation failure', {
              endpoint,
              error: refreshError instanceof Error ? refreshError.message : String(refreshError),
            });
          }
        }
      }

      return {
        success: false,
        message: data?.message || `HTTP ${response.status}: ${response.statusText}`,
        payload: data?.payload || null,
        rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
      };
    }

    // DEBUG: Log successful response
    FrontendLogger.debug('API request successful', {
      endpoint,
      success: data?.success !== false,
      hasPayload: !!(data?.payload || data),
      rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
    });

    return {
      success: data?.success !== false,
      message: data?.message || 'Request successful',
      payload: data?.payload || data,
      rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
    };
  } catch (error) {
    // DEBUG: Log network/fetch errors
    FrontendLogger.error('API request error (network/fetch)', {
      endpoint,
      url,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Network error occurred',
      payload: null,
    };
  }
}
