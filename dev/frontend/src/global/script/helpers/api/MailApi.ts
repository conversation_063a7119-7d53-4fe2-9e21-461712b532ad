import { MailPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../var';
import { ApiWrapper } from './ApiWrapper';

export const MailApi = async (payload: MailPayloadInterface) => {
  let endpoint: string = '';
  if (payload.variant === 'emailVerificationCode') {
    endpoint = Var.api.endpoint.account.email.sendVerificationCode;
  } else if (payload.variant === 'passwordResetCode') {
    endpoint = Var.api.endpoint.account.password.sendResetCode;
  } else {
    return {
      success: false,
      message: 'Invalid mail variant',
      payload: null,
    };
  }

  try {
    const result = await ApiWrapper(endpoint, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log mail API error
    FrontendLogger.error('Error in MailApi', {
      variant: payload.variant,
      endpoint,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Error sending email',
      payload: null,
    };
  }
};
