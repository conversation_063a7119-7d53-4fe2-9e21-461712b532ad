import { Var, FrontendLogger } from '../../var';
import { ApiWrapper } from './ApiWrapper';
import { Store } from '../../store';

/**
 * Logs out the current user session
 *
 * This function sends a logout request to the server and handles the cleanup
 * of client-side session data. It gracefully handles CSRF token errors that
 * may occur during logout due to session destruction.
 *
 * @returns Promise resolving to logout operation result
 *
 * @example
 * const result = await LogoutApi();
 * if (result.success) {
 *   // Redirect to login page
 * }
 */
export const LogoutApi = async () => {
  try {
    // DEBUG: Log logout attempt
    FrontendLogger.debug('Initiating logout request', {
      endpoint: Var.api.endpoint.account.auth.logout,
      hasCurrentToken: !!Store.csrfToken,
      currentToken: Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
    });

    const result = await ApiWrapper(Var.api.endpoint.account.auth.logout, {
      method: 'POST',
    });

    // DEBUG: Log logout API response
    FrontendLogger.debug('Logout API response received', {
      success: result.success,
      message: result.message,
      hasPayload: !!result.payload,
    });

    // Clear CSRF token regardless of result since logout destroys session
    const oldToken = Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none';
    Store.csrfToken = '';
    FrontendLogger.debug('CSRF token cleared after logout attempt', {
      oldToken,
      reason: 'logout destroys session',
    });

    // If logout failed due to CSRF but we're already logged out, treat as success
    if (!result.success && result.message?.includes('Invalid security token')) {
      FrontendLogger.warn('Logout failed due to CSRF token, treating as success', {
        originalMessage: result.message,
        reason: 'session likely already invalid',
      });

      return {
        success: true,
        message: 'Logout successful (session was already invalid)',
        payload: { isSessionActive: false },
      };
    }

    // DEBUG: Log successful logout
    FrontendLogger.debug('Logout completed successfully', {
      success: result.success,
      message: result.message,
      hasPayload: !!result.payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log logout error
    FrontendLogger.error('Error during logout request', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    // Clear CSRF token even on error since we're logging out
    const oldToken = Store.csrfToken ? Store.csrfToken.substring(0, 10) + '...' : 'none';
    Store.csrfToken = '';
    FrontendLogger.debug('CSRF token cleared after logout error', {
      oldToken,
      reason: 'cleanup on logout error',
    });

    return {
      success: false,
      message: 'Error during logout request',
      payload: null,
    };
  }
};
