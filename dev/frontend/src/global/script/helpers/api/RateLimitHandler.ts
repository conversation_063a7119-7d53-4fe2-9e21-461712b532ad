import { FrontendLogger } from '../../var';

interface RateLimitInfo {
  isRateLimited: boolean;
  retryAfter?: number;
  limit?: number;
  remaining?: number;
  resetTime?: number;
}

interface RateLimitState {
  [endpoint: string]: {
    isLimited: boolean;
    retryAfter: number;
    lastLimitTime: number;
  };
}

/**
 * Rate Limit Handler for API requests
 *
 * This class manages client-side rate limiting to prevent excessive API calls
 * and handle server-side rate limit responses gracefully.
 *
 * Features:
 * - Tracks rate limit state per endpoint
 * - Automatic expiration of rate limits
 * - Warning system for approaching limits
 * - Environment-aware debug logging
 */
export class RateLimitHandler {
  private static rateLimitState: RateLimitState = {};

  static isEndpointRateLimited(endpoint: string): boolean {
    const state = this.rateLimitState[endpoint];
    if (!state) return false;

    const now = Date.now();
    const timeSinceLimit = now - state.lastLimitTime;

    if (timeSinceLimit > state.retryAfter * 1000) {
      delete this.rateLimitState[endpoint];
      return false;
    }

    return state.isLimited;
  }

  /**
   * Records a rate limit for an endpoint
   *
   * @param endpoint - The API endpoint that was rate limited
   * @param rateLimitInfo - Rate limit information from the server
   */
  static recordRateLimit(endpoint: string, rateLimitInfo: RateLimitInfo): void {
    if (!rateLimitInfo.isRateLimited) return;

    this.rateLimitState[endpoint] = {
      isLimited: true,
      retryAfter: rateLimitInfo.retryAfter || 60,
      lastLimitTime: Date.now(),
    };

    // DEBUG: Log rate limit recording
    FrontendLogger.warn('Rate limit recorded for endpoint', {
      endpoint,
      retryAfter: rateLimitInfo.retryAfter,
      limit: rateLimitInfo.limit,
      remaining: rateLimitInfo.remaining,
    });
  }

  static getTimeUntilReset(endpoint: string): number {
    const state = this.rateLimitState[endpoint];
    if (!state) return 0;

    const now = Date.now();
    const timeSinceLimit = now - state.lastLimitTime;
    const timeRemaining = state.retryAfter * 1000 - timeSinceLimit;

    return Math.max(0, Math.ceil(timeRemaining / 1000));
  }

  static clearRateLimit(endpoint: string): void {
    delete this.rateLimitState[endpoint];
  }

  static clearAllRateLimits(): void {
    this.rateLimitState = {};
  }

  static getRateLimitedEndpoints(): string[] {
    const now = Date.now();
    const rateLimited: string[] = [];

    for (const [endpoint, state] of Object.entries(this.rateLimitState)) {
      const timeSinceLimit = now - state.lastLimitTime;
      if (timeSinceLimit < state.retryAfter * 1000) {
        rateLimited.push(endpoint);
      } else {
        delete this.rateLimitState[endpoint];
      }
    }
    return rateLimited;
  }

  static getRateLimitMessage(endpoint: string): string {
    const timeRemaining = this.getTimeUntilReset(endpoint);

    if (timeRemaining > 0) {
      const minutes = Math.floor(timeRemaining / 60);
      const seconds = timeRemaining % 60;

      if (minutes > 0) {
        return `Rate limit exceeded. Please try again in ${minutes}m ${seconds}s.`;
      } else {
        return `Rate limit exceeded. Please try again in ${seconds} seconds.`;
      }
    }

    if (endpoint.includes('/auth/')) {
      return 'Too many authentication attempts. Please try again in a few minutes.';
    } else if (endpoint.includes('/password')) {
      return 'Too many password reset attempts. Please try again later.';
    } else if (endpoint.includes('/email')) {
      return 'Too many email verification attempts. Please try again later.';
    } else if (endpoint.includes('/surveys')) {
      return 'Too many survey requests. Please try again in a moment.';
    }

    return 'Rate limit exceeded. Please try again later.';
  }

  static shouldWarnAboutRateLimit(rateLimitInfo?: RateLimitInfo): boolean {
    if (!rateLimitInfo || !rateLimitInfo.limit || !rateLimitInfo.remaining) {
      return false;
    }

    const usagePercentage = (rateLimitInfo.limit - rateLimitInfo.remaining) / rateLimitInfo.limit;
    return usagePercentage >= 0.8;
  }

  static getRateLimitWarning(rateLimitInfo: RateLimitInfo): string {
    if (!rateLimitInfo.remaining || !rateLimitInfo.limit) {
      return 'You are approaching the rate limit. Please slow down your requests.';
    }

    return `You have ${rateLimitInfo.remaining} requests remaining out of ${rateLimitInfo.limit}.`;
  }

  static calculateRetryDelay(attempt: number, baseDelay: number = 1000): number {
    const exponentialDelay = baseDelay * Math.pow(2, attempt);
    const jitter = Math.random() * 1000;
    return Math.min(exponentialDelay + jitter, 30000);
  }
}
