/**
 * Global respondent details options for survey components
 * Used in both create survey and edit survey components
 */

import { SelectOption } from '../interfaces';

/**
 * Enhanced interface for respondent detail options with flexible configuration
 */
export interface RespondentDetailOption {
  value: string; // internal name (e.g., "email")
  label: string; // label shown to user
  inputType: string; // e.g., "text", "dropdown", "email", "radio", "checkbox", "number"
  required?: boolean; // whether the field is required
  placeholder?: string; // optional placeholder text
  options?: SelectOption[]; // only for dropdown/radio/checkbox fields
  defaultValue?: any; // prefilled value if any
}

/**
 * List of accepted input types for respondent details
 */
export const AcceptedInputTypes = [
  { value: 'text', label: 'Text' },
  { value: 'email', label: 'Email' },
  { value: 'dropdown', label: 'Dropdown' },
  { value: 'radio', label: 'Single Choice' },
  { value: 'checkbox', label: 'Multi Choice' },
  { value: 'number', label: 'Number' },
];

/**
 * Legacy respondent details options - kept for backward compatibility
 */
export const RespondentDetailsOptions: RespondentDetailOption[] = [
  { value: '-', label: 'Choose Respondent Details', inputType: 'text' },
  {
    value: 'fullName',
    label: 'Full Name',
    inputType: 'text',
    placeholder: 'Enter your full name',
    required: true,
  },
  {
    value: 'email',
    label: 'Email',
    inputType: 'email',
    placeholder: 'Enter your email address',
    required: true,
  },
  {
    value: 'age',
    label: 'Age',
    inputType: 'dropdown',
    placeholder: 'Select your age range',
    required: true,
    options: [
      { value: 'under_18', label: 'Under 18' },
      { value: '18_24', label: '18-24' },
      { value: '25_34', label: '25-34' },
      { value: '35_44', label: '35-44' },
      { value: '45_54', label: '45-54' },
      { value: '55_64', label: '55-64' },
      { value: '65_74', label: '65-74' },
      { value: '75_plus', label: '75+' },
    ],
  },
  {
    value: 'gender',
    label: 'Gender',
    inputType: 'dropdown',
    placeholder: 'Select your gender',
    required: true,
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'non_binary', label: 'Non-binary' },
      { value: 'other', label: 'Other' },
      { value: 'prefer_not_to_say', label: 'Prefer not to say' },
    ],
  },
  {
    value: 'jobTitle',
    label: 'Job Title / Role',
    inputType: 'text',
    placeholder: 'Enter your job title',
    required: true,
  },
  {
    value: 'seniority',
    label: 'Seniority Level',
    inputType: 'dropdown',
    placeholder: 'Select your seniority level',
    required: true,
    options: [
      { value: 'intern', label: 'Intern / Trainee' },
      { value: 'entry', label: 'Entry Level (0-2 years)' },
      { value: 'junior', label: 'Junior Level (2-4 years)' },
      { value: 'mid', label: 'Mid Level (4-7 years)' },
      { value: 'senior', label: 'Senior Level (7-10 years)' },
      { value: 'lead', label: 'Lead / Principal (10+ years)' },
      { value: 'manager', label: 'Manager / Team Lead' },
      { value: 'senior_manager', label: 'Senior Manager' },
      { value: 'director', label: 'Director' },
      { value: 'vp', label: 'Vice President' },
      { value: 'c_level', label: 'C-Level Executive (CEO, CTO, etc.)' },
      { value: 'founder', label: 'Founder / Owner' },
      { value: 'consultant', label: 'Consultant / Freelancer' },
      { value: 'other', label: 'Other' },
    ],
  },
  {
    value: 'department',
    label: 'Department',
    inputType: 'dropdown',
    placeholder: 'Select your department',
    required: true,
    options: [
      // Technology & Development
      { value: 'engineering', label: 'Engineering & Development' },
      { value: 'product', label: 'Product Management' },
      { value: 'design', label: 'Design & User Experience' },
      { value: 'data', label: 'Data & Analytics' },
      { value: 'it', label: 'IT & Infrastructure' },
      { value: 'security', label: 'Information Security' },
      { value: 'qa', label: 'Quality Assurance & Testing' },

      // Business & Strategy
      { value: 'executive', label: 'Executive & Leadership' },
      { value: 'strategy', label: 'Strategy & Business Development' },
      { value: 'consulting', label: 'Consulting & Advisory' },
      { value: 'project_management', label: 'Project & Program Management' },

      // Sales & Marketing
      { value: 'sales', label: 'Sales & Business Development' },
      { value: 'marketing', label: 'Marketing & Communications' },
      { value: 'customer_success', label: 'Customer Success & Account Management' },
      { value: 'customer_support', label: 'Customer Support & Service' },

      // Operations & Support
      { value: 'operations', label: 'Operations & Process Management' },
      { value: 'supply_chain', label: 'Supply Chain & Logistics' },
      { value: 'procurement', label: 'Procurement & Vendor Management' },
      { value: 'facilities', label: 'Facilities & Administration' },

      // Finance & Legal
      { value: 'finance', label: 'Finance & Accounting' },
      { value: 'legal', label: 'Legal & Compliance' },
      { value: 'risk', label: 'Risk Management & Audit' },

      // People & Culture
      { value: 'hr', label: 'Human Resources & Talent' },
      { value: 'recruiting', label: 'Recruiting & Talent Acquisition' },
      { value: 'learning', label: 'Learning & Development' },

      // Industry-Specific
      { value: 'manufacturing', label: 'Manufacturing & Production' },
      { value: 'research', label: 'Research & Development' },
      { value: 'clinical', label: 'Clinical & Medical Affairs' },
      { value: 'regulatory', label: 'Regulatory Affairs' },
      { value: 'editorial', label: 'Editorial & Content' },

      // Catch-All
      { value: 'other', label: 'Other Department' },
    ],
  },
  {
    value: 'employmentType',
    label: 'Employment Type',
    inputType: 'dropdown',
    placeholder: 'Select your employment type',
    required: true,
    options: [
      // Traditional Employment
      { value: 'full_time', label: 'Full-time Employee' },
      { value: 'part_time', label: 'Part-time Employee' },
      { value: 'temporary', label: 'Temporary Employee' },
      { value: 'seasonal', label: 'Seasonal Employee' },

      // Contract & Freelance
      { value: 'contract', label: 'Contract Worker' },
      { value: 'freelance', label: 'Freelancer / Independent Contractor' },
      { value: 'consultant', label: 'Consultant' },
      { value: 'gig_worker', label: 'Gig Worker' },

      // Learning & Development
      { value: 'intern', label: 'Intern' },
      { value: 'apprentice', label: 'Apprentice / Trainee' },
      { value: 'student_worker', label: 'Student Worker' },

      // Business Ownership
      { value: 'self_employed', label: 'Self-Employed' },
      { value: 'business_owner', label: 'Business Owner / Entrepreneur' },
      { value: 'partner', label: 'Business Partner' },

      // Non-Traditional
      { value: 'volunteer', label: 'Volunteer' },
      { value: 'remote_worker', label: 'Remote Worker (Location Independent)' },
      { value: 'retired', label: 'Retired' },
      { value: 'unemployed', label: 'Unemployed / Job Seeking' },
      { value: 'student', label: 'Student (Not Working)' },
      { value: 'homemaker', label: 'Homemaker / Stay-at-Home Parent' },

      // Catch-All
      { value: 'other', label: 'Other Employment Type' },
    ],
  },
  {
    value: 'organisationName',
    label: 'Organisation Name',
    inputType: 'text',
    placeholder: 'Enter your organization name',
    required: true,
  },
  {
    value: 'organisationSize',
    label: 'Organisation Size',
    inputType: 'dropdown',
    placeholder: 'Select your organization size',
    required: true,
    options: [
      { value: 'solo', label: 'Solo / Just me (1 employee)' },
      { value: '2_10', label: 'Micro business (2-10 employees)' },
      { value: '11_50', label: 'Small business (11-50 employees)' },
      { value: '51_200', label: 'Medium business (51-200 employees)' },
      { value: '201_500', label: 'Large business (201-500 employees)' },
      { value: '501_1000', label: 'Large company (501-1,000 employees)' },
      { value: '1001_5000', label: 'Enterprise (1,001-5,000 employees)' },
      { value: '5001_10000', label: 'Large enterprise (5,001-10,000 employees)' },
      { value: '10001_plus', label: 'Fortune 500 / Global enterprise (10,001+ employees)' },
      { value: 'unknown', label: "Not sure / Don't know" },
    ],
  },
  {
    value: 'industry',
    label: 'Industry',
    inputType: 'dropdown',
    placeholder: 'Select your industry',
    required: true,
    options: [
      // Core Economic Sectors
      { value: 'technology', label: 'Technology & Software' },
      { value: 'healthcare', label: 'Healthcare & Medical Services' },
      { value: 'finance', label: 'Financial Services & Banking' },
      { value: 'education', label: 'Education & Training' },
      { value: 'manufacturing', label: 'Manufacturing & Production' },
      { value: 'retail', label: 'Retail & E-commerce' },
      { value: 'construction', label: 'Construction & Real Estate' },
      { value: 'transportation', label: 'Transportation & Logistics' },
      { value: 'energy', label: 'Energy & Utilities' },
      { value: 'agriculture', label: 'Agriculture & Food Production' },

      // Professional Services
      { value: 'legal', label: 'Legal Services' },
      { value: 'consulting', label: 'Consulting & Business Services' },
      { value: 'accounting', label: 'Accounting & Finance' },
      { value: 'marketing', label: 'Marketing & Advertising' },
      { value: 'engineering', label: 'Engineering & Technical Services' },
      { value: 'architecture', label: 'Architecture & Design' },
      { value: 'research', label: 'Research & Development' },

      // Creative & Media Industries
      { value: 'media', label: 'Media & Entertainment' },
      { value: 'arts', label: 'Arts & Creative Industries' },
      { value: 'publishing', label: 'Publishing & Communications' },
      { value: 'gaming', label: 'Gaming & Digital Entertainment' },

      // Service Industries
      { value: 'hospitality', label: 'Hospitality & Tourism' },
      { value: 'foodService', label: 'Food Service & Restaurants' },
      { value: 'personalCare', label: 'Personal Care & Beauty Services' },
      { value: 'fitness', label: 'Fitness & Wellness' },
      { value: 'repair', label: 'Repair & Maintenance Services' },
      { value: 'security', label: 'Security & Safety Services' },

      // Specialized Industries
      { value: 'automotive', label: 'Automotive Industry' },
      { value: 'aerospace', label: 'Aerospace & Defense' },
      { value: 'pharmaceuticals', label: 'Pharmaceuticals & Biotechnology' },
      { value: 'telecommunications', label: 'Telecommunications' },
      { value: 'insurance', label: 'Insurance' },
      { value: 'mining', label: 'Mining & Natural Resources' },
      { value: 'textiles', label: 'Textiles & Apparel' },
      { value: 'chemicals', label: 'Chemicals & Materials' },

      // Public & Social Sectors
      { value: 'government', label: 'Government & Public Administration' },
      { value: 'military', label: 'Military & Defense' },
      { value: 'nonprofit', label: 'Nonprofit & Social Services' },
      { value: 'religious', label: 'Religious Organizations' },
      { value: 'emergency', label: 'Emergency Services & Public Safety' },

      // Employment Status
      { value: 'student', label: 'Student' },
      { value: 'unemployed', label: 'Unemployed / Job Seeking' },
      { value: 'retired', label: 'Retired' },
      { value: 'selfEmployed', label: 'Self-Employed / Entrepreneur' },
      { value: 'homemaker', label: 'Homemaker / Stay-at-Home Parent' },
      { value: 'partTime', label: 'Part-Time / Temporary Work' },
      { value: 'volunteer', label: 'Volunteer / Community Work' },

      // Catch-All Options
      { value: 'other', label: 'Other Industry' },
      { value: 'preferNotToSay', label: 'Prefer Not to Say' },
    ],
  },
  { value: 'custom', label: '+ Create Custom Detail', inputType: 'text' },
];
